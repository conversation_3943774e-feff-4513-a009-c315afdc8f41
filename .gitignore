# Laravel
/public/hot
/public/storage
public/.DS_Store
/storage/*.key
/vendor
/nbproject
Homestead.json
Homestead.yaml
.phpunit.result.cache
.env.backup

# Vagrant
/.vagrant

# Dependencies
/node_modules

# IDEs and editors
.idea
.vscode
.vs
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
error_log

# Logs
!logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.npm
.eslintcache
.yarn-integrity

# System Files
.DS_Store
.AppleDouble
.LSOverride

# Thumbnails
._*
Thumbs.db
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Windows shortcuts
*.lnk
/.history
*.zip

/tests/
.phpintel/24775332a60567d2b981a5f78b81cb6c
.phpintel/index
.env
