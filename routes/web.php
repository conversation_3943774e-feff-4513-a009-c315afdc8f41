<?php

use App\Models\DrmProduct;
use App\Enums\V2UserAccess;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Models\Marketplace\AdditionalInfo;
use App\Models\Export\MarketplaceProductsExport;
use App\Services\Marketplace\DuplicateEanService;

$router->get('/', function () {
	return 'Welcome !';
});

$router->get('/redis-x45-test', function () {
	$redis = Redis::connection();
	// $redis->set('key', "hello");
	Redis::flushDB();
	dd("done");
	// dd($redis->get('key'));
});

$router->get('/redis-connection-test', function () {
    try {
        $redis = Redis::connection();

        // Test basic connection
        $ping = $redis->ping();

        // Get Redis info
        $info = $redis->info();

        // Test basic operations
        $testKey = 'test_connection_' . time();
        $testValue = 'Redis connection test at ' . date('Y-m-d H:i:s');

        // Set a test value
        $redis->set($testKey, $testValue);

        // Get the test value back
        $retrievedValue = $redis->get($testKey);

        // Test expiration
        $redis->setex('test_expire_' . time(), 10, 'This will expire in 10 seconds');

        // Clean up test key
        $redis->del($testKey);

        // Get some basic stats
        $dbSize = $redis->dbsize();
        $memory = isset($info['used_memory_human']) ? $info['used_memory_human'] : 'N/A';
        $version = isset($info['redis_version']) ? $info['redis_version'] : 'N/A';
        $uptime = isset($info['uptime_in_seconds']) ? $info['uptime_in_seconds'] : 'N/A';

        return response()->json([
            'status' => 'success',
            'message' => 'Redis connection is working properly',
            'connection_test' => [
                'ping' => $ping,
                'test_write_read' => $testValue === $retrievedValue ? 'PASSED' : 'FAILED',
                'test_value_set' => $testValue,
                'test_value_retrieved' => $retrievedValue
            ],
            'redis_info' => [
                'version' => $version,
                'uptime_seconds' => $uptime,
                'database_size' => $dbSize,
                'memory_usage' => $memory,
                'host' => config('database.redis.default.host'),
                'port' => config('database.redis.default.port'),
                'database' => config('database.redis.default.database')
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], 200);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Redis connection failed',
            'error' => $e->getMessage(),
            'config' => [
                'host' => config('database.redis.default.host'),
                'port' => config('database.redis.default.port'),
                'database' => config('database.redis.default.database'),
                'client' => config('database.redis.client')
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ], 500);
    }
});

$router->get('color-change', function () {
	$drmProductsMpIds = \App\Models\DrmProduct::where('marketplace_product_id', '>', 0)->pluck('marketplace_product_id')->toArray();
	$drmProductsMpIds = array_unique($drmProductsMpIds);

	$bikeApiProducts  = \App\Models\Marketplace\Product::whereIn('id', $drmProductsMpIds)
						->orderBy('id', 'desc')
						->get();

	foreach ($bikeApiProducts as $product) {

		$attributes = [
			'item_color' => $product->item_color ?? '',
			'materials'	 => $product->materials ?? '',
		];

		$finalAttributes = [];

		if ($attributes['item_color'] == true || $attributes['materials'] == true) {
			if ($attributes['item_color'])  $finalAttributes['item_color'] = $attributes['item_color'];
			if ($attributes['materials'])  $finalAttributes['materials'] = $attributes['materials'];

			$drmProducts = \App\Models\DrmProduct::where('marketplace_product_id', $product->id);
			$drmProducts->update($finalAttributes);

		} else {
			continue;
		}
	}
});

$router->get('change-color-in-de', 'Marketplace\BikeApiController@changeColorInDe');

$router->get('count-drm-products', function () {
	dd( ini_get('MAX_EXECUTION_TIME') );
});

$router->get('test-bdroppy-product', 'Marketplace\BDroppyApiController@testProductSync');

$router->get('test-bdroppy-oldproduct-update', 'Marketplace\BDroppyApiController@oldProductUpdate');
// $router->get('test-b2buhren-product-delete-withdrm', 'Marketplace\B2bUhrenApiController@deleteProductWithDrmProduct');
// $router->get('b2buhren-product-update-by-csv', 'Marketplace\B2bUhrenApiController@updateStockByCsv');
// $router->get('get-all-b2buhren-products', 'Marketplace\B2bUhrenApiController@getAllB2bProducts');

$router->get('check-bike-apidata','Marketplace\ProductController@checkBikeApiData');


$router->group(['prefix'=>'testroute', 'as'=>'bdroptestroutepy.'], function ($router) {
    $router->get('b2brhren-brand-sync', 'Marketplace\TestController@brandSync');
    $router->get('b2buhren-product-insert', 'Marketplace\TestController@buildProductSyncJobs');

    $router->get('bdroppy-import-catelog-list', 'Marketplace\TestController@getCatelogList');
    $router->get('bdroppy-import-categories', 'Marketplace\TestController@getCategories');
    $router->get('bdroppy-product-insert', 'Marketplace\TestController@testProductSync');


    // Bike API routes
    $router->get('bike-api-import-products', 'Marketplace\TestController@bikeApiImportProducts');
});

$router->get('collection-product-sync','Marketplace\CollectionController@collectionProductSync');
$router->get('compare-mp-product-to-drm-product','Marketplace\ProductController@compareMPPoroductToDrmProduct');

$router->get('get-product-variation','Marketplace\BigBuyApiController@getProductVariation');

$router->get('get-product-variation-stock','Marketplace\BigBuyApiController@getProductVariationStock');

$router->get('get-product-images','Marketplace\BigBuyApiController@getProductImages');

$router->get('get-product-category','Marketplace\BigBuyApiController@getProductCategory');

$router->get('get-product-information','Marketplace\BigBuyApiController@getProductInformation');

$router->get('get-product-shippingcosts','Marketplace\BigBuyApiController@getProductShippingCosts');

$router->get('get-product-brand','Marketplace\BigBuyApiController@getProductBrand');

$router->get('update-bigbuy-product','Marketplace\BigBuyApiController@updateBigbuyProduct');
$router->get('insert-bigbuy-product-new','Marketplace\BigBuyApiController@bigbuyProductInsertNew');
// bigbuy variant product insert
$router->get('insert-bigbuy-variant-product-new','Marketplace\BigBuyApiController@bigbuyVariantProductInsertNew');
$router->get('stock-update-bigbuy-variant-product-new','Marketplace\BigBuyApiController@bigbuyVarientProductStockSync');
$router->get('bigbuy-variant-product-price-sync','Marketplace\BigBuyApiController@bigbuyVarientProductPriceSync');

$router->get('cornot-product-check','Marketplace\CollectionController@cornotProductCheck');

$router->get('order-check/{drmOrder}','Marketplace\BigBuyApiController@orderBigBuyProduct');

$router->get('supplier-monthly-report-create','Marketplace\ProductController@createMonthlyReport');

$router->get('get-allproduct','Marketplace\BigBuyApiController@checkTotalProduct');
$router->get('sync-bigbuy-tracking','Marketplace\BigBuyApiController@syncTracking');
$router->get('bigbuy-shipping-cost-update','Marketplace\BigBuyApiController@updateProductShippingCost');
$router->get('bigbuy-product-by-id/{product_id}','Marketplace\BigBuyApiController@getProductById');
$router->get('bigbuy-ch-shipping-cost-update','Marketplace\BigBuyApiController@updateProductChShippingCost');

// Bike api

$router->get('bikeapi-stock-update-from-csv','Marketplace\ProductController@bikeApiStockUpdateFromCsvData');
$router->get('bikeapi-category-check','Marketplace\ProductController@bikeApiCategoryCheck');
$router->get('check-bikeapi-product-available','Marketplace\ProductController@checkApiProductAvailableOnSystem');
$router->get('bikeapi-missing-product','Marketplace\ProductController@checkAMissingApiProduct');
$router->get('bikeapi-product-that-has-not-api','Marketplace\ProductController@bikeApiProductThatHasNotApi');

$router->get('bikeapi-genarate-report','Marketplace\ProductController@genarateBikeApiReport');
$router->get('bikeapi-report','Marketplace\ProductController@showReport');
$router->get('bikeapi-stock-stync','Marketplace\ProductController@stockSyncBikeApi');
$router->get('bikeapi-missing-product-sync','Marketplace\ProductController@bikeApiStockZeroMissingProduct');
$router->get('bikeapi-price-update-by-csv','Marketplace\ProductController@bikeApiPriceUpdateByCsv');
$router->get('manually-transfer-product/{user_id}/{category_id}','Marketplace\ProductController@transferProductToDrmManually');

$router->get('transfer-product/{user_id}/{category_id}', function () {
	$subscriptions = \App\Models\Marketplace\AutoTransferSubscription::where('end_date', '>=', \Carbon\Carbon::now())->where('status', 1)->get();

	foreach ($subscriptions as $subscription) {
	    $userId         = $subscription->user_id;
	    $categoryId     = $subscription->category_id;
		app(\App\Http\Controllers\Marketplace\ProductController::class)->transferProductsInstantly($userId, $categoryId);

	}

});


$router->get('sync-bigbuy-product-stock','Marketplace\BigBuyApiController@bigbuyProductStockSync');
$router->get('sync-bigbuy-product-price','Marketplace\BigBuyApiController@bigbuyProductPriceSync');
$router->get('update-product-category-id','Marketplace\BigBuyApiController@updateProductApiCategoryId');
$router->get('mapping-category-to-mpcategory-map','Marketplace\BigBuyApiController@categoryMappingWithMpProduct');
$router->get('update-product-brand','Marketplace\BigBuyApiController@updateBrand');
$router->get('update-bigbuy-vat','Marketplace\BigBuyApiController@updateBigBuyVat');
$router->get('update-bigbuy-drm-tax-type','Marketplace\BigBuyApiController@updateDrmTaxType');

$router->get('duplicate-ean-service-check', function () {

	$productId = request()->product_id;
	$ProductEan = request()->product_ean;
	$service  = new DuplicateEanService();

	if($productId){
		$service->stockUpdateToActiveBest($productId);
	}else if($ProductEan){
		$service->duplicateEanCheck($ProductEan);
	}
});

$router->post('sms-send', 'Marketplace\SmsController@sendSms');

$router->get('b2buhren-apicategory-column-update',function(){
    Product::where('api_id',2)->get()->each(function($product){
        $product->api_category_id = $product->brand;
        $product->save();
    });
});


$router->get('products-image-uploade',function(){

	$categoryId = request()->category_id;
	// $mpProducts = new Product();
    $mpProducts = Product::where('category_id',$categoryId)
				->whereNotNull('image')
				->where('image','!=' ,'[]')
				->where('image','!=' ,'null')
				->select('id','image','old_images','is_image_process','ean')->take(500)->get();
	foreach($mpProducts->chunk(100) as $products){
		if(request()->permission == 'true'){
			dispatch(new \App\Jobs\Marketplace\UrlImageUploadCloud($products));
		}else{
			dd('product',$products);
		}
	}
});

$router->get('product-shipping-cost-update',function(){
	return app(\App\Services\Marketplace\BikeApi\BikeApiService::class)->shippingCostUpdateByCsv();
});

$router->get('check-mp-to-drm-product-changes','Marketplace\ProductController@checkMpToDrmProductChanges');

$router->get('product-transfer-drm-by-csv',function(){
	$path = "https://drm-file.fra1.digitaloceanspaces.com/marketplace-collections/2570/98e7dbcabc4f7b1030686ff87b4a24ef.xlsx";
	$type = pathinfo($path, PATHINFO_EXTENSION);
	$csv_array  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($path, $type, 'auto', false);
	
	$conn = new Product();
	$products = $conn->setConnection('drm_team')->whereIn('ean',array_column($csv_array,'ean'))->where('status',1)->get();
	
	foreach ($products as $key => $product) {
		app(\App\Http\Controllers\Marketplace\ProductController::class)->transferAllFilteredProductsToDrm ($product->id, 62, $product->category_id);
		dd($product);
	}
	
});

$router->get('update-existing-product-additioninfo',function(){
dd('trun on this function first');
	$updateCsv = 'https://drm-team.fra1.digitaloceanspaces.com/marketplace-collections/71/d9cc568fe6ffc7a6dfb01aff99f885f6.xls';
    $type = pathinfo($updateCsv, PATHINFO_EXTENSION);
	$rows = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($updateCsv, $type, 'auto', false);
	$matchEan = array_filter(array_column($rows, 'EAN'));
	// $mpProduct = new Product();
	$allProducts = Product::select('id','ean')->whereIn('ean',$matchEan)->where('shipping_method',2)->get();
	
	if(request()->permission == 'true'){
		foreach($allProducts as $product){
			try{
				foreach ($rows as $k => $v) {
					$csv_ean = trim($v["EAN"]);

					if ($csv_ean === trim($product->ean)) {
						$additionalInfo = [];

						$dimensions =  explode('x',$rows[$k]['Dimensions']);
						if(count($dimensions) > 1 ){
							$product_length = ((empty($dimensions[2]) ? 0 : $dimensions[2]) /10);
							$product_width  = ((empty($dimensions[1]) ? 0 : $dimensions[1]) /10);
							$product_height = ((empty($dimensions[0]) ? 0 : $dimensions[0]) /10);
							$volume = (($product_length * $product_width * $product_height) / 1000000);

							$additionalInfo['product_length'] =  $product_length;
							$additionalInfo['product_width']  =  $product_width;
							$additionalInfo['product_height'] =  $product_height;
							$additionalInfo['item_unit']      =  'Gram';
							$additionalInfo['volume'] = number_format($volume, 6);
						}
						$item_weight = $rows[$k]['Weight [g]'] ?? 0;
						$additionalInfo['product_id'] =  $product->id;
						$product->where('id',$product->id)->update(['item_weight'=>$item_weight]);
						// $addInfo = new AdditionalInfo();
						AdditionalInfo::create($additionalInfo);
						dump('Product Update Done',$product->ean);
					}
				}
			}catch(\Exception $e){
			  var_dump('exception',$e->getMessage(),'Product ean',$product->ean);
			}
		}
	}else{
		dd('Total product match',$allProducts);
	}
	dd('Product Update complete');
});

$router->get('product-stock-update-by-id',function(){

	if(isset($_GET['token']) && $_GET['token'] == "mpupdate" && !empty($_GET['product_id'])){
		$product = Product::with('drmProducts')
					->where('id',$_GET['product_id'])
					->select('id','ean','shipping_method','stock','internel_stock')
					->first();
		if($product){
			$stock = $product->shipping_method == 1 ? $product->stock : $product->internel_stock;
			$drm_products = $product->drmProducts;
			if(count($drm_products) > 0){
				$data['stock'] = $stock;
				app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
				info("DRM product sync-".$product->ean);
			}
		}else{
			dd('product not found');
		}
	}else{
		dd('token not match or product id not found');
	}
	
});

$router->get('full-product-stock-update-by-id',function(){

	if(isset($_GET['token']) && $_GET['token'] == "mpupdate" && !empty($_GET['product_id']) && !empty($_GET['stock'])){
		$product = Product::with(
			'drmProducts',
			         'additionalInfo:product_id,product_length,product_width,product_height'
			        )
					->where('id',$_GET['product_id'])
					->select('id','ean','internel_stock','old_internel_stock','internel_stock_updated_at')
					->first();
		if($product){

			$old_stock = $product->internel_stock ? $product->internel_stock : 0;
			$new_stock = ($old_stock + $_GET['stock']);

			$product->internel_stock             = $new_stock;
			$product->old_internel_stock         = $old_stock;
			$product->internel_stock_updated_at  = \Carbon\Carbon::now();
			
			$drm_products = $product->drmProducts;
			if(count($drm_products) > 0){
				$data['stock'] = $new_stock;
				app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
				info("DRM product sync-".$product->ean);
			}
			if ($product->additionalInfo) {
				$volume = (($product->additionalInfo->product_length ?? 0) * ($product->additionalInfo->product_width ?? 0) * ($product->additionalInfo->product_height ?? 0)) / 1000000;
				$newCubicMeter = $volume * $new_stock;
				$product->cubic_meters = $newCubicMeter;
			}
			$product->update();
		}else{
			dd('product not found');
		}
	}else{
		dd('token not match or product id not found');
	}
	
});

$router->get('update-plush-send-products',function(){
	app(\App\Http\Controllers\Marketplace\PlushController::class)->plushSendProductUpdate();
});

$router->get('increse-mp-product-uvp',function(){
	
	if(!empty($_GET['token']) && $_GET['token'] == 'mpuvpincrease'){
		app(\App\Http\Controllers\Marketplace\ProductController::class)->allAPIProductsUVPincrease();
	}else{
		dd('token not match');
	}
	
});

$router->get('plush-product-info-update', function () {
	if (!empty($_GET['token']) && $_GET['token'] == 'plushInfo') {
		$delivery_company_id = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;
		$csv_url = "https://drm-team.fra1.digitaloceanspaces.com/marketplace-collections/71/38b74d87c9d802c0ce6ca17bfe850cd5.xlsx";
		$rows = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($csv_url, 'xlsx', 'auto', false);
		app(\App\Http\Controllers\Marketplace\PlushController::class)->plushProductAdditionalInfoUpdate($delivery_company_id, $rows);
	} else {
		dd('token not match');
	}
});

// $router->get('products-iamge-update',function(){
// 	Log::info("Product Image upload scheduled ran!");
//     $mpProducts = Product::with('drmProducts')->where('is_image_process', 0)->select('id', 'image')->take(250)->get();
// 	foreach ($mpProducts->chunk(50) as $products) {
// 		if (request()->permission == 'true') {
// 			dispatch(new \App\Jobs\Marketplace\UrlImageUploadCloud($products));
// 		} else {
// 			dd('product', $products);
// 		}
// 	}
// });

$router->get('drm-product-price-update',function(){
	dd("when needed");
	$offset = $_GET['offset'] ?? 0;
	$limit  = $_GET['limit'] ?? 1000;
	$user_id = 3515;
	$drm_products = DrmProduct::where('user_id',$user_id)
					->whereNotNull('marketplace_product_id')
					->where('marketplace_product_id', '<>', '')
					->offset($offset)
					->take($limit)
					->pluck('marketplace_product_id')
					->toArray();
					
	$mp_products = Product::with('drmProducts')->whereIn('id',$drm_products)->get();

	foreach($mp_products as $mp_product){
		$data = [];
		$drm_products = $mp_product->drmProducts;
		if(count($drm_products) > 0){
			$data['vk_price'] = $mp_product->vk_price;
			$data['stock'] = $mp_product->stock;
			app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
			info("product sync - ".$mp_product->ean);
		}
	}

	dd("finised");
	
});

$router->get('plush-return-label-check',function(){
	$csv_url = 'https://drm-team.fra1.digitaloceanspaces.com/marketplace-collections/71/49c5fe0d0e06a54d9caf79581dff8ecb.xlsx';

	$filterReturnLabelProducts = array_filter(app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($csv_url, 'xlsx', 'auto', false), function ($product) {
		return ($product['EAN'] != null);
	});
	$eans = array_column($filterReturnLabelProducts, 'retournierte_menge', 'EAN');

	$query = Product::with([
		'additionalInfo:product_id,product_length,product_width,product_height,volume',
		'stockSendComment:id,mp_product_id,send_stock_comment'
	])
	->where('marketplace_products.shipping_method',1)
	->whereIn('marketplace_products.ean',array_keys($eans));

	// Select all columns for cloning
	$products = $query->select('marketplace_products.*')->get();

	if (!empty($_GET['token']) && $_GET['token'] == 'plushInfo') {

		foreach($products as $product){
			app(\App\Http\Controllers\Marketplace\PlushController::class)->plushDuplicateProductsProcess($product,$eans);
			dump('dropshipping-product',$product->ean);
		}
	} else {
		dd('total return label products',$products);
	}
});

$router->get('manualy-drm-product-update-by-mp',function(){
	dd("when needed");
	$products = Product::with('drmProducts')
				->where('status',1)
				->select('id','vk_price','ean')
				->offset(450000)
				->take(50000)
				->get();
				dd($products);
	foreach($products as $product){
		$data = [];
		$drm_products = $product->drmProducts;
		if(count($drm_products) > 0){
			$data['vk_price'] = $product->vk_price;
			app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
			info("product sync - ".$product->ean);
		}
	}

	dd("when needed");
});

$router->get('mp-to-drm-product-transfer-manually',function(){

	ini_set('max_execution_time', '0');
    $poduct_csv = 'https://t3735942.p.clickup-attachments.com/t3735942/1ffad856-5c68-4789-96dd-39f38eb29749/bigbuy_shipping_cost_list.csv';
    $type = pathinfo($poduct_csv, PATHINFO_EXTENSION);
	$csv_array  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($poduct_csv, $type, 'auto', true);
	foreach ($csv_array as &$row) {
		$row = array_map(function ($value) {
			return str_replace(['+ACI-', '-'], ['', '_'], $value);
		}, $row);
	}
	unset($row);
    $eans =  array_filter(array_column($csv_array, '+ACI-item+AF8-number+ACI-'));

	$mp_ids = [];
	foreach(array_chunk($eans, 10000) as $ean){
		$products 	= Product::where('status',1)->where('api_id', 4)->whereIn('item_number',array_unique($ean))->pluck('id')->toArray();
		$drmProducts = \App\Models\DrmProduct::where('user_id', 3420)->whereNotNull('marketplace_product_id')->pluck('marketplace_product_id')->toArray();
		$transferable_product_ids = array_diff($products, $drmProducts);
	
		if(request()->permission == 'true'){
			foreach(array_chunk($transferable_product_ids, 500) as $product_ids){
				$productIds = implode(",",$product_ids);
				$userId 	= 3420;
				$autoTransferUrl = "https://drm.software/api/backup-products-mp-to-drm-transfer/$productIds/$userId"; //Live url
				$autoTransferProducts = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($autoTransferUrl);
				dump($autoTransferProducts);
				sleep(1);
				info("transfered products to drm");
			}
		}
		$mp_ids[] = $transferable_product_ids;
	}
	dd($mp_ids);
	
});

$router->get('drm-product-shipping-cost-increase-update',function(){
	$offset = $_GET['offset'] ?? 0;
	$limit  = $_GET['limit'] ?? 1000;
	$products = Product::with('drmProducts')
			// ->where('status',1)
			->where('delivery_company_id', 50303)
			->select('id','shipping_cost','ean')
			->offset($offset)
			->take($limit)
			->get();

	foreach($products as $product){
		$data = [];
		$drm_products = $product->drmProducts;
		if(count($drm_products) > 0){
			$data['shipping_cost'] = $product->shipping_cost;
			app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
			info("Shipping-cost-increase on products ". $product->ean);
		}
	}
});

$router->get('selected-ean-to-product-csv-download',function(){
    ini_set('max_execution_time', '0');
    $poduct_csv = 'https://t3735942.p.clickup-attachments.com/t3735942/76dae0f7-3f82-42a8-a4fd-fec1caf30529/ean_export%20(4)(1).csv';
    $type = pathinfo($poduct_csv, PATHINFO_EXTENSION);
	$csv_array  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($poduct_csv, $type, 'auto', false);
    $csv_ean =  array_filter(array_column($csv_array, 'ean'));

	if(request()->permission == 'true'){
        $products = Product::whereIn('ean', $csv_ean)
			->where(function($query) {
				$query->where([
					['shipping_method', 1],
					['stock', '>', 0]
				])->orWhere([
					['shipping_method', 2],
					['internel_stock', '>', 0]
				]);
			})
			->select('ean', 'name', 'stock', 'internel_stock')
			->get();

		$headings = array_keys(array_diff($products->first()->toArray(), ['internel_stock']));
        return Excel::download(new MarketplaceProductsExport($products, $headings, false), 'schmuck_category_product_list.csv');
	}else{
		dd('Total Requested EAN:: ', count($csv_ean));
	}

});


$router->get('selected-api-product-csv-download',function(){
    ini_set('max_execution_time', '0');

	if(request()->permission == 'true'){
        $products = Product::where('api_id', request()->api_id)
			->where('stock', '>=', 1)
			->where('ek_price', '>=', 50)
			->select('ean', 'item_number', 'ek_price', 'shipping_cost', 'stock')
			->get();

		$headings = array_keys($products->first()->toArray());
        return Excel::download(new MarketplaceProductsExport($products, $headings, false), 'bigbuy_api_product_list.csv');
	}else{
		dd('API id required');
	}

});

$router->get('vidaxl-ch-price-update','\App\Http\Controllers\Marketplace\VidaXlApiController@chPriceSync');
$router->get('bigbuy-imhandel-update',function(){

	dd("for testing");
	// $parent_cats = DB::table('marketplace_parent_categories')->where('im_handel', '>', 0)->get();
	// foreach($parent_cats as $parennt){
	// 	DB::table('marketplace_categories')->where('parent_id', $parennt->id)->update(['im_handel' => $parennt->im_handel]);
	// }
	// dd("finised");
	$offset = $_GET['offset'] ?? 0;
	$limit  = $_GET['limit'] ?? 5;
	$local_category_im = DB::table('marketplace_categories')->pluck('im_handel','id')->toArray();
	$products = Product::where('api_id', 4)
						->where('category_id', '<>', 68)
						->select('id','category_id','ean','vk_price','im_handel')
						->offset($offset)
						->take($limit)
						->get();

	foreach($products as $product){

		if(isset($local_category_im[$product->category_id]) && $local_category_im[$product->category_id] > 0){
			$im_handel =  $product->vk_price * (1 + $local_category_im[$product->category_id] / 100) ?? 0;
			if($product->im_handel !=  number_format($im_handel, 2)){
				$product->im_handel = $im_handel;
				$product->update();
				info("IM-Handel price update on product - ".$product->ean);
			}else{
				info("IM-Handel price not update - ".$product->ean);
			}
		}

	}
	dd("finised");
			
});

// this route for manual v2 product update
$router->get('mp-to-drm-v2-stock-update',function(){

	$country_id = $_GET['coountry'] ?? 1;
	$user_id = [3878,2454,3675,3987,4146,3417,4173];
	$offset = $_GET['offset'] ?? 0;
	$limit  = $_GET['limit'] ?? 5;

	$drmProductss = DB::connection('drm_core')->table('drm_products_new')
					->join('drm_product_marketplace', 'drm_product_marketplace.drm_product_id', '=', 'drm_products_new.id')
					->join('drm_product_stock', 'drm_product_stock.drm_product_id', '=', 'drm_products_new.id')
					->join('drm_product_price', 'drm_product_price.drm_product_id', '=', 'drm_products_new.id')
					->whereIn('drm_products_new.user_id', $user_id)
					->where('drm_products_new.country_id', $country_id)
					->offset($offset)
					->limit($limit)
					->get();


	$mp_products = Product::whereIn('id', $drmProductss->pluck('marketplace_product_id'))
						->select('id','ean','stock','vk_price')
						->get();

	

	foreach($drmProductss->chunk(3000) as $drmProducts) {
		$product_sync_data = [];
		foreach($drmProducts as $drmProduct){
		
			$mp_local = $mp_products->where('id',$drmProduct->marketplace_product_id)->first();
	
			if ($mp_local) {
				$updateableColumns = [];
				$mp_vk_price = $mp_local->vk_price;
	
				$mp_price_markup_discount = !blank($drmProduct->mp_price_markup) ? ($drmProduct->mp_price_markup * $mp_vk_price) / 100 : 0;
	
				$cat_offer_discount = !blank($drmProduct->mp_category_offer) ? ($drmProduct->mp_category_offer * $mp_vk_price) / 100 : 0;
	
				$mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);
				
				if (round($drmProduct->ek_price,2) != number_format($mp_new_vk_price * 100, 0, '', '') || $drmProduct->stock != $mp_local->stock) {
	
					$updateableColumns['ek_price'] = $mp_new_vk_price;
					$updateableColumns['stock'] = $mp_local->stock;
	
					$product_sync_data[] = [
						'marketplace_product_id' => $drmProduct->marketplace_product_id,
						'user_id'  => $drmProduct->user_id,
						'country_id' => $country_id,
						'status' => 1,
						'metadata' => json_encode($updateableColumns),
						'created_at' => \Carbon\Carbon::now(),
						'updated_at' => \Carbon\Carbon::now(),
					];
				}
			}
	
		}
	
		if(!blank($product_sync_data)) {
			DB::table('mp_product_sync_histories')->insert($product_sync_data);
			$product_sync_data = [];
		}
	}
	
	dd($offset);

});

$router->get('bigbuy-name-description-update',function(){
	dd("test");
	app(\App\Http\Controllers\Marketplace\BigBuyApiController::class)->updateNameDescription();
});

$router->get('hs-code-update-for-vidaxl', function () {
    try {
        ini_set('max_execution_time', '0');
        
        // Fetch CSV file and process rows
        $url = 'https://transport.productsup.io/80e1fa0cd9a6c8a76fd6/channel/236922/vidaXL_b2b_cn_code.csv';
		$csv_string = file_get_contents($url);
        $rows =  collect(app(\App\Services\Marketplace\Vidaxl\StockSyncProcess::class)->generateArray($csv_string))->toArray();
		
		$csCodes = array_combine(
			array_filter(array_column($rows, 'sku')),
			array_column($rows, 'CN code')
		);
		
        if (empty($csCodes)) {
            return response()->json(['message' => 'No valid SKUs found in the CSV file.'], 400);
        }

        if (request()->permission === 'true') {
            $updatedCount = 0;
			$offset = request()->offset ?? 0;
			$limit  = request()->limit ?? 10000;
			$csCodes = array_slice($csCodes, $offset, $limit, true);

			collect($csCodes)
                ->chunk(5000)
                ->each(function ($chunk) use (&$updatedCount) {
                    Product::with('additionalInfo:product_id,custom_tariff_number')
                        ->where('api_id', 5)
                        ->whereIn('item_number', array_keys($chunk->toArray()))
                        ->select('id', 'item_number')
                        ->cursor()
                        ->each(function ($product) use ($chunk, &$updatedCount) {
                            $product->additionalInfo()->updateOrCreate(
                                ['product_id' => $product->id],
                                ['custom_tariff_number' => $chunk[$product->item_number]]
                            );
                            $updatedCount++;
                        });
                });

            return response()->json([
                'message' => 'HS codes successfully updated for matching products.',
                'updated_count' => $updatedCount,
            ], 200);
        } else {
            return response()->json(['total_matched_products' => count($csCodes)], 200);
        }
    } catch (Exception $e) {
        Log::error('Error updating HS codes: ' . $e->getMessage());
        return response()->json(['message' => 'An error occurred while processing.'], 500);
    }
});

$router->get('energy_efficency_supplier_adding_for_vidaxl', function () {
    try {
		ini_set('max_execution_time', '0');
	
		// Fetch the CSV file content
		$url = 'https://transport.productsup.io/80e1fa0cd9a6c8a76fd6/channel/492689/b2b_vidaXL_Energy_class.csv';
		$csv_string = file_get_contents($url);	
		$rows = collect(app(\App\Services\Marketplace\Vidaxl\StockSyncProcess::class)->generateArray($csv_string));

		$item_numbers = $rows->pluck('sku')->toArray();
		$api_energy_labels = $rows->pluck('API_energy_label', 'sku')->toArray();
		$api_doc_energy_labels = $rows->pluck('api_doc_energy-label_1', 'sku')->toArray();
	
		if (!request()->boolean('permission')) {
			return response()->json(['error' => 'Permission denied.'], 403);
		}
	
		$updatedCount = 0;
		Product::with('additionalInfo:product_id,energy_efficiency_class,energy_label')
			->where('api_id', 5)
			->whereIn('item_number', $item_numbers)
			->select('id', 'item_number', 'image')
			->cursor()
			->each(function ($product) use ($api_energy_labels, $api_doc_energy_labels, &$updatedCount) {
				$energy_label = $api_energy_labels[$product->item_number];
				$doc_energy_label = $api_doc_energy_labels[$product->item_number];
				$electrical_appliance = ($energy_label && $doc_energy_label) ? 1 : 0;

				$product->additionalInfo()->updateOrCreate(
					['product_id' => $product->id],
					[
						'electrical_appliance' => $electrical_appliance,
						'energy_efficiency_class' => $energy_label,
						'energy_label' => [$doc_energy_label]
					]
				);
	
				$updatedCount++;
			});
	
		info('Energy labels updated successfully', ['updated_count' => $updatedCount]);
		return response()->json([
			'message' => 'Energy label successfully updated for matching products.',
			'updated_count' => $updatedCount,
		], 200);
	
	} catch (\Exception $e) {
		return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
	}
	
});

$router->get('bigbuy-hs-code-update', function () {
	$offset = request()->offset ?? 0;
	$limit  = request()->limit ?? 100;
	// $api_products 	  = app(App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->getParentProducts();
	// $api_products_arr = collect($api_products)->pluck('manufacturer', 'id')->toArray();

	// if (isset($api_products_arr) && Redis::connection()) {
	// 	info('Redis .............................');
	// 	Redis::set('bigbuy_product_id_hs', json_encode($api_products_arr));
	// };

	$api_products_arr =  json_decode(Redis::get('bigbuy_product_id_hs'), true);
	$local_products = Product::with('additionalInfo:product_id,custom_tariff_number','drmProducts', 'drmProductsV2')
			->where('api_id', 4)
			->offset($offset)
			->limit($limit)
			->select('id', 'api_product_id','ean')
			->get();

	$product_sync_data = [];
	$additional_info_data = [];
	foreach ($local_products as $local_product) {

		if(isset($api_products_arr[$local_product->api_product_id])){

			$custom_tariff_number = $api_products_arr[$local_product->api_product_id];
			$additionalInfo = $local_product->additionalInfo;

			if ($additionalInfo) {
				$additionalInfo->custom_tariff_number = $custom_tariff_number;
				$additionalInfo->update();
			} else {
				$additional_info_data[] = [
					'product_id' => $local_product->id,
					'custom_tariff_number' => $custom_tariff_number,
					'created_at' => \Carbon\Carbon::now(),
					'updated_at' => \Carbon\Carbon::now(),
				];
			}

			$drm_products = $local_product->drmProducts;
			$updateableColumns['custom_tariff_number'] = $custom_tariff_number;
			if (count($drm_products) > 0) {
				app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $updateableColumns);
			}

			$drm_productsV2 = $local_product->drmProductsV2;
			if ($drm_productsV2->isNotEmpty()) {
				
				foreach ($drm_productsV2 as $drm_product_v2) {

					if (in_array($drm_product_v2->user_id, V2UserAccess::USERS)) {
						$product_sync_data[] = [
							'marketplace_product_id' => $drm_product_v2->marketplace_product_id,
							'user_id'  => $drm_product_v2->user_id,
							'country_id' => $drm_product_v2->country_id,
							'metadata' => json_encode($updateableColumns),
							'status' => 1,
							'created_at' => \Carbon\Carbon::now(),
							'updated_at' => \Carbon\Carbon::now(),
						];
					}
				}
				info("product tariff -" . $local_product->ean);
			}
		}
	}
	
	// Bulk Insert into AdditionalInfo
	if (!empty($additional_info_data)) {
		DB::table('mp_product_additional_info')->insert($additional_info_data);
	}
	if (!blank($product_sync_data)) DB::table('mp_product_sync_histories')->insert($product_sync_data);
	dd("done");
});

$router->get('vidaxl-hs-code-update', function () {
	ini_set('max_execution_time', '0');
        
	$url = 'https://transport.productsup.io/80e1fa0cd9a6c8a76fd6/channel/236922/vidaXL_b2b_cn_code.csv';
	$csv_string = file_get_contents($url);
	$rows =  collect(app(\App\Services\Marketplace\Vidaxl\StockSyncProcess::class)->generateArray($csv_string))->toArray();
	
	$csCodes = array_combine(
		array_filter(array_column($rows, 'sku')),
		array_column($rows, 'CN code')
	);
	
	if (empty($csCodes)) {
		return response()->json(['message' => 'No valid SKUs found in the CSV file.'], 400);
	}

	$offset = request()->offset ?? 0;
	$limit  = request()->limit ?? 100;

	$local_products = Product::with('additionalInfo:product_id,custom_tariff_number','drmProducts','drmProductsV2')
			->where('api_id', 5)
			->offset($offset)
			->limit($limit)
			->select('id', 'item_number')
			->get();

	$product_sync_data = [];
	$additional_info_data = [];
	foreach ($local_products as $local_product) {

		if(isset($csCodes[$local_product->item_number])){

			$custom_tariff_number = $csCodes[$local_product->item_number];
			$additionalInfo = $local_product->additionalInfo;

			if ($additionalInfo) {
				$additionalInfo->custom_tariff_number = $custom_tariff_number;
				$additionalInfo->update();
			} else {
				$additional_info_data[] = [
					'product_id' => $local_product->id,
					'custom_tariff_number' => $custom_tariff_number,
					'created_at' => \Carbon\Carbon::now(),
					'updated_at' => \Carbon\Carbon::now(),
				];
			}

			$drm_products = $local_product->drmProducts;
			$updateableColumns['custom_tariff_number'] = $custom_tariff_number;
			if (count($drm_products) > 0) {
				app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $updateableColumns);
			}

			$drm_productsV2 = $local_product->drmProductsV2;
			if ($drm_productsV2->isNotEmpty()) {
				
				foreach ($drm_productsV2 as $drm_product_v2) {

					if (in_array($drm_product_v2->user_id, V2UserAccess::USERS)) {
						$product_sync_data[] = [
							'marketplace_product_id' => $drm_product_v2->marketplace_product_id,
							'user_id'  => $drm_product_v2->user_id,
							'country_id' => $drm_product_v2->country_id,
							'metadata' => json_encode($updateableColumns),
							'status' => 1,
							'created_at' => \Carbon\Carbon::now(),
							'updated_at' => \Carbon\Carbon::now(),
						];
					}
				}
				info("product tariff -" . $local_product->item_number);
			}
		}
	}
	
	// Bulk Insert into AdditionalInfo
	if (!empty($additional_info_data)) {
		DB::table('mp_product_additional_info')->insert($additional_info_data);
	}
	if (!blank($product_sync_data)) DB::table('mp_product_sync_histories')->insert($product_sync_data);
	dd("done");
});


$router->get('manualy-brand-update-everprise-api',function(){

	$offset = $_GET['offset'] ?? 0;
	$limit  = $_GET['limit'] ?? 5;
	Product::with(['drmProducts', 'drmProductsV2'])
            ->where('api_id', 8)
            ->where('status', 1)
	    ->where('brand',0)
            ->select('id', 'ean', 'item_number', 'brand')
            //->offset($offset)
            //->limit($limit)
            ->chunkById(100, function ($products) {
                $product_sync_data = [];

                foreach ($products as $local_product) {
                    $updateableColumns = [];
                    $updateableColumnsV2 = [];

                    // DRM v1
                    foreach ($local_product->drmProducts as $drm_product) {
                            $drmBrand = DB::connection('drm_core')
                                ->table('dropmatix_product_brands')
                                ->where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper('Everspring') . '%')
                                ->where('user_id', $drm_product->user_id)
                                ->first();

                            if (blank($drmBrand)) {
                                DB::connection('drm_core')
                                    ->table('dropmatix_product_brands')
                                    ->insertOrIgnore([
                                        'brand_name' => strtoupper('Everspring'),
                                        'user_id' => $drm_product->user_id,
                                        'brand_logo' => null,
                                    ]);

                                $brandId = DB::connection('drm_core')
                                    ->table('dropmatix_product_brands')
                                    ->where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper('Everspring') . '%')
                                    ->where('user_id', $drm_product->user_id)
                                    ->value('id');

                                $drmBrand = (object) ['id' => $brandId];
                            }

                            $updateableColumns['brand'] = $drmBrand->id;

                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct(
                                collect([$drm_product]),
                                $updateableColumns
                            );

                            Log::info("product sync - {$local_product->ean}");
                    }

                    // DRM v2
                    foreach ($local_product->drmProductsV2 as $drm_product_v2) {
                        if (in_array($drm_product_v2->user_id, V2UserAccess::USERS)) {
                            $drmBrand = DB::connection('drm_core')
                                ->table('dropmatix_product_brands')
                                ->where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper('Everspring') . '%')
                                ->where('user_id', $drm_product_v2->user_id)
                                ->first();

                            if (blank($drmBrand)) {
                                DB::connection('drm_core')
                                    ->table('dropmatix_product_brands')
                                    ->insertOrIgnore([
                                        'brand_name' => strtoupper('Everspring'),
                                        'user_id' => $drm_product_v2->user_id,
                                        'brand_logo' => null,
                                    ]);

                                $brandId = DB::connection('drm_core')
                                    ->table('dropmatix_product_brands')
                                    ->where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper('Everspring') . '%')
                                    ->where('user_id', $drm_product_v2->user_id)
                                    ->value('id');

                                $drmBrand = (object) ['id' => $brandId];
                            }

                            $updateableColumnsV2['brand'] = $drmBrand->id;

                            $product_sync_data[] = [
                                'marketplace_product_id' => $drm_product_v2->marketplace_product_id,
                                'user_id' => $drm_product_v2->user_id,
                                'country_id' => $drm_product_v2->country_id,
                                'metadata' => json_encode($updateableColumnsV2),
                                'status' => 1,
                                'created_at' => \Carbon\Carbon::now(),
                                'updated_at' => \Carbon\Carbon::now(),
                            ];
                        }
                    }

                    Log::info("product tariff - {$local_product->item_number}");
                }

                if (!blank($product_sync_data)) {
                    DB::table('mp_product_sync_histories')->insert($product_sync_data);
                }
            });

        info('Sync completed.');
});

$router->get('manualy-drm-product-uvp-update-by-mp-supplier_id', function () {

    $products = Product::with('drmProducts', 'drmProductsV2')
        ->where('supplier_id', 4283)
        ->select('id', 'uvp', 'ean')
        ->get();

    $product_sync_data = [];
    foreach ($products as $product) {
        $data = [];
        $updateableColumns = [];

        // Handle drmProducts (V1)
        $drm_products = $product->drmProducts;
        if ($drm_products->isNotEmpty()) {
            $data['uvp'] = $product->uvp;
            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
            info("Product sync v1 - " . $product->ean);
        }

        // Handle drmProductsV2
        $drm_productsV2 = $product->drmProductsV2;
        if ($drm_productsV2->isNotEmpty()) {
            $updateableColumns['uvp'] = $product->uvp;

            foreach ($drm_productsV2 as $drm_product_v2) {
                if (in_array($drm_product_v2->user_id, V2UserAccess::USERS)) {
                    $product_sync_data[] = [
                        'marketplace_product_id' => $drm_product_v2->marketplace_product_id,
                        'user_id' => $drm_product_v2->user_id,
                        'country_id' => $drm_product_v2->country_id,
                        'metadata' => json_encode($updateableColumns),
                        'status' => 1,
                        'created_at' => \Carbon\Carbon::now(),
                        'updated_at' => \Carbon\Carbon::now(),
                    ];
                }
            }

            info("Product sync v2 - " . $product->ean);
        }
    }

    // Optional: Insert to database if needed
    if (!empty($product_sync_data)) {
        DB::table('mp_product_sync_histories')->insert($product_sync_data);
    }

    return 'Sync process completed.';
});