<?php

/** @var \Laravel\Lumen\Routing\Router $router */

use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\Marketplace\B2bUhrenApiController;
use App\Http\Controllers\Marketplace\BinoMertensApiController;


set_time_limit(0);

$router->group(['prefix'=>'v1', 'as'=>'v1.'], function () use ($router) {

    // TWN API
    $router->group(['prefix'=>'twm', 'as'=>'twm.'], function ($router) {
        //PRODUCT
        $router->get('build-prodcut-sync-jobs', '\App\Http\Controllers\Marketplace\BikeApiController@buildProductSyncJobs');

        $router->get('fetch-all-products',  '\App\Http\Controllers\Marketplace\BikeApiController@getAllProducts');

        $router->get('fetch-product-by-id/{id}',  '\App\Http\Controllers\Marketplace\BikeApiController@getProductById');

        $router->get('all-segments',  '\App\Http\Controllers\Marketplace\BikeApiController@productListArguments');

        $router->get('products-per-segement/{segment}',  '\App\Http\Controllers\Marketplace\BikeApiController@productsPerSegment');
        $router->get('products-for-last-n-days/{segment_code}/{days}',  '\App\Http\Controllers\Marketplace\BikeApiController@v2FetchChangedProductsForNDays');

        //STOCK
        $router->get('stock-changes-in-last-n-minutes[/minutes]',  '\App\Http\Controllers\Marketplace\BikeApiController@fetchStockChangesInTheLastNMinutes');

        $router->get('stock-by-product-id/{id}',  '\App\Http\Controllers\Marketplace\BikeApiController@fetchStockPerProductById');

        // TRACKING
        $router->get('all-tracking-code-for-user',  '\App\Http\Controllers\Marketplace\BikeApiController@listAllTrackingCodeForUser');
        $router->get('tracking-code-by-id/{id}',  '\App\Http\Controllers\Marketplace\BikeApiController@fetchTrackingCodeById');

        // ORDERS
        $router->get('submit-order/{orderId}',  '\App\Http\Controllers\Marketplace\BikeApiController@submitAnOrder');

        //all orders
        $router->get('all-orders',  '\App\Http\Controllers\Marketplace\BikeApiController@listAllOrdersForUser');
        $router->get('order-by-id/{id}',  '\App\Http\Controllers\Marketplace\BikeApiController@fetchOrderById');
        $router->get('order-for-last-n-days/{days}',  '\App\Http\Controllers\Marketplace\BikeApiController@fetchOrdersForlastNDays');

        $router->get('stock-out-product-sync', function () {
            app(\App\Services\Marketplace\BikeApi\BikeApiService::class)->syncStockOutProducts();
        });

        //TEST
        $router->get('test/set-page-to-one', function () {
            \App\Models\Marketplace\BikeApiCredential::first()
                ->update([
                    'next_import_page' => 1
                ]);
        });

        $router->get('test-read', function () {
            $product = App\Models\DrmOrder::first();
            dd($product);
        });

        $router->get('test-tracking-983458723746523498798797786766543', function () {
            return app(\App\Http\Controllers\Marketplace\BikeApiController::class)
                    ->fetchTrackingCodeById(7000999);
        });

        $router->get('insert-productsin-manual-way', function () {
            return app(\App\Http\Controllers\Marketplace\BikeApiController::class)->getAllProductsManualProcess();
        });

        $router->get('insert-item-number-as-ean', function () {
            $productsFromApiButWithoutItemNumber = \App\Models\Marketplace\Product::whereNull('item_number')
                                                    ->where('api_product_id', '>', 0)
                                                    ->orderBy('id','desc')
                                                    ->limit(25000)
                                                    ->get();
            $count = 0;
            foreach ($productsFromApiButWithoutItemNumber as $product) {
                $product->update([
                    'item_number' => $product->ean,
                ]);
                $count++;
            }

            dd($count);
        });

        $router->get('change-attributes-to-de/{productId}', '\App\Http\Controllers\Marketplace\BikeApiController@changeAttrInDe');
    });

    // b2buhren API
    $router->group(['prefix'=>'b2buhren','as'=>'b2buhren.'],function ($router){
        $router->get('brand-sync','B2bUhrenApiController@brandSync');
        $router->get('fetch-product-by-brand/{brand}','B2bUhrenApiController@fetchProductBYBrand');
        $router->get('build-prodcut-sync-jobs','B2bUhrenApiController@buildProductSyncJobs');
        $router->get('sync-product-stock','B2bUhrenApiController@syncProductStockFromApi');
        $router->get('fetch-product-by-id/{id}','B2bUhrenApiController@getProductById');
        $router->get('fetch-all-shipping-contry','B2bUhrenApiController@getShippingCountry');


        $router->get('getproductbyid/{id}','B2bUhrenApiController@fetchProductById');
    });

    // BDroppy API
    $router->group(['prefix'=>'bdroppy', 'as'=>'bdroppy.'], function ($router) {
        $router->get('import-products', 'BDroppyApiController@importProducts');
        $router->get('import-catelog-list', 'BDroppyApiController@getCatelogList');
        $router->get('import-categories', 'BDroppyApiController@getCategories');
    });

    $router->get('change-attributes-to-de', '\App\Http\Controllers\Marketplace\BikeApiController@changeColorInDe');

    $router->group(['prefix'=>'vidaxl', 'as'=>'vidaxl.'], function ($router) {
        $router->get('stock-sync', '\App\Http\Controllers\Marketplace\VidaXlApiController@stockUpdateFromCsv');
        $router->get('fetch-product-from-csv', '\App\Http\Controllers\Marketplace\VidaXlApiController@getDataFromCsv');
    });

    $router->get('send-drm-order-to-mp/{orderId}','\App\Http\Controllers\Marketplace\OrderController@getOrderFromDRM');
    $router->get('get-api-tracking','\App\Http\Controllers\Marketplace\OrderController@getOrderTrackingNumber');

    // please do not use this route. This is emergency route for channel stock update
    $router->get('update-channel-stock-mp',function(){

        if(isset($_GET['product_id'])){
            $product = Product::with('drmProducts')->where('id',$_GET['product_id'])->select('id','stock')->first();

            if(!empty($product)){
                    $drmProducts = $product->drmProducts;
                    if(count($drmProducts) > 0){
                        foreach($drmProducts as $drmProduct){
                            if($product->stock < 2){
                                $updateableColumns['stock'] = 0;
                                $updateableColumns['old_stock'] = $product->old_stock ?? 0;
                                $updateableColumns['stock_updated_at'] = \Carbon\Carbon::now();
                            }else{
                                $updateableColumns['stock'] = $product->stock;
                                $updateableColumns['old_stock'] = $product->old_stock ?? 0;
                                $updateableColumns['stock_updated_at'] = \Carbon\Carbon::now();
                            }

                            if(count($updateableColumns) > 0){
                                $data['product_id'] = $drmProduct->id;
                                $data['user_id'] = $drmProduct->user_id;
                                $data['metadata'] = $updateableColumns;

                                app(\App\Services\Marketplace\ProductService::class)->buildRequest(json_encode($data));
                            }

                        }
                    }
            }else{
                return 'Product not found';
            }
        }else{
            return 'pram not found';
        }
    });

    $router->group(['prefix'=>'BinoMertens', 'as'=>'BinoMertens.'], function ($router) {
        $router->get('fetch-products-from-xml', '\App\Http\Controllers\Marketplace\BinoMertensApiController@getDataFromXmlFile');
    });

    $router->group(['prefix'=>'plush','as'=>'plush.'],function ($router){
        $router->get('insert-products','\App\Http\Controllers\Marketplace\PlushController@insertProduct');
        $router->get('update-products','\App\Http\Controllers\Marketplace\PlushController@updateProduct');
        $router->get('product-brand-update','\App\Http\Controllers\Marketplace\PlushController@productBrandUpdate');
    });

    // $router->group(['prefix'=>'elady'],function ($router){
    //     $router->get('product-update',function(){
    //         if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'elady'){
    //             app(\App\Http\Controllers\Marketplace\CollectionController::class)->eladyProductUpdate();
    //         }else{
    //             return 'Token not found';
    //         }
    //     });
    // });

    $router->group(['prefix'=>'lkjlkjlakfjalkf','as'=>'lkjlkjlakfjalkf.'],function ($router){

        $router->get('manual-csv-product-insert',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'manualcsvproductinsert'){
                app(\App\Http\Controllers\Marketplace\CollectionController::class)->manualCsvProductInsert();
            }else{
                return 'Token not found';
            }
        });

        $router->get('binomertens-stockout-product-update',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'binostockout'){
                app(\App\Http\Controllers\Marketplace\BinoMertensApiController::class)->stockOutProduct();
            }else{
                return 'Token not found';
            }
        });

        $router->get('plush-product-description-update',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'plushdescription'){
                app(\App\Http\Controllers\Marketplace\PlushController::class)->productDescriptionUpdate();
            }else{
                return 'Token not found';
            }
        });

        $router->get('plush-order-tracking-sync',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'plushtracking'){
                app(\App\Http\Controllers\Marketplace\PlushController::class)->plushTrackingNumberSync();
            }else{
                return 'Token not found';
            }
        });

        $router->get('brand-update-manually',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'brandupdate'){
                app(\App\Http\Controllers\Marketplace\ProductController::class)->productBrandManualUpdate();
            }else{
                return 'Token not found';
            }
        });

        $router->get('fetch-bigbuy-parent-product-stock',function(){
            if(isset($_REQUEST['token']) && $_REQUEST['token'] == 'bparentstock'){
                app(\App\Http\Controllers\Marketplace\BigBuyApiController::class)->getParentProductStock();
            }else{
                return 'Token not found';
            }
        });
        $router->get('insert-bigbuy-scale-price',function(){
            app(\App\Http\Controllers\Marketplace\BigBuyApiController::class)->insertApiScalePrice();
        });

        $router->get('insert-vidax-ch-product',function(){
            app(App\Http\Controllers\Marketplace\Vidaxl\CountryWiseProductController::class)->insertChProduct();
        });

        $router->get('insert-vidax-es-product',function(){
            app(App\Http\Controllers\Marketplace\Vidaxl\CountryWiseProductController::class)->insertESProduct();
        });

        $router->get('insert-vidax-at-product',function(){
            app(App\Http\Controllers\Marketplace\Vidaxl\CountryWiseProductController::class)->insertATProduct();
        });

        $router->get('stock-sync-vidax-all-country',function(){
            app(\App\Services\Marketplace\Vidaxl\StockSyncProcess::class)->process();
        });

        $router->get('price-sync-vidax-all-country',function(){
            if(isset($_GET['country'])){
                if($_GET['country'] == 'de'){
                    app(\App\Services\Marketplace\Vidaxl\PriceSyncProcess::class)->syncDeProductPrice();
                }elseif($_GET['country'] == 'ch'){
                    app(\App\Services\Marketplace\Vidaxl\PriceSyncProcess::class)->syncChProductPrice();
                }elseif($_GET['country'] == 'es'){
                    app(\App\Services\Marketplace\Vidaxl\PriceSyncProcess::class)->syncEsProductPrice();
                }elseif($_GET['country'] == 'at'){
                    app(\App\Services\Marketplace\Vidaxl\PriceSyncProcess::class)->syncAtProductPrice();
                }
            }else{
                dd('country parms not found');
            }
        });

        $router->get('insert-bigbuy-product',function(){
            if(isset($_GET['country'])){
                if($_GET['country'] == 'de'){
                    app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->productInsertDE();
                }elseif($_GET['country'] == 'ch'){
                    app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->productInsertCH();
                }elseif($_GET['country'] == 'es'){
                    app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->productInsertES();
                }elseif($_GET['country'] == 'at'){
                    app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->productInsertAT();
                }
            }else{
                dd("select country");
            }
        });
        
        $router->get('bigbuy-stock-sync',function(){
            app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->stockSync();
        });

        $router->get('bigbuy-price-sync',function(){
            app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->priceSync();
        });

        $router->get('insert-bigbuy-variant-product',function(){
            if(isset($_GET['country'])){
                if($_GET['country'] == 'es'){
                    app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->variantProductInsertES();
                }
            }else{
                dd("select country");
            }
        });
        
    });

    $router->group(['prefix'=>'VanDerMeer', 'as'=>'VanDerMeer.'], function ($router) {
        $router->get('fetch-products-from-ftp-xml', '\App\Http\Controllers\Marketplace\VanDerMeerSupplierController@getDataFromFtpXmlFile');
    });


    // cache remove route
    $router->get('mp-category-cache-remove', function () {
        Cache::forget('marketplace_categories_im_handel');
        info('mp category cache removed');
    });

    //return label
    $router->get('swiss-post-return-label', function(){
        if(isset($_REQUEST['token']) && !empty($_REQUEST['order_id']) && $_REQUEST['token'] == 'swisspostreturn3423jjfja'){
            return app(\App\Http\Controllers\Marketplace\SwissPostController::class)->retrnLabel($_REQUEST['order_id']);
        }else{
            return 'Token or order id not found';
        }
    });

});
