<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNextImportPageToMarketplaceBdroppyCatelogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if ( !Schema::hasColumn('marketplace_bdroppy_catelogs', 'next_import_page') ) {
            Schema::table('marketplace_bdroppy_catelogs', function (Blueprint $table) {
                $table->unsignedInteger('next_import_page')
                      ->default(1)
                      ->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('marketplace_bdroppy_catelogs', function (Blueprint $table) {
            $table->dropColumn('next_import_page');
        });
    }
}
