<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCsvUrlToAllApiSyncReport extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('all_api_sync_report', 'csv_url')) {
            Schema::table('all_api_sync_report', function (Blueprint $table) {
                $table->string('csv_url')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('all_api_sync_report', 'csv_url')) {
            Schema::table('all_api_sync_report', function (Blueprint $table) {
                $table->dropColumn('csv_url');
            });
        }
    }
}
