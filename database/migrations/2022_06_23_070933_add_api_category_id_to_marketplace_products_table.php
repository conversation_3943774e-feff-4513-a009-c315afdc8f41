<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddApiCategoryIdToMarketplaceProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('marketplace_products', 'api_category_id')) {
            Schema::table('marketplace_products', function (Blueprint $table) {
               $table->integer('api_category_id')->after('category_id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('marketplace_products', 'api_category_id')) {
            Schema::table('marketplace_products', function (Blueprint $table) {
                $table->dropColumn('api_category_id');
            });
        }
    }
}
