<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceApiSyncHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('marketplace_api_sync_histories')) {
            Schema::create('marketplace_api_sync_histories', function (Blueprint $table) {
                $table->id();
                $table->integer('api_id');
                $table->integer('page_no')->nullable();
                $table->string('start_time')->nullable();
                $table->integer('sync_report_id');
                $table->json('response')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('marketplace_api_sync_histories')) {
            Schema::dropIfExists('marketplace_api_sync_histories');
        }
    }
}
