<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddApiIdToMarketplaceApiCredentials extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('marketplace_api_credentials', 'api_id')) {
            Schema::table('marketplace_api_credentials', function (Blueprint $table) {
                $table->unsignedInteger('app_id')
                      ->nullable()
                      ->after('id'); 
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('marketplace_api_credentials', 'api_id')) {
            Schema::table('marketplace_api_credentials', function (Blueprint $table) {
                $table->dropColumn('api_id');
            });
        }
    }
}
