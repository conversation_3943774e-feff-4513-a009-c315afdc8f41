<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStockUpdatedAtToMarketplaceApiCredentials extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if ( !Schema::hasColumn('marketplace_api_credentials', 'stock_updated_at') ) {
            Schema::table('marketplace_api_credentials', function (Blueprint $table) {
                $table->timestamp('stock_updated_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('marketplace_api_credentials', function (Blueprint $table) {

        });
    }
}
