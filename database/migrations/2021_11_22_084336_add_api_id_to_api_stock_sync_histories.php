<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddApiIdToApiStockSyncHistories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('api_stock_sync_histories', 'api_id')) {
            Schema::table('api_stock_sync_histories', function (Blueprint $table) {
                $table->unsignedInteger('api_id')->after('id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('api_stock_sync_histories', 'api_id')) {
            Schema::table('api_stock_sync_histories', function (Blueprint $table) {
                    $table->dropColumn('api_id');
            });
        }
    }
}
