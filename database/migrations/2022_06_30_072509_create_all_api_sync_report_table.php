<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAllApiSyncReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('all_api_sync_report')) {

            Schema::create('all_api_sync_report', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('api_id');
                $table->text('file_name')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('all_api_sync_report')) {
            Schema::dropIfExists('all_api_sync_report');
        }
    }
}
