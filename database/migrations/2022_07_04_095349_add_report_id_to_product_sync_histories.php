<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddReportIdToProductSyncHistories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product_sync_histories', 'report_id')) {
            Schema::table('product_sync_histories', function (Blueprint $table) {
                $table->integer('report_id')->nullable()->after('id');
            });
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (!Schema::hasColumn('product_sync_histories', 'report_id')) {
            Schema::table('product_sync_histories', function (Blueprint $table) {
                $table->dropColumn('report_id');
            });
        }
    }
}
