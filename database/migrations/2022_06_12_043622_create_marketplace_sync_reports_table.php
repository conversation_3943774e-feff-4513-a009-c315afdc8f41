<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceSyncReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('marketplace_sync_reports')) {
            Schema::create('marketplace_sync_reports', function (Blueprint $table) {
                $table->id();
                $table->string('sync_time');
                $table->string('status');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('marketplace_sync_reports')) {
            Schema::dropIfExists('marketplace_sync_reports');
        }
    }
}
