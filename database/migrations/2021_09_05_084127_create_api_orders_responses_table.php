<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApiOrdersResponsesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_orders_responses', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('api_id');
            $table->unsignedInteger('order_id');
            $table->string('reference_no')->nullable();
            $table->string('invoice_number')->nullable();
            $table->string('tracking_codes')->nullable();
            $table->unsignedFloat('shipping_cost', 6, 2);
            $table->unsignedFloat('total', 8, 2);
            $table->json('customer_infos')->nullable();
            $table->json('product_infos')->nullable();
            $table->json('misc')->nullable();
            $table->json('error_response')->nullable();
            $table->unsignedTinyInteger('status')->default(1);
            $table->timestamp('order_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('api_orders_responses');
    }
}
