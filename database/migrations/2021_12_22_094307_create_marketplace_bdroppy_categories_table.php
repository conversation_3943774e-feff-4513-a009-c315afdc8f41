<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceBdroppyCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('marketplace_bdroppy_categories', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('api_category_id')
                  ->nullable();
            $table->string('name', 100)
                  ->nullable();
            $table->string('code', 100)
                  ->nullable();
            $table->boolean('active')
                  ->nullable()
                  ->default(true);
            $table->json('translations')
                  ->nullable();
            $table->unsignedTinyInteger('status')
                  ->default(1);
            $table->unsignedInteger('marketplace_category_id')
                  ->nullable(); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('marketplace_bdroppy_categories');
    }
}
