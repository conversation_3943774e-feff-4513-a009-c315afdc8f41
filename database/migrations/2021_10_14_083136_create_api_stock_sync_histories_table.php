<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApiStockSyncHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('api_stock_sync_histories', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('mp_product_id')->nullable();
            $table->unsignedInteger('bike_api_id')->nullable();
            $table->json('drm_product_ids')->nullable();
            $table->unsignedInteger('mp_updated_atock')->nullable();
            $table->unsignedInteger('drm_updated_stock')->nullable();
            $table->timestamp('sync_time')->nullable();
            $table->boolean('status')->nullable();
            $table->timestamps();
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('api_stock_sync_histories');
    }
}
