<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceReviewsRatingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('marketplace_reviews_rating')) {
            Schema::create('marketplace_reviews_rating', function (Blueprint $table) {
                $table->id();
                $table->string('rating')->nullable();
                $table->string('review_count')->nullable();
                $table->string('ean')->unique()->nullable();
                $table->timestamps();

                // Adding an index to the 'ean' column
                $table->index('ean');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('marketplace_reviews_rating')) {
            Schema::dropIfExists('marketplace_reviews_rating');
        }
    }
}
