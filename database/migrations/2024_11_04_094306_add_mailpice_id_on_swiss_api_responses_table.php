<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMailpiceIdOnSwissApiResponsesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('swiss_api_responses', 'mailpiece_id')) {
            Schema::table('swiss_api_responses', function (Blueprint $table) {
                $table->string('mailpiece_id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('swiss_api_responses', function (Blueprint $table) {
            //
        });
    }
}
