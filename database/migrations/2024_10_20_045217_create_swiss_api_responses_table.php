<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSwissApiResponsesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('swiss_api_responses')) {
            Schema::create('swiss_api_responses', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('drm_order_id')->default(0);
                $table->string('pdf_url')->nullable();
                $table->json('response')->nullable();
                $table->string('status_message')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('swiss_api_responses');
    }
}
