<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceBdroppyCatelogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('marketplace_bdroppy_catelogs', function (Blueprint $table) {
            $table->id();
            $table->string('catelog_id', 64)
                  ->nullable();
            $table->string('name', 64)
                  ->nullable();
            $table->unsignedInteger('count')
                  ->default(0)
                  ->nullable();
            $table->string('currency')
                  ->nullable();
            $table->json('attributes')
                  ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('marketplace_bdroppy_catelogs');
    }
}
