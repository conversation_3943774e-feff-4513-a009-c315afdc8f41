<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceDailyApiUpdateScheduleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('marketplace_daily_api_update_schedule')) {
            Schema::create('marketplace_daily_api_update_schedule', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('api_id');
                $table->timestamp('next_update_time');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('marketplace_daily_api_update_schedule')) {
           Schema::dropIfExists('marketplace_daily_api_update_schedule');
        }
    }
}
