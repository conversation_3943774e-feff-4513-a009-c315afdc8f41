<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceB2bUhrenApiBrands extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('marketplace_b2b_uhren_api_brands', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('id_brand')->unique();
            $table->string('name')->nullable();
            $table->string('group')->nullable();
            $table->string('category')->nullable();
            $table->unsignedInteger('count_product')->default(0);
            $table->unsignedInteger('sync_time')->nullable();
            $table->unsignedInteger('sync_count')->default(0);
            $table->unsignedInteger('sync_status')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('marketplace_b2b_uhren_api_brands');
    }
}
