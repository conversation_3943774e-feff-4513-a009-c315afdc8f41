<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMarketplaceAllApiStocksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('marketplace_all_api_stocks')) {

            Schema::create('marketplace_all_api_stocks', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('api_id');
                $table->string('ean');
                $table->string('sku')->nullable();
                $table->integer('stock')->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('marketplace_all_api_stocks');
    }
}
