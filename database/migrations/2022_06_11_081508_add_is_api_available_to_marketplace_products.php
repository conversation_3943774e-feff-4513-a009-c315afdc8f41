<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsApiAvailableToMarketplaceProducts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('marketplace_products', 'is_api_available')) {
            Schema::table('marketplace_products', function (Blueprint $table) {
                $table->boolean('is_api_available')->default(false);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('marketplace_products', 'is_api_available')) {
            Schema::table('marketplace_products', function (Blueprint $table) {
                $table->dropColumn('is_api_available');
            });
        }
    }
}
