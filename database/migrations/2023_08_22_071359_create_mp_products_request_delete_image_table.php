<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMpProductsRequestDeleteImageTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('request_mp_products_image_delete')) {
            Schema::create('request_mp_products_image_delete', function (Blueprint $table) {
                $table->id();
                $table->json('request_url');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('request_mp_products_image_delete')) {
            Schema::dropIfExists('request_mp_products_image_delete');
        }
    }
}
