<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsUpdateableToMarketplaceAllApiStocks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('marketplace_all_api_stocks', 'is_updateable')) {
            Schema::table('marketplace_all_api_stocks', function (Blueprint $table) {
                $table->boolean('is_updateable')->default(true);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('marketplace_all_api_stocks', 'is_updateable')) {
            Schema::table('marketplace_all_api_stocks', function (Blueprint $table) {
                $table->dropColumn('is_updateable');
            });
        }
    }
}
