<?php
return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', 3306),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            // 'timezone' => env('DB_TIMEZONE', '+00:00'),
        ],
        'drm_core' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_DRM', '127.0.0.1'),
            'port' => env('DB_PORT_DRM', 3306),
            'database' => env('DB_DATABASE_DRM', 'forge'),
            'username' => env('DB_USERNAME_DRM', 'forge'),
            'password' => env('DB_PASSWORD_DRM', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            // 'timezone' => env('DB_TIMEZONE', '+00:00'),
        ],
        'drm_team' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_DRM_TEAM', '127.0.0.1'),
            'port' => env('DB_PORT_DRM_TEAM', 3306),
            'database' => env('DB_DATABASE_DRM_TEAM', 'forge'),
            'username' => env('DB_USERNAME_DRM_TEAM', 'forge'),
            'password' => env('DB_PASSWORD_DRM_TEAM', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            // 'timezone' => env('DB_TIMEZONE', '+00:00'),
        ],
        'drm_core_team' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_DRM_CORE_TEAM', '127.0.0.1'),
            'port' => env('DB_PORT_DRM_CORE_TEAM', 3306),
            'database' => env('DB_DATABASE_DRM_CORE_TEAM', 'forge'),
            'username' => env('DB_USERNAME_DRM_CORE_TEAM', 'forge'),
            'password' => env('DB_PASSWORD_DRM_CORE_TEAM', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            // 'timezone' => env('DB_TIMEZONE', '+00:00'),
        ],
        'mp_back_up' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_MP_BACK_UP', '127.0.0.1'),
            'port' => env('DB_PORT_MP_BACK_UP', 3306),
            'database' => env('DB_DATABASE_MP_BACK_UP', 'forge'),
            'username' => env('DB_USERNAME_MP_BACK_UP', 'forge'),
            'password' => env('DB_PASSWORD_MP_BACK_UP', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            // 'timezone' => env('DB_TIMEZONE', '+00:00'),
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'cluster' => env('REDIS_CLUSTER', false),
        'read_write_timeout' => -1,
        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'username' => env('REDIS_USERNAME', 'default'),
            "scheme" => env('REDIS_SCHEME', 'tls'),
            'database' => env('REDIS_DB', 0),
            'read_write_timeout' => -1,
        ],

        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'username' => env('REDIS_USERNAME', 'default'),
            "scheme" => env('REDIS_SCHEME', 'tls'),
            'database' => env('REDIS_CACHE_DB', 1),
            'read_write_timeout' => -1,
        ],
    ],


];
