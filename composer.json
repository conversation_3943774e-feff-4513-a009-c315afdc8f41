{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.3|^8.0", "aws/aws-sdk-php": "~3.0", "flipbox/lumen-generator": "^8.2", "google/cloud-translate": "^1.15", "illuminate/redis": "^8.55", "laravel/lumen-framework": "^8.0", "league/csv": "^9.8", "league/flysystem": " ~1.0", "league/flysystem-aws-s3-v3": "~1.0", "maatwebsite/excel": "^3.1", "phpoffice/phpspreadsheet": "^1.9", "predis/predis": "~1.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/MarketplaceHelpers.php"]}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}