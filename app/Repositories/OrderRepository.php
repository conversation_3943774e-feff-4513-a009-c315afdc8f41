<?php

namespace App\Repositories;

use App\Enums\Marketplace\ApiResources;
use App\Models\DrmOrder;
use App\Interfaces\OrderInterFace;
use App\Models\Marketplace\Product;
use Illuminate\Support\Str;
class OrderRepository implements OrderInterFace
{
    public function getOrderById($orderId)
    {
        try{
            $order           = DrmOrder::with('customer')
                                        ->where('id',$orderId)
                                        ->where('test_order','!=',1)
                                        ->whereNull('mp_api_id')
                                        ->where(function ($query){
                                            $query->where('marketplace_paid_status',1)
                                            ->orWhereNotNull('mp_payment_agreement_at')
                                            ->orWhere('payment_agreement',1);
                                        })
                                        ->first();

            if(isset($order)){

                $customer_info   = $this->getShippingInfo($order);
                $products        = $this->getProducts($order);

                $order_info      = [
                    'order_id'               => $order->id,
                    'marketplace_order_ref'  => $order->marketplace_order_ref,
                    'marketplace_paid_status'=> $order->marketplace_paid_status,
                    'payment_type'           => $order->payment_type,
                    'invoice_number'         => $order->invoice_number,
                    'shipping_cost'          => $order->shipping_cost,
                    'total'                  => $order->total,
                    'user_id'                => $order->cms_client,
                    'cms_user_id'            => $order->cms_user_id,
                ];

                $order_informations = [
                    'customer_info' => $customer_info,
                    'products'      => $products,
                    'order_info'    => $order_info,
                ];

                return $order_informations;

            }else{
                return false;
            }

        }catch (\Exception $e){

            return response()->json([
                'status'=>'error',
                'message'=>$e->getMessage(),
            ]);
        }
    }

    function getShippingInfo ($drmOrder)
    {
        $customerInfo = getAndProcessCustomerInfosFromDrmOrderForBikeApiOrder($drmOrder);

        return $customerInfo;

    }

    public function getProducts ($drmOrder)
    {
        if(Str::contains($drmOrder->order_id_api,"mpmanual_s")){

            $cart = DrmOrder::where('id',$drmOrder->marketplace_order_ref)
                                ->select('cart')
                                ->first();

            $order_products = collect(json_decode($cart->cart, 1))->toArray();

        }else{

            $order_products = collect(json_decode($drmOrder->cart, 1))->toArray();

        }

        $products = [];

        foreach($order_products as $order_product){
    
            $query = Product::where('ean', $order_product['ean'])
            ->where(function ($query) use ($order_product) {
                $query->where('shipping_method', 2)
                        ->where('status', 1)
                        ->where('internel_stock', '>=', $order_product['qty']);
            })
            ->select('id', 'api_product_id', 'ean', 'item_number', 'name', 'api_id', 'misc', 'delivery_company_id', 'shipping_method','item_weight','description');
                                    
            $local_product = $query->exists() ? $query->first() : $query->orWhere('id', $order_product['marketplace_product_id'])->first();
                                      
            if($local_product){
                if(in_array($local_product->id, array_column($products, 'product_id'))){

                    $key = array_search($local_product->id, array_column($products, 'product_id'));
                    $products[$key]['qty'] += $order_product['qty'];
                    
                }else{
                    $data['product_id']         = $local_product->id;
                    $data['api_product_id']     = $local_product->api_product_id;
                    $data['ean']                = $local_product->ean;
                    $data['item_number']        = $local_product->item_number;
                    $data['name']               = $local_product->name;
                    $data['qty']                = $order_product['qty'];
                    $data['rate']               = $order_product['rate'];
                    $data['tax']                = $order_product['tax'] ?? 0;
                    $data['product_discount']   = $order_product['product_discount'] ?? 0;
                    $data['amount']             = $order_product['amount'];
                    $data['shipping_cost']      = $order_product['shipping_cost'] ?? 0;
                    $data['api_id']             = $this->orderSendLocation($local_product);
                    $data['misc']               = $local_product->api_id == 4 ? $local_product->misc:'';
                    $data['drm_product_id']     = $order_product['product_id'] ?? null;
                    $data['item_weight']        = $local_product['item_weight'] ?? null;
                    $data['description']        = $local_product['description'] ?? null;
                    $data['currency']           = $drmOrder->currency ?? "EUR";
                    $products[]                 = $data;
                }
            }
        }

        return $products;
    }

    private function orderSendLocation($local_product): int
    {
        if($local_product->delivery_company_id == ApiResources::PLUSH_DELIVERY_COMPANY_ID || $local_product->shipping_method == 2){
            return ApiResources::PLUSH_DELIVERY_COMPANY_ID;
        }else{
            return $local_product->api_id;
        }
    }

    public function sendOrderToApi($order_informations)
    {
        try {
            // Process Swiss orders first - create duplicate with MyCargoGate address if needed
            $order_informations = app(\App\Services\Marketplace\SwissOrderService::class)->processSwissOrder($order_informations);

            $products       = $order_informations['products'];
            $customer_info  = $order_informations['customer_info'];
            $order_info     = $order_informations['order_info'];

            if(empty($products)) return response()->json(['status'=>'error','message' => 'product not found'], 404);

            $api_send_address     = \App\Enums\Marketplace\ApiResources::ORDER_SEND_ADDRESS;
            $productCollection    = collect($products)->groupBy('api_id');

            $response             = [];
            foreach($productCollection as $product){
                if(array_key_exists($product[0]['api_id'],$api_send_address)){
                    $response = app(\App\Enums\Marketplace\ApiResources::ORDER_SEND_ADDRESS[$product[0]['api_id']].''::class)->sendOrder($product,$customer_info,$order_info);
                }
            }

            return $response;

        } catch (\Exception $e) {
            return response()->json([
                'status'=>'error',
                'message'=>$e->getMessage(),
            ]);
        }

    }

    public function getDiliveryOrderInfoById($orderId)
    {
        try{
            $order  = DrmOrder::where('id',$orderId)->where('test_order','!=',1)->first();

            if(isset($order)){

                $products        = $this->getProducts($order);

                $order_info      = [
                    'order_id'               => $order->id,
                    'marketplace_order_ref'  => $order->marketplace_order_ref,
                    'marketplace_paid_status'=> $order->marketplace_paid_status,
                    'payment_type'           => $order->payment_type,
                    'invoice_number'         => $order->invoice_number,
                    'shipping_cost'          => $order->shipping_cost,
                    'total'                  => $order->total,
                    'user_id'                => $order->cms_client,
                    'cms_user_id'            => $order->cms_user_id,
                ];

                $order_informations = [
                    'customer_info' => $this->getShippingInfo($order),
                    'products'      => $products,
                    'order_info'    => $order_info,
                ];

                return $order_informations;

            }else{
                return false;
            }

        }catch (\Exception $e){

            return response()->json([
                'status'=>'error',
                'message'=>$e->getMessage(),
            ]);
        }
    }


}
