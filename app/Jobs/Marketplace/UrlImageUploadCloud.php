<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;
class UrlImageUploadCloud extends Job
{
    private $products;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($products)
    {
        $this->products = $products;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("Image upload process start");
        app(\App\Services\Marketplace\UrlImageUploadService::class)->upload_url_image($this->products);
    }
}
