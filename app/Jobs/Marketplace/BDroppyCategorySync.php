<?php

namespace App\Jobs\Marketplace;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Jobs\Job;
use Log;

class BDroppyCategorySync extends Job
{
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('BDroppy Category sync job run');
        app ( \App\Http\Controllers\Marketplace\BDroppyApiController::class )
            ->getCategories();
    }
}
