<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;

class B2bUhrenApiStockSync extends Job
{
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("B2bUhren Product stock sync job dispatched !");

    }
}
