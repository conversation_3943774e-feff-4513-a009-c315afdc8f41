<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
class TrackingCodeSyncJob extends Job
{
    /**
     * Create a new job instance.
     *
     * @return void
     */

    public $apiOrderId;

    public function __construct($apiOrderId)
    {
        $this->apiOrderId = $apiOrderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            ->fetchTrackingCodeById($this->apiOrderId);
    }
}
