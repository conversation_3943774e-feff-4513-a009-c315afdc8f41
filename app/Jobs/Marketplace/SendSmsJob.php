<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;
class SendSmsJob extends Job
{
    public $message_content;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($message_content)
    {
        $this->message_content = $message_content;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("send message");
        app(\App\Http\Controllers\Marketplace\SmsController::class)->processSendSms($this->message_content);
    }
}
