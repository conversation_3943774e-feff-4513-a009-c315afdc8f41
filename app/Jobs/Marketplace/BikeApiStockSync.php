<?php

namespace App\Jobs\Marketplace;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Jobs\Job;

class BikeApiStockSync extends Job
{

    public $minutes;
    public $tries = 3;
    public $timeout = 3500;
    public function __construct($minutes = 5)
    {
        $this->minutes = $minutes;
    }
    public function handle()
    {
        app(BikeApiController::class)
            ->fetchStockChangesInTheLastNMinutes($this->minutes);
    }
}
