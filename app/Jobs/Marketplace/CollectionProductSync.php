<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;
class CollectionProductSync extends Job
{
    private $collection;
    private $csvRows;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($collection,$row)
    {
        $this->collection = $collection;
        $this->csvRows = $row;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("Collection Product Sync job dispatch");
        app(\App\Http\Controllers\Marketplace\CollectionController::class)->productSync($this->collection,$this->csvRows);
    }
}
