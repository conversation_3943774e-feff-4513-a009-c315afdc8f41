<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Log;
class CompareMpToDrmProductJob extends Job
{
    // protected $mp_products;
    protected $drm_products;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($drm_products)
    {
        // $this->mp_products  = $mp_products;
        $this->drm_products = $drm_products;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("MP To DRM purchaes compare job dispatch done");
        app(\App\Http\Controllers\Marketplace\ProductController::class)
        ->processMpToDrmProductCompare($this->drm_products);
    }
}
