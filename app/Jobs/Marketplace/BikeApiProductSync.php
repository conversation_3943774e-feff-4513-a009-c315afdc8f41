<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Jobs\Job;
use Log;

class BikeApiProductSync extends Job 
{
   
    public $pageArr;
    public $tries = 3;
    public $timeout = 3500;

    public function __construct($pageArr = [])
    {
        $this->pageArr = $pageArr;
    }
    public function handle()
    {
        Log::info("Product job dispatched !");
        // app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            // ->getAllProducts($this->pageArr);
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            ->getAllProductsManualProcess();


    }
}
