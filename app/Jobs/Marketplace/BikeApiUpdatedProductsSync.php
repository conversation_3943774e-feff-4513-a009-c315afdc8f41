<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\Job;

class BikeApiUpdatedProductsSync extends Job
{
    
    public $segmentCode;
    public function __construct($segmentCode)
    {
        $this->segmentCode = $segmentCode;
    }

    public function handle()
    {
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            ->v2FetchChangedProductsForNDays($this->segmentCode, 1);
    }
}
