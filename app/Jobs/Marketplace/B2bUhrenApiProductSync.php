<?php

namespace App\Jobs\Marketplace;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;

class B2bUhrenApiProductSync extends Job
{
    public $products;
    public $category;

    public function __construct($items,$category)
    {
        $this->products = $items;
        $this->category = $category;
    }

    
    public function handle()
    {
        Log::info("B2bUhren Product job dispatched !");
        // app(\App\Http\Controllers\Marketplace\B2bUhrenApiController::class)
        //     ->buildProductSyncJobs();
        app(\App\Services\Marketplace\B2bUhrenApi\B2bUhrenApiService::class)->pushProductsOldDBFromApi($this->products,$this->category);
    }
}
