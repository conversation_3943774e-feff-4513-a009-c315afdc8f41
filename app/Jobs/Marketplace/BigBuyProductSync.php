<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Jobs\Job;
use App\Services\Marketplace\BigBuyApi\BigBuyApiService;
use Log;

class BigBuyProductSync extends Job
{
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $catelog;
    public function __construct($catelog)
    {
        $this->catelog = $catelog;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(BigBuyApiService::class)->bigbuyProductStockSync();
        Log::info('Bigbuy job run');
    }
}

