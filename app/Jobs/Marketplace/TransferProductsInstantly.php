<?php

namespace App\Jobs\Marketplace;

use App\Jobs\Job;
use Illuminate\Support\Facades\Log;

class TransferProductsInstantly extends Job
{
    private $categorySubscription;
    private $high_tariff_users;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($categorySubscription, $high_tariff_users)
    {
        $this->categorySubscription = $categorySubscription;
        $this->high_tariff_users    = $high_tariff_users;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $user_id = $this->categorySubscription->user_id;
        $category_id =  $this->categorySubscription->category_id;
        if (in_array($user_id, $this->high_tariff_users)) {
            Log::info("MP To DRM auto transfer start running");
            Log::info("User_Id " . $user_id . " <---> Category_Id " . $category_id);
            app(\App\Http\Controllers\Marketplace\ProductController::class)->transferProductsInstantly($this->categorySubscription);
        } else {
            $this->categorySubscription->update(['status' => 0]);
        }
        
        // $url = "https://drm.software/api/check_drm/import_plan/".$user_id; //live url
        // $importProduct = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($url);
        // if(!blank($importProduct)){
        //     $import_plan_id = $importProduct['import_plan_id'];
        //     if (in_array($import_plan_id, [26, 27, 31]) && $importProduct['product_amount'] > 0) {
        //         Log::info("MP To DRM auto transfer start running");
        //         Log::info("User_Id " . $user_id . " <---> Category_Id " . $category_id);
        //         app(\App\Http\Controllers\Marketplace\ProductController::class)->transferProductsInstantly($this->categorySubscription);
        //     } else {
        //         $this->categorySubscription->update(['status' => 0]);
        //     }
        // }
    }
}
