<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Jobs\Job;
use Log;

class BDroppyProductSync extends Job
{

    public $catelog;

    public function __construct($catelog)
    {
        $this->catelog = $catelog;
    }

    public function handle()
    {
        Log::info("BDroppy product sync job run in handle !");

        $filter = [
            'acceptedlocales'   => 'en_US,de_DE',
            'user_catalog'      => $this->catelog->catelog_id,
            'pageSize'          => \App\Enums\Marketplace\ApiResources::BDROPPY_API_PER_PAGE_PRODUCT,
            'page'              => $this->catelog->next_import_page,
        ];

        $filter = http_build_query($filter);

        $products = app( \App\Services\Marketplace\BDroppy\ApiService::class )
            ->fetchData( \App\Enums\Marketplace\ApiResources::BDROPPY_APIS['PRODUCTS'].$filter );

        app( \App\Services\Marketplace\BDroppy\ApiService::class )
            ->insertProductsToMarketplace($products);

        if($this->catelog->next_import_page >= $products['totalPages']){
            $this->catelog->next_import_page = 1;
            $this->catelog->update();
            Log::info("Catelog page Updated");
        }else{
            $this->catelog->increment('next_import_page');
        }

    }
}
