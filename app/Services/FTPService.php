<?php

namespace App\Services;
use League\Flysystem\Adapter\Ftp;
use League\Flysystem\Filesystem;


use Exception;

class FTPService
{
    //Filesystem
    private $filesystem = null;

    public function __construct(array $config)
    {
        $adapter = new Ftp([
            'host' => $config['host'], // required
            'root' => '/', // required
            'username' => $config['username'], // required
            'password' => $config['password'], // required
            'port' => $config['port'],
            'ssl' => false,
            'timeout' => 90,
            'utf8' => false,
            'passive' => true,
            'transferMode' => FTP_BINARY,
            'systemType' => null, // 'windows' or 'unix'
            'ignorePassiveAddress' => null, // true or false
            'timestampsOnUnixListingsEnabled' => false, // true or false
            'recurseManually' => false // true
        ]);
        $this->filesystem = new Filesystem($adapter);
    }

    public function has(String $dir): bool
    {
        return $this->filesystem->has($dir);
    }

    //Store file to ftp
//    public function storeFileToFtp($path, $fileName, $contents)
//    {
//        try{
//            if(!$this->has($path)) throw new Exception('Folder not exists!');
//            $remoteName = $path.'/'.$fileName;
//            // upload a file
//            $response =  $this->filesystem->put($remoteName, $contents) ? true : false;
//            return $response;
//        }catch(\Exception $e){
//            return response()->json(['success' => false,'message' => $e->getMessage()]);
//        }
//    }

    public function getFileToFtp($path, $fileName){

        try{
            if(!$this->has($path)) throw new Exception('Folder not exists!');
            $remoteName = $path.'/'.$fileName;
            return $this->filesystem->read($remoteName);
        }catch(\Exception $e){
            return response()->json(['success' => false,'message' => $e->getMessage()]);
        }

    }
    //Store file to ftp
    public function storeFileToFtp($path, $fileName, $contents)
    {
        try {
            if (!$this->has($path)) throw new Exception('Folder not exists!');
            $remoteName = $path . '/' . $fileName;
            // upload a file
            return $this->filesystem->put($remoteName, $contents);
        } catch (\Exception $e) {
            return false;
        }
    }
}
