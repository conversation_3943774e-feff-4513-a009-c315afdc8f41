<?php
/*
	Author:: 			<EMAIL>
	Creating Date:: 	2021-01-20
*/
namespace App\Services\Marketplace;
use App\Services\BaseService;

class SoapRequest extends BaseService{

	public $openSoapEnvelope = '<?xml version="1.0" encoding="UTF-8"?>
                    <SOAP-ENV:Envelope
                    xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
                    xmlns:ns1="http://soapServer/"
                    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"
                    SOAP-ENV:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
                     <SOAP-ENV:Body>';
    public $closeSoapEnvelope = '</SOAP-ENV:Body>
                    </SOAP-ENV:Envelope>';
    public $params_body = '';
	public $request_xml = '';

	public function __construct()
	{

	}

	// Should be called before method::formRequest
	public function setParamsBody ($params_body) {
		$this->params_body = $params_body;
		return $this;
	}

	// Should be called before method::get
	public function formRequest()
	{
		$this->request_xml = $this->openSoapEnvelope.$this->params_body.$this->closeSoapEnvelope;
		return $this;
	}

	// Should return an SimpleXML object
	public function get()
	{
		$webservice_url = "http://test_api.internel.eu/server.php";
		$headers = array(
		    'Content-Type: text/xml;',
		    'Content-Length: '.strlen($this->request_xml)
		);

		$ch = curl_init($webservice_url);

		curl_setopt ($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt ($ch, CURLOPT_POSTFIELDS, $this->request_xml);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		$data = curl_exec ($ch);
		$result = $data;

		if ($result === FALSE) {
		    printf("CURL error (#%d): %s<br>\n", curl_errno($ch),
		    htmlspecialchars(curl_error($ch)));
		}
		curl_close ($ch);

		$xml = simplexml_load_string($data);
		return $xml;

	}

	public function post()
	{
		return 'Post testing';
	}

	public function getParamsBody ()
	{
		return $this->params_body;
	}

	public function getWholeSoapRequest()
	{
		return $this->request_xml;
	}
}
