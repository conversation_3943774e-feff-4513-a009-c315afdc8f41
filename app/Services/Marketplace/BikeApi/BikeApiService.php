<?php


namespace App\Services\Marketplace\BikeApi;

use Log;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Enums\Marketplace\ProductStatus;
use App\Services\Marketplace\ProductService;
use App\Models\Marketplace\BikeApiCredential;
use App\Models\Marketplace\MarketplaceApiCsv;
use App\Models\Marketplace\ProductSyncHistory;
use App\Services\Marketplace\DuplicateEanService;

class BikeApiService extends BaseService
{
    public $token;
    public $authEmail;
    public $authPassword;
    public $headers;
    public $ch;
    public $tokenUpdatedTime;
    public $apiCredentials;
    public $duplicateService;

    public function __construct()
    {
        $apiCredentials         = \App\Models\Marketplace\ApiCredential::where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)
                                ->first();

        Log::info($apiCredentials);


        // dd($apiCredentials);
        Log::info('Now');
        Log::info(\Carbon\Carbon::now());

        Log::info('Next update Time');
        Log::info($apiCredentials->token_next_update_time);
        if ( $apiCredentials->token_next_update_time < \Carbon\Carbon::now()  ) {
            $apiCredentials = $this->updateToken($apiCredentials);

            Log::info('In update section');
            Log::info($apiCredentials);

            if (isset($apiCredentials['error'])) {
                return $apiCredentials['msg'];
            }
        }

        $this->token            = $apiCredentials->token;
        $this->authEmail        = $apiCredentials->api_user_name;
        $this->authPassword     = $apiCredentials->api_password;
        // $this->tokenUpdatedTime = $apiCredentials->token_updated_time;
        $this->apiCredentials   = $apiCredentials;

        $this->ch               = curl_init();

        $this->headers           = [
            'Authorization: Bearer '.$this->token,
            'Content-Type: application/json',
            'Accept: application/json',
        ];

        $this->duplicateService = new DuplicateEanService();
    }

    public function updateToken ($apiCredentials)
    {
        try {
            $req = curl_init(\App\Enums\Marketplace\ApiResources::ADDRESSES['AUTH']);
            curl_setopt($req, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($req, CURLOPT_POSTFIELDS, [
                'email'         => $apiCredentials->api_user_name,
                'password'      => $apiCredentials->api_password,
            ]);
            $response = json_decode(curl_exec ($req), 1);

            Log::info($response);

            curl_close ($req);
            $apiCredentials->old_token          = $apiCredentials->token ?? '';
            // $apiCredentials->token              = $response['token'] ?? $apiCredentials->token;
            $apiCredentials->token              = $response['token'];
            $apiCredentials->token_next_update_time = date('Y-m-d H:i:s', $response['valid_until'] ?? $apiCredentials->token_next_update_time);
            $apiCredentials->save();

            return $apiCredentials;

        } catch (\Exception $e) {
            Log::info('Bike api token update failed');
            return [
                'staus' => 'error',
                'msg'   => 'Api Token is not valid. Error:: '.$e->getMessage(),
            ];
        }
    }

    public function fetchData($apiAddress, $method="GET", $data=[])
    {
        $response = $this->buildRequest($apiAddress, $method, $data)
            ->getResponse();
        return $response;
    }

    public function buildRequest ($apiAddress, $method='GET', $data=[]) {
        curl_setopt($this->ch, CURLOPT_URL,htmlspecialchars_decode($apiAddress));
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
        curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($this->ch, CURLOPT_POST, $method=="POST"?1:0);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
        if ( !empty($data) && $method=="POST" ) {
            curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
        }
        return $this;
    }

    public function getResponse ()
    {
        try {
            $response = curl_exec ($this->ch);
            curl_close ($this->ch);
            $this->ch = curl_init();
            return json_decode($response, 1);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function pushProductsFromApi ($apiProductsArr, $insertLimit = null)
    {
        $insertedProductsCount  = 0;
        $deliveryCompanyId      = \App\Enums\Marketplace\ApiResources::BIKE_API_DELIVERY_COMPANY_ID;
        $shippingCost           = \App\Enums\Marketplace\ApiResources::BIKE_API_DEFAULT_SHIPPING_COST;
        $deliveryDays           = \App\Enums\Marketplace\ApiResources::BIKE_API_DEFAULT_DELIVERY_DAYS;
        $supplierId             = \App\Enums\Marketplace\ApiResources::BIKE_API_SUPPLIER_ID;

        $api_brands = array_unique(array_column($apiProductsArr,'brand'));
        $brands     = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);

        $shippingCostArray = [ 'S'=>7.99,
                               'XS' => 7.99,
                               'M' => 11.99,
                               'L' => 36.99,
                               'P' => 97.99,
                               'XL' => 97.99
                            ];

        $old_products = Product::with('drmProducts')
                        ->where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)
                        ->whereIn('ean',array_column($apiProductsArr,'ean'))->get();
        
        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

        $maping_categories  = DB::table('api_category_mapping')
            ->where('api_id',\App\Enums\Marketplace\ApiResources::BIKE_API_ID)
            ->where('is_complete',1)
            ->select('api_category_id','mp_category_id')
            ->get();

        $categories =[];
        foreach($maping_categories as $m_category){
            $categories[$m_category->api_category_id] = $m_category->mp_category_id;
        }

        $product_sync_datas = [];
        $salesTrac = [];
        foreach ( $apiProductsArr as $singleProductArr ) {

            try {

                //limited insert condition
                if($insertLimit != null && $insertedProductsCount == $insertLimit) {
                    break;
                }

                // GET IMAGES
                $images = [];
                $medias = $singleProductArr['media'];
                foreach ( $medias as $media ) {
                    if ( $media['type'] == 'image' ) $images[] = $media['url'];
                }
                // END:: GET IMAGES

                // START:: PRODUCT MISC
                $itemColors = '';
                $materials  = '';
                $gender     = '';
                $attributes = $singleProductArr['attributes'];
                foreach ($attributes as $attribute) {
                    if ($attribute['name_en'] == 'Colour') $itemColors .= $attribute['value_de'].',';
                    if ($attribute['name_en'] == 'Material') $materials .= $attribute['value_de'].',';
                    if ($attribute['name_en'] == 'Gender') $gender .= $attribute['value_de'].',';
                }
                $materials = substr($materials, 0, -1);
                $itemColors = substr($itemColors, 0, -1);
                $gender = substr($gender, 0, -1);
                // END:: PRODUCT MISC
                $old_product = $old_products->where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)->where('ean',$singleProductArr['ean'])->first();

                $productsynhistories = [];
                $product_uvp = ( $singleProductArr['consumer_price'] + ( $singleProductArr['consumer_price'] * 0.10 )) ?? 0;
                $product_shipping_cost = $shippingCostArray[$singleProductArr['shipping_size']] ?? $shippingCost;

                $new_shipping_with_percentage = $product_shipping_cost + ( $product_shipping_cost * 0.10 );
                if($new_shipping_with_percentage > 35){
                    $product_vk_price  = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($singleProductArr['price'], $calculation, $product_uvp, 35);
                    $product_vk_price += $new_shipping_with_percentage - 35;
                    $new_shipping_with_percentage = 35;
                }else{
                    $product_vk_price = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($singleProductArr['price'], $calculation, $product_uvp, $new_shipping_with_percentage);
                }

                $local_category_id = !empty($categories[$singleProductArr['categories'][0]['id']]) ? $categories[$singleProductArr['categories'][0]['id']] : '';
                $im_handel = 0;
                if(isset($local_category_im[$local_category_id]) && $local_category_im[$local_category_id] > 0){
                    $im_handel =  $product_vk_price + ( ($product_vk_price * $local_category_im[$local_category_id]) / 100) ?? 0;
                }

                $brand_id = $brands[strtoupper($singleProductArr['brand'])] ?? '';

                if(isset($old_product)){

                    $old_stock = $old_product->stock;
                    $old_ek_price = $old_product->ek_price;
                    if($old_stock != $singleProductArr['stock']){
                        $old_product->stock             = $singleProductArr['stock'];
                        $old_product->old_stock        = $old_stock;
                        $old_product->stock_updated_at  = \Carbon\Carbon::now();

                        $productsynhistories['stock'] = $singleProductArr['stock'];
                        $productsynhistories['old_stock'] = $old_stock;
                        $productsynhistories['stock_updated_at'] = \Carbon\Carbon::now();

                        if($old_stock > $singleProductArr['stock']){
                            $discres_stock = $old_stock - $singleProductArr['stock'];
                            $salesTrac[] = [
                                'marketplace_product_id'    => $old_product->id,
                                'sales_stock'               => $discres_stock,
                                'sales_amount'              => $discres_stock * $old_product->ek_price,
                                'created_at'                => \Carbon\Carbon::now(),
                            ];
                        }


                    }

                    if($old_ek_price != $singleProductArr['price']){
                        $old_product->ek_price       = $singleProductArr['price'];
                        $old_product->vk_price       = $product_vk_price;
                        $old_product->im_handel      = $im_handel;
                        $data['im_handel']            = $im_handel;

                        $productsynhistories['ek_price'] = round($product_vk_price,2);
                    }

                    $drm_products = $old_product->drmProducts;

                    if($old_stock != $singleProductArr['stock'] || $old_ek_price != $singleProductArr['price'] ){

                        if(count($drm_products) > 0){
                            
                            $data['vk_price'] = round($product_vk_price,2);
                            $data['stock'] = $singleProductArr['stock'];
    
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                           
                            info('bike api update drm - '.$old_product->ean);
                        }

                        $old_product->misc  = $singleProductArr;
                        $old_product_updated = $old_product->update();

                        if(!empty($productsynhistories)){
                             $product_sync_datas[] =[
                                'marketplace_product_id' => $old_product->id,
                                'event'                  => 'update',
                                'tries'                  => 0,
                                'status'                 => 1,
                                'metadata'               => json_encode($productsynhistories),
                                'synced_at'              => \Carbon\Carbon::now(),
                                'created_at'             => \Carbon\Carbon::now(),
                            ];
                        }


                        if($old_product_updated){
                            Log::info('bike api update - '.$old_product->ean);
                        }else{
                            Log::info('bike api not update');
                        }


                    }else{
                        Log::info('bike api not update');
                    }

                }else{
                    $status_set_attributes = [
                        'item_number'   => $singleProductArr['ean'] ?? '', 
                        'brand'         => $brand_id ?? '', 
                        'ek_price'      => $singleProductArr['price'], 
                        'uvp'           => $product_uvp ?? 0, 
                        'delivery_days' => $deliveryDays, 
                        'vat'           => null
                    ];
                    // START :: BUILDING PRODUCTS ATTRIBUTES
                    $attributes = [
                        'api_id' => \App\Enums\Marketplace\ApiResources::BIKE_API_ID,
                        'api_product_id' => $singleProductArr['id'] ?? null,
                        'item_number'    => $singleProductArr['ean'] ?? '',
                        'name'           => $singleProductArr['name_de'] ?? '',
                        'brand'          => $brand_id ?? '',
                        'ean'            => $singleProductArr['ean'],
                        'ek_price'       => $singleProductArr['price'],
                        'vk_price'       => $product_vk_price,
                        'uvp'            => $product_uvp ?? 0,
                        'description'    => $singleProductArr['description_de'] ?? '',
                        'image'          => !empty($images) ? $images : [],
                        'stock'          => $singleProductArr['stock'],
                        'supplier_id'    => $supplierId,
                        'delivery_company_id' => $deliveryCompanyId,
                        'category_id'    => $local_category_id,
                        'api_category_id'=> $singleProductArr['categories'][0]['id'] ?? '',
                        'status'         => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($singleProductArr['name_de'],$singleProductArr['description_de'],$images,$singleProductArr['stock'],$status_set_attributes),
                        'item_weight'    => '',
                        'item_size'      => '',
                        'item_color'     => $itemColors ?? '',
                        'gender'         => $gender ?? '',
                        'materials'      => $materials ?? '',
                        'tags'           => '',
                        'note'           => '',
                        'production_year'=> '',
                        'delivery_days'  => $deliveryDays,
                        'collection_id'  => 0,
                        'shipping_method'=> \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                        'shipping_cost'  => $new_shipping_with_percentage,
                        'real_shipping_cost' => $product_shipping_cost,
                        'category'       => [],
                        'internel_stock' => 0,
                        'misc'           => $singleProductArr,
                        'im_handel'      => $im_handel,

                    ];
                    // END :: BUILDING PRODUCTS ATTRIBUTES
                    $valid_ean = app(ProductService::class)->validateEAN($attributes['ean']);
                    
                    if($valid_ean == true && !empty($attributes['name']) && !empty($attributes['ek_price']) && $attributes['stock'] > 1 && !empty($attributes['category_id'])){

                        $insertedModel = Product::updateOrCreate(['api_product_id' => $attributes['api_product_id'],'ean'=>$attributes['ean']], $attributes);

                        $this->duplicateService->duplicateEanCheck($attributes['ean']);

                        if (!$insertedModel) Log::error($attributes);
                        Log::info('inserted - '.$singleProductArr['id']);
                        $insertedProductsCount += $insertedModel ? 1 : 0;
                    }
                    // if (!$insertedModel) Log::error($singleProductArr);
                    // Log::info('inserted - '.$singleProductArr['id']);
                    // $insertedProductsCount += $insertedModel ? 1 : 0;

                }
            } catch (\Exception $e) {
                dd($e);
                Log::info($e->getMessage());
            }

        }

        if(!empty($product_sync_datas)){
            ProductSyncHistory::insert($product_sync_datas);
        }
        if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        return response()->json([
            'status'                    => 1,
            'products_created_count'    => $insertedProductsCount,
        ]);
    }


    public function getCategoryIdOfApiProduct ($apiCatId)
    {
        //local evn category for test
        $localArrOwnMachin = [
            1 => 15, 2 => 15, 3 => 13, 4 => 15, 5 => 15, 6 => 16, 7 => 15,
            8 => 17, 9 => 18, 10 => 19, 11 => 19, 12 => 20, 13 => 21, 14 => 22,
        ];
        //Team env category for test
        $localArr = [
            1 => 34, 2 => 34, 3 => 33, 4 => 34, 5 => 34, 6 => 37, 7 => 38,
            8 => 14, 9 => 7, 10 => 22, 11 => 22, 12 => 28, 13 => 25, 14 => 11,
        ];

        //production env category
        $liveArr = [
            1 => 34, 2 => 34, 3 => 33, 4 => 34, 5 => 34, 6 => 36,
            7 => 34,
            8 => 14, 9 => 9, 10 => 22, 11 => 22, 12 => 28, 13 => 25, 14 => 11,

        ];
        $arr = (env('APP_ENV') == 'local') ? $localArr : $liveArr;
        try {
            $category = \App\Models\Marketplace\Category::where('id', $arr[$apiCatId])->first();
        } catch (\Exception $e) {

        }

        return $category->id ?? 1;
    }

    public function update_MP_DRM_Stock ($product, $newStock) {
        DB::beginTransaction();
        try {
            $drmProducts = \App\Models\DrmProduct::where('marketplace_product_id', $product->id)->where('ean', $product->ean)->get();
            // Update MP product stock
            $mpProductUpdateStatus = $product->update([
                'old_stock' => $product->stock,
                'stock'     => $newStock,
            ]);

            if(count($drmProducts) > 0){
                $data['stock'] = $newStock;
                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drmProducts,$data);
            }

            DB::commit();
            return response()->json([
                'mp_product_update_status' => $mpProductUpdateStatus
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'error' => $e->getMessage(),
            ]);
        }


    }

    public function changeAttributeEnglishToDe($apiProductsArr, $insertLimit = null)
    {
        foreach ( $apiProductsArr as $singleProductArr ) {

            // START:: PRODUCT MISC
            $itemColors = '';
            $materials  = '';
            $gender     = '';
            $attributes = $singleProductArr['attributes'];
            foreach ($attributes as $attribute) {
                if ($attribute['name_en'] == 'Colour') $itemColors .= $attribute['value_de'].',';
                if ($attribute['name_en'] == 'Material') $materials .= $attribute['value_de'].',';
                if ($attribute['name_en'] == 'Gender') $gender .= $attribute['value_de'].',';
            }

            $materials = substr($materials, 0, -1);
            $itemColors = substr($itemColors, 0, -1);
            $gender = substr($gender, 0, -1);

            // START :: BUILDING PRODUCTS ATTRIBUTES
            $updateableAttributes = [
                'materials'      => $materials ?? '',
                'item_color'     => $itemColors ?? '',
                'gender'         => $gender ?? '',
            ];

            $mpProduct = Product::where('api_product_id',  $singleProductArr['id'])->first();
            if ($mpProduct) {
                $updatedStatus = $mpProduct->update($updateableAttributes);
            } else {
              continue;
            }


            if ($updatedStatus){
              Log::info('Product ID: '.$mpProduct->id);
              Log::info('Color: '.$mpProduct->item_color);
            }
        }
    }

    public function updateProductAttr()
    {
        $nextAttrUpdatePage = BikeApiCredential::first()->next_update_page;

        $allProducts = [];
        try {
            foreach ($pages as $page) {
                try{
                    $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;
                    $response = $this->apiService->fetchData($apiAddress);

                    if (!array_key_exists('data', $response) || empty($response['data'])) {
                        continue;
                    } else {
                        $allProducts = array_merge($allProducts, $response['data']);
                        $this->apiService->ch = curl_init();
                    }
                } catch (\Exception $e) {
                    return $e->getMessage();
                }
            }
            // Insert products to marketplace database
            $this->apiService->pushProductsFromApi($allProducts);

            // Update attributes english to de
            // $this->apiService->changeAttributeEnglishToDe($allProducts);

            $row->next_import_page = $targetPage;
            $row->save();

        } catch (\Exception $e) {
            return 'Got exception:: '.$e->getMessage();
        }
    }

    public function shippingCostUpdateByCsv(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $startTime  = microtime(true);

        $api_id  = \App\Enums\Marketplace\ApiResources::BIKE_API_ID;
        $api_csv = MarketplaceApiCsv::where('api_id',$api_id)->where('is_update',0)->first();

        if(!empty($api_csv)){
                $type       = 'csv';
                $csv_array  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($api_csv->csv_url, $type, 'auto', false);
                $api_shipping_size  = collect($csv_array)->pluck('shipping_size','ean')->toArray();

                $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();
                $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();

                $shipping_cost = [];
                foreach($api_shipping_size as $ean => $shipping_size){
                    $shipping_cost[$ean]= $this->shippingCostArray($shipping_size);
                }

                $local_product = Product::where('api_id', $api_id)->pluck('real_shipping_cost','ean')->toArray();
                
                if(!empty($shipping_cost)){
                    $new_shipping_costs  = array_diff_assoc($shipping_cost,$local_product);
                    $array_ean =[];

                    foreach($new_shipping_costs as $ean => $shipping_cost){
                        if(array_key_exists($ean,$local_product)){
                            $array_ean[] = $ean;
                        }
                    }
                    $count = 0;
                    foreach(array_chunk($array_ean,500) as $ean){
                        $local_products = Product::with('drmProducts')
                                                    ->select('id','ean','uvp','api_id','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','im_handel','category_id','item_number','shipping_cost','update_status','real_shipping_cost','misc')
                                                    ->where('api_id', $api_id)
                                                    ->whereIn('ean',$ean)
                                                    ->get();
                        
                        foreach($local_products as $product){
                            if($product->real_shipping_cost !=  $new_shipping_costs[$product->ean] ){

                                app(\App\Services\Marketplace\ProductService::class)->mpNewPriceCalculation($product, $product->ek_price, $calculation, $local_category_im, $new_shipping_costs[$product->ean], false);
                                // $product->shipping_cost =  $new_shipping_costs[$product->ean];
                                
                                // $drm_products = $product->drmProducts;
                                // if(count($drm_products) > 0){
                                //     $data['shipping_cost'] = round($new_shipping_costs[$product->ean],2);
                                //     app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                                //     info("Bike shipping DRM Price sync-".$product->ean);
                                // }
                                
                                // $product->update();
        
                                info("Bike API shipping cost sync-".$product->ean);
                                $count++;
                            }else{
                                info("Bike API shipping cost not update-".$product->ean);
                            }
                        }
                    }
        
                    $api_csv->update(['is_update'=>1]);
                    $ex_time =  (microtime(true) - $startTime) ." seconds";
        
                    info($ex_time. "</br>" ."Bike Api shipping cost fixed on: " .$count. " Products");
                    return response()->json([
                        'status'      => true,
                        'message'     => $ex_time. "</br>" ."Bike Api shipping cost fixed on: " .$count. " Products",
                    ]);

                }else{
                    $api_csv->update(['is_update'=>1]);
                    $ex_time =  (microtime(true) - $startTime) ." seconds";
        
                    return response()->json([
                        'status'      => true,
                        'message'     => $ex_time. "</br>" ."Bike Api shipping cost not Products",
                    ]);
                }
        }else{
            MarketplaceApiCsv::where('api_id',$api_id)
                                ->update(['is_update'=>0]);
            return response()->json([
                'status'      => false,
                'message'     => 'No CSV found',
            ]);
        }
        
    }

    private function shippingCostArray($size){
        $shippingCost = [ 
                            'S'=>7.99,
                            'XS' => 7.99,
                            'M' => 11.99,
                            'L' => 36.99,
                            'P' => 97.99,
                            'XL' => 97.99
                        ];
        
        return $shippingCost[$size] ?? 7.99;
    }

    public function syncStockOutProducts(){

        $api_id       = \App\Enums\Marketplace\ApiResources::BIKE_API_ID;
        $path         = \App\Enums\Marketplace\ApiResources::BIKE_API_STOCK_CSV_URL;
        $csv_string   = file_get_contents($path);
        $csv_array    = app(\App\Http\Controllers\Marketplace\CollectionController::class)->generateArray($csv_string);
        $api_stocks   = collect($csv_array)->pluck('eancode')->toArray();

        if(count($api_stocks) > 100){
            $local_product = Product::where('api_id', $api_id)->where('stock','!=',0)->pluck('ean')->toArray();
            $dif_ean = array_diff($local_product,$api_stocks);

            $array_ean = [];
            foreach($dif_ean as $ean){
                if(!in_array($ean,$api_stocks)){
                    $array_ean[] = $ean;
                }
            }
            
            foreach(array_chunk($array_ean ,1500) as $ean){

                $products = Product::with('drmProducts')
                                    ->select('api_id','stock','ean','id','old_stock','stock_updated_at')
                                    ->where('api_id', $api_id)
                                    ->whereIn('ean',$ean)
                                    ->get();
                
                foreach($products as $product){
    
                    if($product->stock != 0 ){
                        $old_stock = $product->stock;
                        $product->stock             = 0;
                        $product->old_stock         = $old_stock;
                        $product->stock_updated_at  = \Carbon\Carbon::now();
    
                        $drm_products = $product->drmProducts;
                        if(count($drm_products) > 0){
                            $data['stock'] = 0;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                            Log::info("Bike API Drm stock out product sync-".$product->ean);
                        }
                        $product->update();
                        Log::info("Bike API stock out product sync-".$product->ean);
                    }
                }
            }

        }else{
            Log::info("Product not found");
        }

        Log::info("Bike API stock out product sync completed.................");
        
    }

    public function newBuildRequest($apiAddress, $method='GET', $data=[]){

        try {
            curl_setopt($this->ch, CURLOPT_URL,htmlspecialchars_decode($apiAddress));
            curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($this->ch, CURLOPT_POST, $method=="POST"?1:0);
            curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
            if ( !empty($data) && $method=="POST" ) {
                curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
            }
   
            $response    = curl_exec ($this->ch);
            $status_code = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
            curl_close ($this->ch);
            $this->ch = curl_init();

            $response = json_decode($response, 1);
            $response['status_code'] = $status_code;

            return $response;
        } catch (\Exception $e) {
            $error_response = [
                'status_code' => $status_code,
                'message'       => $response,
                'error'         => $e->getMessage(),
            ];
            return $error_response;
        }
    }

}
