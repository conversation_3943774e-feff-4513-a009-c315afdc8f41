<?php

    namespace App\Services\Marketplace;

    use App\Services\BaseService;
    use App\Models\Marketplace\Product;
    use Illuminate\Support\Facades\Redis;
    use Log;
    use DB;

    class DuplicateEanService extends BaseService{

		public $product_ean;

		public function __construct ()
		{
			// $this->product_ean = $ean;
		}

        public function duplicateEanCheck($ean) {

            $is_exest_product = Product::where('ean',$ean)->get();
            if((count($is_exest_product) > 1)){
                // Get duplicate Other product without best one
                // Other Duplicate product Pending and is_dublicate set
                try{
                    foreach($is_exest_product as $duplicate_ean_product){
                        \App\Models\Marketplace\Product::where('ean',$duplicate_ean_product->ean)->update([
                            'status'       =>0,
                            'is_dublicate' =>1,
                            'best_price'   =>0
                        ]);
                    }
                    //Get duplicate to best product bese on ek_price + shipping_cost
                    $get_best_product = Product::where('ean',$ean)->where('stock','>=',1)->whereNotIn('status', [2, 7])->orderBy(DB::raw("`ek_price` + `shipping_cost`"), 'asc')->first();
                    if($get_best_product != null){
                        // Duplicate to Live best one product
                        Product::where('id',$get_best_product->id)->update([
                            'is_dublicate' =>1,
                            'best_price'   =>1,
                            'status'       =>1,
                        ]);
                    }
                }catch(\Exception $e){

                }
            }
        }

        public function stockUpdateToActiveBest($productId = '', $ean ='',$value = 0){

            $mp_product = \App\Models\Marketplace\Product::where('id',$productId)->first();
            $mp_product_ean = $mp_product ? $mp_product->ean : '';
            $curent_live_product = \App\Models\Marketplace\Product::where('ean',$mp_product_ean)->where('is_dublicate',1)->where('best_price',1)->first();
            $mp_product_total_price = ($mp_product->ek_price ?? 0 + $mp_product->shipping_cost ?? 0);
            $curent_live_product_total_price = ($curent_live_product->ek_price ?? 0 +  $curent_live_product->shipping_cost ?? 0);

            if($mp_product->best_price == 1 && $mp_product->stock == 0){
                $next_posiable_live = \App\Models\Marketplace\Product::where('ean',$mp_product_ean)->where('id','!=',$mp_product->id)->where('stock','>=',1)->whereNotIn('status', [2, 7])->orderBy(DB::raw("`ek_price` + `shipping_cost`"), 'asc')->first();
                if($mp_product->stock < $next_posiable_live->stock){
                    try{
                        // Curent product pending bcz stock are low from another one
                        $mp_product->update([
                            'status' =>  \App\Enums\Marketplace\ProductStatus::PENDING,
                            'best_price'=> 0
                        ]);
                        // next possiable product is live bcz product stock are high from curent live
                        $next_posiable_live->update([
                            'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                            'best_price'=> 1
                        ]);
                    }catch(\Exception $e) {
                        print "!ops product not update base on best price".$e;
                    }
                }
            }else if(($mp_product->is_dublicate == 1 && $mp_product->best_price == 0)){

               if($mp_product->stock >= 1 && $curent_live_product_total_price > $mp_product_total_price ){
                    try{
                        // Curent Request product active bcz Live product total price to this product price is chipest
                        $mp_product->update([
                            'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                            'best_price'=>1
                        ]);
                        // Curent product is pending bcz Request product total price ar chipest
                        $curent_live_product->update([
                            'status' =>\App\Enums\Marketplace\ProductStatus::PENDING ,
                            'best_price'=>0
                        ]);
                    }catch(\Exception $e) {
                        print "!ops product not update base on best price".$e;
                    }
               }

            }
        }

    }


?>
