<?php


namespace App\Services\Marketplace;


use App\Models\Marketplace\Order;
use App\Services\BaseService;

class OrderService extends BaseService
{
    public function all(array $filters = [], $paginate = true)
    {
        $query = Order::query()->select('marketplace_orders.*');

        if(!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $paginate ? $query->paginate() : $query->get();
    }

    public function getById($id)
    {
        return Order::find($id);
    }

    public function store(array $data)
    {
        return $this->saveOrder($data);
    }

    public function update($id, array $data)
    {
        return $this->saveOrder($data, $id);
    }

    public function destroy($id)
    {
        return Order::find($id)->delete();
    }

    private function saveOrder($data, $id = null)
    {
        $order = Order::findOrNew($id);
        $order->fill($data);
        $order->save();

        return $order;
    }
}
