<?php


namespace App\Services\Marketplace\Plush;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Storage;

class PlushService extends BaseService
{

    public function insertProduct($products,$brands,$local_category_im){
        $delivery_company_id = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;
        $local_product = Product::select('id','ean','delivery_company_id')
                                ->whereIn('ean', array_column($products,'EAN'))
                                ->where('delivery_company_id', $delivery_company_id)
                                ->get();
                                
        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        $default_shipping_cost         = 5.50;
        $shipping_cost_with_percentage = $default_shipping_cost + ($default_shipping_cost * 0.10);

        $attributes = [];
        foreach ($products as $product){
            $old_products = $local_product->where('ean',$product['EAN'])->first();
            if($old_products){
                info("product already exist........");
                continue;
            }else{
                if($this->categoryMapping($product['KategorieNR2'])){
                    $category = $this->categoryMapping($product['KategorieNR2']);
                }else if($this->categoryMapping($product['KategorieNR1'])){
                    $category = $this->categoryMapping($product['KategorieNR1']);
                }else if($this->categoryMapping($product['KategorieNR3'])){
                    $category = $this->categoryMapping($product['KategorieNR3']);
                }else{
                    $category = '';
                }

                if(empty($product['EAN']) || empty($category) || empty($product['VKPreis1']) || empty($product['Bilddatei'])) continue;
                $images = ["https://shop.plus-h.de/images/thumbnail/produkte/large/".$product['Bilddatei']];

                $product_name = $product['Bezeichnung'] ?? '';
                $product_description = !empty($product['Produktbeschreibung']) ? $product['Produktbeschreibung'] : '';
                $product_stock = removeCommaFromPrice($product['AnzahlHauptartikel']) ?? 0;
                $product_uvp = ( removeCommaFromPrice($product['VKempfohlen']) + ( removeCommaFromPrice($product['VKempfohlen']) * 0.10) ) ?? 0;
                

                $vkPrice = app(\App\Http\Controllers\Marketplace\CollectionController::class)->calculatePrice(removeCommaFromPrice($product['VKPreis1']), $calculation, $product_uvp, $shipping_cost_with_percentage);

                $im_handel = 0;
                if(isset($local_category_im[$category]) && $local_category_im[$category] > 0){
                    $im_handel =  $vkPrice + (($vkPrice * $local_category_im[$category]) / 100);
                }

                $attributes[] = [
                    'item_number'           => $product['Artikel'] ?? '',
                    'name'                  => $product_name,
                    'brand'                 => $brands[strtoupper($product['MarkenName'])] ?? $product['MarkenName'],
                    'ean'                   => $product['EAN'],
                    'ek_price'              => removeCommaFromPrice($product['VKPreis1']),
                    'vk_price'              => $vkPrice,
                    'uvp'                   => $product_uvp,
                    'description'           => $product_description,
                    'image'                 => json_encode($images) ?? '',
                    'stock'                 => $product_stock,
                    'supplier_id'           => 2769,
                    'delivery_company_id'   => $delivery_company_id,
                    'category_id'           => $category,
                    'status'                => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name,$product_description,$images,$product_stock),
                    'item_weight'           =>  '',
                    'item_size'             => $product['Artikelmass'],
                    'item_color'            => $product['Farbe'],
                    'gender'                => '',
                    'materials'             => !empty($product['Material']) ? $product['Material'] : '',
                    'vat'                   => removeCommaFromPrice($product['ProzentMwST']),
                    'tags'                  => '',
                    'note'                  => '',
                    'production_year'       => '',
                    'delivery_days'         => 2,
                    'collection_id'         => 0,
                    'shipping_method'       => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                    'shipping_cost'         => $shipping_cost_with_percentage,
                    'real_shipping_cost'    => $default_shipping_cost,
                    'internel_stock'        => 0,
                    'created_at'            => \Carbon\Carbon::now(),
                    'im_handel'             => $im_handel ?? 0,
                ];
            }
        }
        if(count($attributes) > 0){
//            $local_product = new Product();
//            $local_product->setConnection('drm_team')->insert($attributes);
            Product::insert($attributes);
            info("Plush product inserted");
            $attributes = [];
        }
        return true;
    }

    private function categoryMapping($category_name){
        $category = [
            "Sets"              => 1073,
            "Wolle"             => 1047,
            "Filzen"            => 1069,
            "Nadeln"            => 1039,
            "Perlen"            => 1076,
            "Sticken"           => 1070,
            "Viskose"           => 1059,
            "Nähgarn"           => 1045,
            "Bündchen"          => 1053,
            "Nähgarne"          => 1045,
            "Bastelsets"        => 1061,
            "Fellpompon"        => 1081,
            "Häkelgarne"        => 1043,
            "Lederimitat"       => 1057,
            "Strickgarne"       => 1049,
            "Sweatstoffe"       => 1058,
            "Wattekugeln"       => 1078,
            "Fleecestoffe"      => 1054,
            "Handarbeiten"      => 1055,
            "Häkelnadeln"       => 1064,
            "Jerseystoffe"      => 1056,
            "Spezialgarne"      => 1046,
            "Stricknadeln"      => 1068,
            "Strumpfgarne"      => 1051,
            "Schnittmuster"     => 1062,
            "Baumwollstoffe"    => 1052,
            "Handstickgarne"    => 1070,
            "Stabilisatoren"    => 1060,
            "Schulterpolster"   => 1042,
            "Gardinenzubehör"   => 1034,
            "Gürtelschnallen"   => 1082,
            "Handarbeitsstoffe" => 1055,
            "Reißverschlüsse"   => 1063,
            "Handarbeitsgeräte"             => 1071,
            "Mieder und Dessous"            => 1038,
            "Maschinenstickgarne"           => 1044,
            "Taschen und Zubehör"           => 1083,
            "Bücher und Anleitungen"        => 1048,
            "Broschen und Haarklemmen"      => 1080,
            "Bänder, Borten, Spitzen"       => 1033,
            "Acryl- und Styroporformen"     => 1074,
            "Nähkörbe und Aufbewahrung"     => 1072,
            "Applikationen (Bügelbilder)"   => 1079,
            "Bastelfilz und Füllmaterial"   => 1075,
            "Maschinennadeln und Zubehör"   => 1065,
            "Scheren und Schneidwerkzeuge"  => 1041,
            "Steck- und Sicherheitsnadeln"  => 1067,
            "Schmuckzubehör und Werkzeuge"  => 1077,
            "Hosenträger / Ärmel- und Sockenhalter"             => 323,
            "Mess- und Markierungswerkzeuge / Patchwork"        => 1037,
            "Näh-, Stick- und Stopfnadeln und Zubehör"          => 1066,
            "Knöpfe, Bekleidungsverschlüsse und Nieten"         => 1036,
            "Reparatur, Ersatz, Pflege, Schutz und Praktisches" => 1040
        ];

        if(isset($category[$category_name])){
            return $category[$category_name];
        }else{
            return false;
        }

    }

    public function findProductImage($image_name){

//        if(file_exists(storage_path('app/plush/'.$image_name))){
//            $url =  storage_path('app/plush/'.$image_name);
//            $fileContent = @file_get_contents($url);
            $fileName = 'marketplace-plush-image/'.$image_name;

//          Storage::disk('spaces')->put($fileName, $fileContent, 'public');
            $imagesUrl = Storage::disk('spaces')->url($fileName) ?? null;
            return $imagesUrl;
//        }else{
//            return false;
//        }
    }
}
