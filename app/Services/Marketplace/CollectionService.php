<?php


namespace App\Services\Marketplace;


use App\Models\Marketplace\Collection;
use App\Services\BaseService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\LazyCollection;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;
use CRUDBooster;

class CollectionService extends BaseService
{
    public function all(array $filters = [], $paginate = true)
    {
        $query = Collection::query();

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['supplier_id'])) {
            $query->where('supplier_id', $filters['supplier_id']);
        }

        if (!empty($filters['excepts'])) {
            $query->whereNotIn('id', $filters['excepts']);
        }

        if (!empty($filters['include'])) {
            $query->whereIn('id', $filters['include']);
        }

        if (!empty($filters['q'])) {
            $query->where(\DB::raw('LOWER(name)'), 'like', '%' . strtolower($filters['q']) . '%');
        }

        return $paginate ? $query->paginate(request()->get('limit', 20)) : $query->get();
    }

    public function getById($id)
    {
        return Collection::find($id);
    }

    public function store(array $data)
    {
        return $this->saveCollection($data);
    }

    public function update($id, array $data)
    {
        return $this->saveCollection($data, $id);
    }

    public function destroy($id)
    {
        return Collection::find($id)->delete();
    }

    public function storeFileFromUrl($url)
    {
        $extension = pathinfo($url, PATHINFO_EXTENSION);
        $filePath = 'uploads/' . date('Y-m');
        Storage::makeDirectory($filePath);

        //Move file to storage
        $filename = md5(Str::random(5)) . '.' . $extension;
        $url_filename = $filePath . '/' . $filename;
        if (Storage::put($url_filename, file_get_contents($url))) {
            return $url_filename;
        }

        return false;
    }

    public function storeFileFromUrl_($url)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);

        $extension = pathinfo($url, PATHINFO_EXTENSION);
        // $filePath = 'uploads/' . date('Y-m');
        $url_file = trim($url);
        $message = ['text' => 'Getting Data From : <i>' . substr($url, 0, 40) . '...</i>', 'percent' => '50'];
        // event(new progressEvent($message));
        sentProgress($message, 'import');

        $csv_data = getRemoteFile($url_file);

        $unique_url = 'marketplace-collections/' . CRUDBooster::myId() . '/' . md5(Str::random(5)) . '.' . $csv_data['ext'];
        if(Storage::disk('spaces')->put($unique_url, $csv_data['data'], 'public')){
            return Storage::disk('spaces')->url($unique_url);
        }
        return false;
        //Move file to storage
        // $filename = md5(Str::random(5)) . '.' . $extension;
        // $url_filename = $filePath . '/' . $filename;
        // if (Storage::put($url_filename, file_get_contents($url))) {
        //     return $url_filename;
        // }


    }

    public function getArrayFromCSVUrl($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $data = trim(curl_exec($ch));
        $mime = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        if ($mime) {
            $ext = mime_to_ext($mime);
        } else {
            $ext = pathinfo($url, PATHINFO_EXTENSION);
        }
        curl_close($ch);
        $result = [
            'data' => makeUtf8($data),
            'ext' => $ext
        ];
        return $result;
    }

    public function csvToArray($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0');
        ini_set('memory_limit', -1);

        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);

        foreach ($paths as $path) {
            $localpath = $path;
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }
            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
            $collection = LazyCollection::make($spreadsheet);

            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(0);
            foreach ($collection as $row) {

                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }
            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }

        return $array;
    }

    private function saveCollection($data, $id = null)
    {
        $collection = Collection::findOrNew($id);
        $collection->fill($data);
        // Add supplier id
        // $collection->supplier_id = session()->get('supplier_id')??\CRUDBooster::myParentId();

        $collection->save();
        return $collection;
    }

    public function syncCollectionProducts($collectionId)
    {
        $collection = Collection::find($collectionId);

    }

    public function array_chank($datas = [], $slice = 10000)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        return array_chunk($datas, $slice, true);
    }

    public function csvToCsvHeaderJson($file, $delimiter, $cloud = true, $initial = false)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $file);
        $count = 0;
        $rand = Str::random(40);
        foreach ($paths as $path) {
            $file_type = pathinfo($path, PATHINFO_EXTENSION);
            try {
                if ($cloud == true) {
                    // $path = Storage::disk('spaces')->url($path);
                    file_put_contents($rand . '.' . $file_type, fopen($path, 'r'));
                    $path = $rand . '.' . $file_type;
                }

                if ($file_type == 'csv' || $file_type == 'txt') {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Csv');
                    if ($delimiter != 'auto') {
                        $reader->setDelimiter($delimiter);
                    }
                    $spreadsheet = $reader->load($path);
                } else {
                    $spreadsheet = IOFactory::load($path);
                }
                $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
                $total = count($spreadsheet);
                for ($i = 0; $i <= 5; $i++) {
                    if ($total >= $i + 1) {
                        $collection[$i] = $spreadsheet[$i];
                    }
                }
                $valid = $this->validateFile($collection);
                if ($valid == false) {
                    if ($cloud == true) {
                        unlink($path);
                    }
                    return false;
                }
            } catch (\Exception $e) {
                return false;
            }
            $demoFinal = makeArrayUtf8(makeArrayUtf8($spreadsheet[1]));
            Request::session()->put('demoData', json_encode($demoFinal));
        }
        if ($cloud == true) {
            unlink($path);
        }
        if ($initial) {
            return $spreadsheet;
        } else {
            $headers = makeArrayUtf8(makeArrayUtf8($spreadsheet[0]));
            $headers = array_map('removeDots', $headers);
            return json_encode($headers);
        }
    }

    public function validateFile($collection): bool
    {
        $valid = true;
        $count = 0;
        foreach ($collection as $key => $value) {
            if ($key == 0) {
                if (containsOnlyNull($value)) {
                    $valid = false;
                }
                if (hasBigString($value)) {
                    $valid = false;
                }
                if (count($value) < 2) {
                    $valid = false;
                }
                $valid = checkArrayKey($value, $collection);

                // if(arrayNullCount($value)>4){
                // 	$valid = false;
                // }
            } else {
                // if(containsOnlyNull($value)){
                // 	$count++;
                // }
                $utf_8 = makeArrayUtf8(makeArrayUtf8($value));

                if (json_encode($utf_8) == false) {
                    $valid = false;
                }
            }
        }
        // if(count($collection)==5 && $count>2){
        // 	$valid = false;
        // }
        return $valid;
    }
}
