<?php


namespace App\Services\Marketplace\Vidaxl;

use App\Models\Marketplace\ApiCredential;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\BaseService;

class VidaxlApiService extends BaseService
{
    public $api_user_name;
    public $api_token;
    public $headers;
    public $api_address;
    public function __construct()
    {
        $apiCredentials     = ApiCredential::where('api_id', \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID)->first();
        $this->api_address    = $apiCredentials->api_address;
        $this->api_user_name   = $apiCredentials->api_user_name;
        $this->api_token       = $apiCredentials->token;


        $this->headers           = [
            'Content-Type: application/json',
        ];
    }

    public function buildRequest($apu_url, $data = [], $method = 'GET')
    {
        if(!isLocal()){
            $url = $this->api_address . '' . $apu_url;

            $ch     = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, $this->api_user_name . ":" . $this->api_token);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (!empty($data) && $method == "POST") {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }

            $resp['data'] = curl_exec($ch);
            $resp['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return $resp;
        }else{
            dd('You are in local environment');
        }

        
    }

    public function buildRequestForVidaxlATCountry($apu_url, $data = [], $method = 'GET')
    {
        if(!isLocal()){
            $url = $this->api_address . '' . $apu_url;
            $vidaxlATUserName = "<EMAIL>"; //"<EMAIL>";
            $vidaxlAtToken = "c17d06f6-8414-4e7d-87be-e02f0dfd34ad"; // "2c2a8206-3f70-4acd-9e85-cb6c871d8942";

            $ch     = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, $vidaxlATUserName . ":" . $vidaxlAtToken);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (!empty($data) && $method == "POST") {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }

            $resp['data'] = curl_exec($ch);
            $resp['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return $resp;
        }else{
            dd('You are in local environment');
        }
 
    }

    public function buildRequestForVidaxlESCountry($apu_url, $data = [], $method = 'GET')
    {
        if(!isLocal()){
            $url = $this->api_address . '' . $apu_url;
            $vidaxlATUserName = "<EMAIL>";
            $vidaxlAtToken    = "48046243-abda-4690-9df1-8bb4fb3f2665";

            $ch     = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, $vidaxlATUserName . ":" . $vidaxlAtToken);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (!empty($data) && $method == "POST") {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }

            $resp['data'] = curl_exec($ch);
            $resp['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return $resp;
        }else{
            dd('You are in local environment');
        }
        
    }

    /**
     * @param $user_email
     * @param $token
     * @param $apu_url
     * @param array $data
     * @param string $method
     * @return array
     */
    public function buildRequestVidaxlByCountry(
        string $user_email,
        string $token,
        string $apu_url,
        $data = [],
        string $method = 'GET'
    ) {
        if(!isLocal()){
            $url = $this->api_address . '' . $apu_url;

            $ch     = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, $user_email . ":" . $token);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if (!empty($data) && $method == "POST") {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }

            $resp['data'] = curl_exec($ch);
            $resp['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return $resp;
        }else{
            dd('You are in local environment');
        }
        
    }
}
