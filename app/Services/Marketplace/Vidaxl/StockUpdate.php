<?php

namespace App\Services\Marketplace\Vidaxl;

use App\Enums\V2UserAccess;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\DB;

class StockUpdate
{
    private const API_ID = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;
    private const SHIPPING_METHOD = 1;
    private const CHUNK_SIZE = 1500;

    public function stockUpdate($api_product_ids, $new_stocks)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        foreach (array_chunk($api_product_ids, self::CHUNK_SIZE) as $eans) {

            $products = Product::with('drmProducts', 'drmProductsV2')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->whereIn('ean', $eans)
                ->select('id', 'ean', 'stock', 'old_stock', 'stock_updated_at')
                ->get();

            $salesTrac = [];
            $product_sync_data = [];
            foreach ($products as $product) {
                $n_stock = $new_stocks[$product->ean];

                if ($product->stock !=  $n_stock) {

                    $old_stock = $product->stock;

                    $product->stock             = $n_stock;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if ($old_stock > $n_stock) {

                        $discres_stock = $old_stock - $n_stock;
                        $salesTrac[] = [
                            'marketplace_product_id'    => $product->id,
                            'sales_stock'               => $discres_stock,
                            'sales_amount'              => $discres_stock * $product->ek_price,
                            'created_at'                => \Carbon\Carbon::now(),
                        ];
                    }

                    $updateableColumns['stock'] = $n_stock;
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $updateableColumns);
                        info("Vidaxl API DRM product stock sync for V1- " . $product->ean);
                    }

                    $drm_productsV2 = $product->drmProductsV2;
                    if ($drm_productsV2->isNotEmpty()) {
                        foreach ($drm_productsV2 as $drm_product) {

                            if (in_array($drm_product->user_id, V2UserAccess::USERS)) {
                                $product_sync_data[] = [
                                    'marketplace_product_id' => $drm_product->marketplace_product_id,
                                    'user_id'  => $drm_product->user_id,
                                    'country_id' => $drm_product->country_id,
                                    'metadata' => json_encode($updateableColumns),
                                    'status' => 1,
                                    'created_at' => \Carbon\Carbon::now(),
                                    'updated_at' => \Carbon\Carbon::now(),
                                ];
                            }
                        }

                        info("Vidaxl API country DRM product sync-" . $product->ean);
                    }

                    $product->update();

                    info("Vidaxl API country product sync-" . $product->ean);
                }
            }

            if (!blank($product_sync_data)) {
                DB::table('mp_product_sync_histories')->insert($product_sync_data);
                $product_sync_data = [];
            }
            if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        }
    }

    public function stockOutUpdate($stock_out_eans)
    {
        $existingMarketplaceIds = [];
        foreach (array_chunk($stock_out_eans, self::CHUNK_SIZE) as $eans) {

            $products = Product::with('drmProducts', 'drmProductsV2')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->whereIn('ean', $eans)
                ->select('id', 'ean', 'stock', 'old_stock', 'stock_updated_at', 'ek_price')
                ->get();

            $salesTrac = [];
            $product_sync_data = [];
            foreach ($products as $product) {
                if ($product->stock > 0) {

                    $old_stock = $product->stock;

                    $product->stock             = 0;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if (!array_key_exists($product->id, $existingMarketplaceIds)) {
                        $salesTrac[] = [
                            'marketplace_product_id' => $product->id,
                            'sales_stock'           => $old_stock,
                            'sales_amount'          => $old_stock * $product->ek_price,
                            'created_at'            => \Carbon\Carbon::now(),
                        ];
                        $existingMarketplaceIds[$product->id] = 0;
                    }

                    $data['stock'] = 0;
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                    }

                    $drm_productsV2 = $product->drmProductsV2;
                    if ($drm_productsV2->isNotEmpty()) {
                        foreach ($drm_productsV2 as $drm_product) {

                            if (in_array($drm_product->user_id, V2UserAccess::USERS)) {
                                $product_sync_data[] = [
                                    'marketplace_product_id' => $drm_product->marketplace_product_id,
                                    'user_id'  => $drm_product->user_id,
                                    'country_id' => $drm_product->country_id,
                                    'metadata' => json_encode($data),
                                    'status' => 1,
                                    'created_at' => \Carbon\Carbon::now(),
                                    'updated_at' => \Carbon\Carbon::now(),
                                ];
                            }
                        }
                    }

                    $product->update();
                    info("Vidaxl stock out sync-" . $product->ean);
                }
            }

            if (!blank($product_sync_data)) {
                DB::table('mp_product_sync_histories')->insert($product_sync_data);
                $product_sync_data = [];
            }
            if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        }
    }
}
