<?php

namespace App\Services\Marketplace\Vidaxl;

use League\Csv\Reader;
use App\Models\Marketplace\Product;
use App\Enums\Marketplace\MpProductCountry;
use App\Services\Marketplace\Vidaxl\StockUpdate;

class StockSyncProcess
{
    private const API_ID = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;
    private const SHIPPING_METHOD = 1;

    private function fetchApiProduct()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $url        = \App\Enums\Marketplace\ApiResources::VidaXLCsv;
        $csv_string = file_get_contents($url);
        $api_csv_products = $this->generateArray($csv_string);

        info('vidaxl csv load...........................................');

        $api_products = [];
        foreach ($api_csv_products as $api_csv_product) {
            $api_products[$api_csv_product['EAN']] = $api_csv_product['Stock'];
        }
        $api_csv_products = [];
        return $api_products;
    }

    public function generateArray($path)
    {
        $reader = Reader::createFromString($path);
        $reader->setHeaderOffset(0);
        return $reader->getRecords();
    }

    public function process()
    {
        $update_time = \Carbon\Carbon::now()->addMinutes(10);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule(self::API_ID, $update_time);

        $apiProducts = $this->fetchApiProduct();

        $local_products = Product::where([
            'api_id' => self::API_ID,
            'shipping_method' => self::SHIPPING_METHOD
        ])
            ->select('stock', 'ean', 'country_id')
            ->get();

        $merged_api_ids = $this->syncStockForCountries($local_products, $apiProducts);

        app(StockUpdate::class)->stockUpdate($merged_api_ids, $apiProducts);
        $this->getStockOutProductIds($local_products, $apiProducts);
    }

    /**
     * Synchronize product stock for specified countries.
     *
     * @param \Illuminate\Support\Collection $localProducts
     * @param array $apiStock
     * @return array
     */
    private function syncStockForCountries($localProducts, $apiProducts): array
    {
        $country_ids = [
            MpProductCountry::GERMANY => 'Germany',
            MpProductCountry::SPAIN => 'Spain',
            MpProductCountry::SWITZERLAND => 'Switzerland',
            MpProductCountry::AUSTRIA => 'Austria'
        ];

        $merged_api_ids = [];

        foreach ($country_ids as $country_id => $country_name) {
            info("Vidaxl $country_name stock sync process..............");

            $local_ids = $localProducts->where('country_id', $country_id)->pluck('stock', 'ean')->toArray();
            $new_stocks = array_diff_assoc($apiProducts, $local_ids);
            $apiProductIdsToUpdate = array_keys(array_intersect_key($new_stocks, $local_ids));

            if (!empty($apiProductIdsToUpdate)) {
                $merged_api_ids = array_merge($merged_api_ids, $apiProductIdsToUpdate);
            } else {
                info("No stock updates required for $country_name.");
            }
        }

        return array_unique($merged_api_ids);
    }

    private function getStockOutProductIds($local_products, $apiProducts)
    {
        $local_product_id = $local_products->where('stock', '!=', 0)->pluck('ean')->toArray();
        $stock_out_ids        = array_diff($local_product_id, array_keys($apiProducts));

        app(StockUpdate::class)->stockOutUpdate($stock_out_ids);
    }
}
