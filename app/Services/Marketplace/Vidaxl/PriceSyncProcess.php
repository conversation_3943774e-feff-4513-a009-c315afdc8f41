<?php

namespace App\Services\Marketplace\Vidaxl;

use App\Enums\Marketplace\ApiResources;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;

class PriceSyncProcess
{
    private const API_ID = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;

    public function syncDeProductPrice()
    {
        $country_id = 1;
        $url        = ApiResources::VIDAXL_CSVS['DE'];

        $this->syncProduct($url, $country_id);
    }

    public function syncChProductPrice()
    {
        $country_id = 83;
        $url        = ApiResources::VIDAXL_CSVS['CH'];

        $this->syncProduct($url, $country_id);
    }

    public function syncEsProductPrice()
    {
        $country_id = 8;
        $url        = ApiResources::VIDAXL_CSVS['ES'];

        $this->syncProduct($url, $country_id);
    }

    public function syncAtProductPrice()
    {
        $country_id = 74;
        $url        = ApiResources::VIDAXL_CSVS['AT'];

        $this->syncProduct($url, $country_id);
    }


    private function syncProduct($url, $country_id)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $csv_string = file_get_contents($url);
        info("Vidaxl API CSV file fetched");
        $api_products = [];
        foreach ($this->generateArray($csv_string) as $api_csv_product) {
            $api_products[$api_csv_product['EAN']] = $api_csv_product['B2B price'];
        }

        return $this->process($api_products, $country_id);
    }

    private function generateArray($path)
    {
        $reader = Reader::createFromString($path);
        $reader->setHeaderOffset(0);
        return $reader->getRecords();
    }

    private function process($api_products, $country_id)
    {
        $update_time = \Carbon\Carbon::now()->addMinutes(10);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule(self::API_ID, $update_time);
        info('vidaxl csv loaded');

        $local_products     = Product::where('api_id', self::API_ID)->where('country_id', $country_id)->pluck('ek_price', 'ean')->toArray();
        $new_price_ean      = array_diff_assoc(array_map('floatval', $api_products), array_map('floatval', $local_products));
        $array_ean          = array_keys(array_intersect_key($new_price_ean, $local_products));

        if (empty($array_ean)) {
            dd("No new price found");
        }
        $this->update($array_ean, $new_price_ean, $country_id);
    }

    private function update($array_ean, $new_price_ean, $country_id)
    {
        $calculation       = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

        foreach (array_chunk($array_ean, 2500) as $ean) {
            $local_mp_products = Product::with('drmProducts', 'drmProductsV2')
                ->select('id', 'ean', 'uvp', 'api_id', 'ek_price', 'old_ek_price', 'ek_price_updated_at', 'vk_price', 'old_vk_price', 'vk_price_updated_at', 'im_handel', 'category_id', 'item_number', 'shipping_cost', 'real_shipping_cost')
                ->where('api_id', self::API_ID)
                ->whereIn('ean', $ean)
                ->where('country_id', $country_id)
                ->get();

            foreach ($local_mp_products as $product) {
                $new_ek_price = $new_price_ean[$product->ean];

                if ($product->ek_price !=  $new_ek_price) {

                    app(\App\Services\Marketplace\ProductService::class)
                        ->mpNewPriceCalculation($product, $new_ek_price, $calculation, $local_category_im, $product->shipping_cost, true);
                }
            }
        }
    }
}
