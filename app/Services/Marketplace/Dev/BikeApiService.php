<?php 
	
	namespace App\Services\Marketplace\Dev;
	use Log;

	class BikeApiService {

		public $token;
		public $authEmail;
		public $authPassword;
		public $headers;
		public $ch;
		public $tokenUpdatedTime;
		public $apiCredentials;

		public function __construct ()
		{
			$apiCredentials         = \App\Models\Marketplace\ApiCredential::where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)
			                        ->first();

			if ( $apiCredentials->token_next_update_time < \Carbon\Carbon::now()  ) {
				
			    $apiCredentials = $this->updateToken($apiCredentials);

			    if (isset($apiCredentials['error'])) {
			        return $apiCredentials['msg'];
			    }
			}

			$this->token            = $apiCredentials->token;
			$this->authEmail        = $apiCredentials->api_user_name;
			$this->authPassword     = $apiCredentials->api_password;
			// $this->tokenUpdatedTime = $apiCredentials->token_updated_time;
			$this->apiCredentials   = $apiCredentials;

			$this->ch               = curl_init();

			$this->headers           = [
			    'Authorization: Bearer '.$this->token,
			    'Content-Type: application/json',
			    'Accept: application/json',
			];

		}

		public function updateToken ($apiCredentials)
		{
		    try {
		        $req = curl_init(\App\Enums\Marketplace\ApiTestResources::ADDRESSES['AUTH']);
		        curl_setopt($req, CURLOPT_RETURNTRANSFER, true);
		        curl_setopt($req, CURLOPT_POSTFIELDS, [
		            'email'         => $apiCredentials->api_user_name,
		            'password'      => $apiCredentials->api_password,
		        ]);
		        $response = json_decode(curl_exec ($req), 1);

		        Log::info($response);
		        Log::info( date('Y-m-d H:i:s', $response['valid_until']) );

		        curl_close ($req);
		        $apiCredentials->old_token          = $apiCredentials->token ?? '';
		        $apiCredentials->token              = $response['token'] ?? $apiCredentials->token;
		        $apiCredentials->token_next_update_time = date('Y-m-d H:i:s', $response['valid_until'] ?? $apiCredentials->token_next_update_time);
		        $apiCredentials->save();

		        return $apiCredentials;

		    } catch (\Exception $e) {
		        return [
		            'staus' => 'error',
		            'msg'   => 'Api Token is not valid. Error:: '.$e->getMessage(),
		        ];
		    }
		}

		public function fetchData($apiAddress, $method="GET", $data=[])
		{
		    $response = $this->buildRequest($apiAddress, $method, $data)
		        ->getResponse();
		    return $response;
		}

		public function buildRequest ($apiAddress, $method='GET', $data=[]) {
		    curl_setopt($this->ch, CURLOPT_URL,htmlspecialchars_decode($apiAddress));
		    curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
		    curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
		    curl_setopt($this->ch, CURLOPT_POST, $method=="POST"?1:0);
		    curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);

		    if ( !empty($data) && $method=="POST" ) {
		        curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
		    }
		    return $this;
		}

		public function getResponse ()
		{
		    try {
		        $response = curl_exec ($this->ch);
		        curl_close ($this->ch);
		        $this->ch = curl_init();
		        dd($response);
		        return json_decode($response, 1);
		    } catch (\Exception $e) {
		        return $e->getMessage();
		    }
		}



		public function pushProductsFromApi ($apiProductsArr, $insertLimit = null)
		{
		    $insertedProductsCount  = 0;
		    $deliveryCompanyId      = \App\Enums\Marketplace\ApiResources::BIKE_API_DELIVERY_COMPANY_ID;
		    $shippingCost           = \App\Enums\Marketplace\ApiResources::BIKE_API_DEFAULT_SHIPPING_COST;
		    $deliveryDays           = \App\Enums\Marketplace\ApiResources::BIKE_API_DEFAULT_DELIVERY_DAYS;
		    $supplierId             = \App\Enums\Marketplace\ApiResources::BIKE_API_SUPPLIER_ID;


		    foreach ( $apiProductsArr as $singleProductArr ) {

		        try {

		            //limited insert condition
		            if($insertLimit != null && $insertedProductsCount == $insertLimit) {
		                break;
		            }

		            // GET IMAGES
		            $images = [];
		            $medias = $singleProductArr['media'];
		            foreach ( $medias as $media ) {
		                if ( $media['type'] == 'image' ) $images[] = $media['url'];
		            }
		            // END:: GET IMAGES

		            // START:: PRODUCT MISC
		            $itemColors = '';
		            $materials  = '';
		            $gender     = '';
		            $attributes = $singleProductArr['attributes'];
		            foreach ($attributes as $attribute) {
		                if ($attribute['name_en'] == 'Colour') $itemColors .= $attribute['value_de'].',';
		                if ($attribute['name_en'] == 'Material') $materials .= $attribute['value_de'].',';
		                if ($attribute['name_en'] == 'Gender') $gender .= $attribute['value_de'].',';
		            }
		            $materials = substr($materials, 0, -1);
		            $itemColors = substr($itemColors, 0, -1);
		            $gender = substr($gender, 0, -1);
		            // END:: PRODUCT MISC

		            // START :: BUILDING PRODUCTS ATTRIBUTES
		            $attributes = [
		                'api_id' => \App\Enums\Marketplace\ApiResources::BIKE_API_ID,
		                'api_product_id' => $singleProductArr['id'] ?? null,
		                'item_number'    => $singleProductArr['ean'] ?? '',
		                'name'           => $singleProductArr['name_de'] ?? '',
		                'ean'            => $singleProductArr['ean'],
		                'ek_price'       => $singleProductArr['price'],
		                'vk_price'       => $singleProductArr['price'] + ($singleProductArr['price']*0.05),
		                'uvp_price'      => $singleProductArr['consumer_price']  ?? 0,
		                'description'    => $singleProductArr['description_de'] ?? '',

		                'images'         => !empty($images) ? $images : [],
		                'supplier_id'    => $supplierId,
		                'delivery_company_id' => $deliveryCompanyId,
		                'status'         => \App\Enums\Marketplace\ProductStatus::ACTIVE,
		                # 'category_id'    => $this->getCategoryIdOfApiProduct($singleProductArr['categories'][0]['parent_segment']) ?? 0,
		                'stock_info' => [
		                    'stock'                     => $singleProductArr['stock'],
		                    'old_stock'                 => '',
		                    'stock_updated_at'          => '',
		                    'internel_stock'            => 0,
		                    'old_internel_stock'        => '',
		                    'internel_stock_updated_at' => '',
		                ],
		                'product_misc' => [
		                    'brand'          => $singleProductArr['brand'] ?? '',
		                    'color'          => $itemColors ?? '',
		                    'item_weight'    => '',
		                    'item_size'      => '',
		                    'gender'         => $gender ?? '',
		                    'materials'      => $materials ?? '',
		                    'tags'           => '',
		                    'note'           => '',
		                    'production_year'=> '',
		                    'delivery_days'  => $deliveryDays,
		                    'country_id'     => '',
		                    'language_id'    => '',
		                ],
		                'misc'               => '[]',

		                'collection_id'  => 0,
		                'shipping_method'=> \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
		                'shipping_cost'  => $shippingCost,
		                # 'category'       => [],
		                # 'materials'      => $materials ?? '',
		                # 'gender'         => $gender ?? '',
		                # 'internel_stock' => 0,
		                # 'delivery_days'  => $deliveryDays,
		            ];
		            // END :: BUILDING PRODUCTS ATTRIBUTES

		            $insertedModel = Product::updateOrCreate(['api_product_id' => $attributes['api_product_id']], $attributes);


		            $category_id   = $this->getCategoryIdOfApiProduct($singleProductArr['categories'][0]['parent_segment']) ?? 0;
		            $insertedModel->category()->sync($category_id);

		            if (!$insertedModel) Log::error($singleProductArr);
		            Log::info('inserted - '.$singleProductArr['id']);
		            $insertedProductsCount += $insertedModel ? 1 : 0;
		        } catch (\Exception $e) {
		            dd($e);
		            Log::info($e->getMessage());
		        }

		    }
		    return response()->json([
		        'status'                    => 1,
		        'products_created_count'    => $insertedProductsCount,
		    ]);
		}


		public function getCategoryIdOfApiProduct ($apiCatId)
		{
		    //local evn category for test
		    $localArrOwnMachin = [
		        1 => 15, 2 => 15, 3 => 13, 4 => 15, 5 => 15, 6 => 16, 7 => 15,
		        8 => 17, 9 => 18, 10 => 19, 11 => 19, 12 => 20, 13 => 21, 14 => 22,
		    ];
		    //Team env category for test
		    $localArr = [
		        1 => 34, 2 => 34, 3 => 33, 4 => 34, 5 => 34, 6 => 37, 7 => 38,
		        8 => 14, 9 => 7, 10 => 22, 11 => 22, 12 => 28, 13 => 25, 14 => 11,
		    ];

		    //production env category
		    $liveArr = [
		        1 => 34, 2 => 34, 3 => 33, 4 => 34, 5 => 34, 6 => 36,
		        7 => 34,
		        8 => 14, 9 => 9, 10 => 22, 11 => 22, 12 => 28, 13 => 25, 14 => 11,

		    ];
		    $arr = (env('APP_ENV') == 'local') ? $localArr : $liveArr;
		    try {
		        $category = \App\Models\Marketplace\Category::where('id', $arr[$apiCatId])->first();
		    } catch (\Exception $e) {

		    }

		    return $category->id ?? 1;
		}		
	}