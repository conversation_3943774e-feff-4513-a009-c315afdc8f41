<?php

namespace App\Services\Marketplace\BtsHolesalerApi;

use \App\Enums\Marketplace\ApiResources;

class BTSHolesalerApiService
{
    private $headers;
    public function __construct()
    {
        $this->headers  = [
            'Authorization:'.ApiResources::BTSWholesalar['TOKEN'],
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json',
        ];
    }

    public function fetchData($apiAddress, $method = 'GET', $data=[]){
        try {

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,$apiAddress);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($ch, CURLOPT_POST, $method=="POST"?1:0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            if ( !empty($data) ) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }

            $response = curl_exec ($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close ($ch);

            $response_data['data'] = json_decode($response, 1);
            $response_data['status_code'] = $status_code;

            return $response_data;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }


}
