<?php

namespace App\Services\Marketplace;

use SoapClient;

class InternelAPISoapClient{

//    public static $_WsdlUri='http://test_api.internel.eu/server.php?WSDL';
    public static $_WsdlUri='https://api.internel.eu/server.php?WSDL';
    public static $_Server=null;

    public static function _Call($method,$param){
        self::$_Server=new SoapClient(self::$_WsdlUri);
		return self::$_Server->__soapCall($method,$param);
	}

	public function testConnection($name){
		return self::_Call('testConnection',Array(
			$name
		));
	}

	public function testCredentials($obj){
		return self::_Call('testCredentials',Array(
			$obj
		));
	}

	public function changePassword($obj){
		return self::_Call('changePassword',Array(
			$obj
		));
	}

	public function newOrder($obj){
		return self::_Call('newOrder',Array(
			$obj
		));
	}

	public function orderStatus($obj){
		return self::_Call('orderStatus',Array(
			$obj
		));
	}


	public function newDelivery($obj){
		return self::_Call('newDelivery',Array(
			$obj
		));
	}


	public function deliveryStatus($obj){
		return self::_Call('deliveryStatus',Array(
			$obj
		));
	}


	public function removeDelivery($obj){
		return self::_Call('removeDelivery',Array(
			$obj
		));
	}


	public function newVendor($obj){
		return self::_Call('newVendor',Array(
			$obj
		));
	}


	public function getVendor($obj){
		return self::_Call('getVendor',Array(
			$obj
		));
	}


	public function updateVendor($obj){
		return self::_Call('updateVendor',Array(
			$obj
		));
	}


	public function newProduct($obj){
		return self::_Call('newProduct',Array(
			$obj
		));
	}


	public function getProduct($obj){
		return self::_Call('getProduct',Array(
			$obj
		));
	}


	public function updateProduct($obj){
		return self::_Call('updateProduct',Array(
			$obj
		));
	}


	public function getStockReport($obj){
		return self::_Call('getStockReport',Array(
			$obj
		));
	}
}
