<?php


namespace App\Services\Marketplace;


use App\Country;
use App\Enums\Marketplace\Credentials;
use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\Delivery;
use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\ProductSyncedHistory;
use App\NewOrder;
use App\Services\Marketplace\Internel\ChangePasswordDataType;
use App\Services\Marketplace\Internel\GeneralDataType;
use Illuminate\Support\Facades\Log;
use App\User;
use Carbon\Carbon;


use crocodicstudio\crudbooster\helpers\CRUDBooster;
use http\Exception;
use SimpleXMLElement;

class InternelSyncService
{
    private $authLogin;
    private $authPassword;
    private $version;
    private $cli;
    public $haveCredentials;

    public function __construct(InternelAPISoapClient $cli)
    {
        $haveCredentials = ApiCredential::take(1)->first();

        $this->haveCredentials = $haveCredentials;

        $this->authLogin        = $haveCredentials->api_user_name;
        $this->authPassword     = $haveCredentials->api_password;
        $this->version          = Credentials::API_VERSION;

        $this->cli              = $cli;
    }

    public function generateNewpassword()
    {
        $letters = 'abcdefghijklmnopqrstuvwxyz';
        $digits  = '**********';
        $specialChracters = '!@#$%^&*()\-_=+{};:,<.>';

        $fullStr = $letters.strtoupper($letters).$digits.$specialChracters;
        $str = '';

        $countUnpickedPasswords = \App\Models\Marketplace\ApiStoredPassword::where('picked', 0)
                                ->count();

        // Pattern for at leat one uppercase & one lowercase letters at least one digit, at least 13
        $pattern = "/^(?=(?:.*[A-Z]){1,})(?=(?:.*[a-z]){1,})(?=(?:.*\d){1,})(?=(?:.*[!@#$%^&*()\-_=+{};:,<.>]){1,})(.{13,24})$/";

        $rows = [];

        if ( $countUnpickedPasswords == 0 ) {
            for ( $i = 0; $i < 50; ) {
                $str = str_shuffle($fullStr);
                $str = substr( $str, 0, rand(13, 24));
                if ( preg_match($pattern, $str) ) {
                    $rows[] = [ 'password' => $str ];
                    $i++;
                } else {
                    continue;
                }
            }
            \App\Models\Marketplace\ApiStoredPassword::insert($rows);
        }

        $pickedPasswordRow = \App\Models\Marketplace\ApiStoredPassword::where('picked', 0)->first();

        $pickedPasswordRow->picked = 1;
        $pickedPasswordRow->save();

        return $pickedPasswordRow->password;
    }

    public function changePassordEveryDuration($secretKey)
    {
        $secretKeyMatched = Credentials::API_SECRET_KEY == $secretKey;

        if ( $secretKeyMatched ) {

            $oldPasswordWillBe = $this->authPassword;
            $newPassword = $this->generateNewpassword();

            // For safety
            Log::channel('uscreen')->info('Password trying to be changed to '.$newPassword);
            try{
                Log::channel('uscreen')->info('Password trying to be changed to '.$newPassword);
                $this->haveCredentials->update([
                    'histories' => 'Password trying to be changed to '.$newPassword,
                ]);
            } catch (Exception $e) {
                dd('Trying to change the password to '.$newPassword);
            }


            $cpdt = new ChangePasswordDataType();
            $cpdt->authLogin = $this->authLogin;
            $cpdt->authPassword = $this->authPassword;
            $cpdt->version = $this->version;
            $cpdt->newPassword = $newPassword;

            $response = $this->cli->changePassword($cpdt);
            $xml      = new SimpleXMLElement($response);

            if (isset($xml->Error)) {
                $responseArray = [
                    'error_code'    => "{$xml->Error->ErrorCode}\n",
                    'error_message' => "{$xml->Error->ErrorMsg}\n",
                    'time'          => Carbon::now(),
                ];
                $this->haveCredentials->update([
                    'histories' => $responseArray,
                ]);
                return response()->json($responseArray);

            } else {
                $updatedCredentials = $this->haveCredentials->update([
                    'api_password' => $newPassword,
                    'histories'    => [
                        'last_changing_date' => Carbon::now(),
                        'old_password'  => $oldPasswordWillBe,
                    ],
                ]);

                return response()->json([
                    'old_Password' => $oldPasswordWillBe,
                    'new_password' => $updatedCredentials->api_password,
                    'message'      => 'Password Changed Successfully',
                ]);
            }

        } else {
             return response()->json([
                'error' => 'Unauthorised Request !',
             ]);
        }
    }

    public function transferVendorData($model_id)
    {
        $gdt = new GeneralDataType();

        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        try{
            $supplier       = User::find($model_id);
            $vendorID       = $supplier->id;
            $vendorName     = $supplier->name;
            $country        = strtoupper( $supplier->billing_detail->country->country_shortcut );
            $city           = $supplier->billing_detail->city;
            $zipCode        = $supplier->billing_detail->zip;
            $street         = $supplier->billing_detail->address;
            $houseNumber    = '1';
            $email          = $supplier->email;

            $newVendor = new SimpleXMLElement ("<NewVendor/>");
            $vendor = $newVendor->addChild("Vendor");
            $vendor->addChild('VendorID',$vendorID);
            $vendor->addChild('VendorName',$vendorName);
            $vendor->addChild('Country',$country);
            $vendor->addChild('City',$city);
            $vendor->addChild('ZipCode',$zipCode);
            $vendor->addChild('Street',$street);
            $vendor->addChild('HouseNumber',$houseNumber);
            $vendor->addChild('Email',$email);

            $gdt->xmlData = $newVendor->asXML();

            $response = $this->cli->newVendor($gdt);
            $xml = new SimpleXMLElement($response);

            if (isset($xml->Error)) {
//            echo "Error code: {$xml->Error->ErrorCode}\n";
//            echo "Error message: {$xml->Error->ErrorMsg}\n";
            } else {
//            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
                foreach ($xml->Vendor as $vendor) {
                    if ( $vendor->Status = 'ADDED' ) {
                        $supplier->update([
                            'marketplace_vendor_id' => $vendor->VendorID,
                            'marketplace_api_response' => $vendor->Status,
                        ]);

                        return [
                            "VendorID" => $vendor->VendorID,
                            "Status" => $vendor->Status,
                        ];
                    }
                    // echo "VendorID: {$vendor->VendorID}\n";
                    // echo "Status: {$vendor->Status}\n";
                    
                    if (isset($vendor->Error)) {
                        $supplier->update([
                            'marketplace_api_response' => $vendor->Error->ErrorMsg,
                        ]);
                        echo "Error code: {$vendor->Error->ErrorCode}\n";
                        echo "Error message: {$vendor->Error->ErrorMsg}\n";
                    }
                }
            }
        } catch (Exception $e) {

        }

    }

    public function updateVendorData($model_id)
    {
        $gdt = new GeneralDataType();

        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $supplier       = User::find($model_id);

        $vendorID       = $supplier->id;
        $vendorName     = $supplier->name;
        $country        = strtoupper( $supplier->billing_detail->country->country_shortcut );
        $city           = $supplier->billing_detail->city;
        $zipCode        = $supplier->billing_detail->zip;
        $street         = $supplier->billing_detail->address;
        $houseNumber    = '1';
        $email          = $supplier->email;

        $updateVendor = new SimpleXMLElement("<UpdateVendor/>");
        $vendor = $updateVendor->addChild('Vendor');
        $vendor->addChild('VendorID', $vendorID);
        $vendor->addChild("VendorName", $vendorName);
        $vendor->addChild("Country", $country);
        $vendor->addChild("City", $city);
        $vendor->addChild("ZipCode", $zipCode);
        $vendor->addChild("Email", $email);

        $gdt->xmlData = $updateVendor->asXML();

        $response = $this->cli->updateVendor($gdt);
        $xml = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Vendor as $vendor) {

                if ( $vendor->Status = 'UPDATED' ) {
                    $supplier->update([
                        'marketplace_api_response' => $vendor->Status,
                    ]);
                }

                echo "VendorID: {$vendor->VendorID}\n";
                echo "Status: {$vendor->Status}\n";
                if (isset($vendor->Error)) {
                    echo "Error code: {$vendor->Error->ErrorCode}\n";
//                    echo "Error message: {$vendor->Error->ErrorMsg}\n";
                    $supplier->update([
                        'marketplace_api_response' => $vendor->Error->ErrorMsg,
                    ]);
                }
            }
        }
    }

// Not in useing in production
    public function retriveVendorData()
    {
        $gdt = new GeneralDataType();

        $gdt->authLogin     = $this->authLogin;
        $gdt->authPassword  = $this->authPassword;
        $gdt->version       = $this->version;

        $getVendor          = new SimpleXMLElement("<GetVendor/>");
        $vendor             = $getVendor->addChild("Vendor");

        $vendor->addChild("VendorID", "246");

        $gdt->xmlData       = $getVendor->asXML();

        $response           = $this->cli->getVendor($gdt);
        $xml                = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Vendor as $vendor) {

                echo "VendorID: {$vendor->VendorID}"."<br />";
                echo "Vendor Name: {$vendor->VendorData->VendorName}"."<br />";
                echo "Country: {$vendor->VendorData->Country}"."<br />";
                echo "City: {$vendor->VendorData->City}"."<br />";
                echo "Zip Code: {$vendor->VendorData->ZipCode}"."<br />";
                echo "Street: {$vendor->VendorData->Street}"."<br />";
                echo "House Number: {$vendor->VendorData->HouseNumber}"."<br />";
                echo "Apartment Number: {$vendor->VendorData->ApartmentNumber}"."<br />";
                echo "Contact Person: {$vendor->VendorData->ContactPerson}"."<br />";
                echo "Phone Number: {$vendor->VendorData->PhoneNumber}"."<br />";
                echo "Email: {$vendor->VendorData->Email}"."<br />";

                if (isset($vendor->Error)) {
                    echo "Error code: {$vendor->Error->ErrorCode}"."<br />";
                    echo "Error message: {$vendor->Error->ErrorMsg}"."<br />";
                }
            }
        }
    }

    public function transferProductData (array $product_ids)
    {
        $res = [];
        $syncedProductCount = 0;

        $syncableProducts = Product::whereIn('id', $product_ids)->get();



        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);
        try{
            $newProduct = new SimpleXMLElement("<NewProduct/>");

            foreach ( $syncableProducts as $item ) {
                $product    = $newProduct->addChild('Product');
                $product->addChild('ProductID', $item->id);
                $product->addChild('ProductName', $item->name);
                $product->addChild('ProductNumber', $item->item_number ?? 'NULL');
                $product->addChild('VendorID', $item->collection->supplier->marketplace_vendor_id);
                $product->addChild('NetWeight', $item->item_weight ?? '1');
                $product->addChild('GrossWeight', '1');

//        $productDimentions = $product->addChild('ProductDimensions');
//        $productDimentions->addChild('Height', '30');
//        $productDimentions->addChild('Width', '150');
//        $productDimentions->addChild('Length', '120');

                $product->addChild('EAN', $item->ean);
                $product->addChild('OriginCountry', $item->country_id != NULL ? strtotupper($item->country->country_shortcut) : 'DE');
            }

            $gdt->xmlData = $newProduct->asXML();

            Log::channel('uscreen')->info($product_ids);
            Log::channel('uscreen')->info($syncableProducts);
            Log::channel('uscreen')->info($gdt->xmlData);


            $response = $this->cli->newProduct($gdt);
            $xml      = new SimpleXMLElement($response);

            $success_product_ids = [];

            if (isset($xml->Error)) {
               echo "Error code: {$xml->Error->ErrorCode}"."<br />";
               echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
            } else {
                foreach ($xml->Product as $product) {
                    if ( $product->Status == "ADDED" ) {
                        Product::find($product->ProductID)->update([
                            'marketplace_product_id' => $product->ProductID,
                            'marketplace_api_response' => $product->Status,
                        ]);
                        $syncedProductCount++;
                        $success_product_ids[] = "{$product->ProductID}";
                    }
                    if (isset($product->Error)) {
                        Product::find($product->ProductID)->update([
                            'marketplace_api_response' => $product->Error->ErrorMsg,
                        ]);
                    }
                }
                $history = ProductSyncedHistory::create([
                    'user_id'       => CRUDBooster::myId(),
                    'product_ids'   => $success_product_ids,
                    'download_count'=> 0,
                    'synced_time'   => Carbon::now(),
                ]);
            }
        } catch (Exception $e) { }
        $res['syncedProductCount'] = $syncedProductCount;
        $res['historyId'] = $history->id;

        Log::channel('uscreen')->info($xml);


        return $res;
    }

    public function updateProductData ($product_id)
    {
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $item = Product::find($product_id);

        $updateProduct = new SimpleXMLElement("<UpdateProduct/>");

        $product = $updateProduct->addChild("Product");
        $product->addChild("ProductID", $item->id);
        $product->addChild("ProductName", $item->name);
//        $product->addChild("VendorID", $item->collection->supplier->id);
        $product->addChild("NetWeight", $item->item_weight ?? '1');
        $product->addChild("GrossWeight", "1");

//        $productDimentions = $product->addChild("ProductDimensions");
//        $productDimentions->addChild("Height", "28");
//        $productDimentions->addChild("Width", "148");
//        $productDimentions->addChild("Lenght", "118");
//        $product->addChild("OriginCountry", "LT");

        $gdt->xmlData   = $updateProduct->asXML();
        $response       = $this->cli->updateProduct($gdt);
        $xml            = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Product as $product) {

                echo "Product Id : {$product->ProductID}"."<br />";
                echo "Status : {$product->Status}"."<br />";

                if (isset($product->Error)) {
                    echo "Error code: {$product->Error->ErrorCode}"."<br />";
                    echo "Error message: {$product->Error->ErrorMsg}"."<br />";
                }
            }
        }
    }

    public function transferOutgoingOrders (array $request)
    {
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $newOrder           = new SimpleXMLElement("<NewOrder/>");
        $order              = $newOrder->addChild("Order");
        $order->addChild('OrderID', $request['OrderID']);
        $order->addChild('ShipmentMethod', $request['ShipmentMethod']);
        $order->addChild('PaymentMethod', $request['PaymentMethod']);

        if ( isset( $request['OrderDateTime'] ) ) {
            $order->addChild('OrderDateTime', $request['OrderDateTime']);
        }

        $client = $order->addChild('Client');

        if ( isset($request['CustomerID']) ) {
            $client->addChild('CustomerID', $request['CustomerID']);
        }
        $client->addChild('ClientFirstName', $request['ClientFirstName']);
        $client->addChild('ClientLastName', $request['ClientLastName']);

        if ( isset($request['CompanyName']) && !empty($request['CompanyName']) ) {
            $client->addChild('CompanyName', $request['CompanyName']);
        }

        $countryCode = strtolower($request['Country'])=='usa' ? 'US' : $request['Country'];

        $shippingAddress = $client->addChild('ShippingAddress');
        $shippingAddress->addChild('Country', $countryCode);
        $shippingAddress->addChild('City', $request['City']);
        $shippingAddress->addChild('ZipCode', $request['ZipCode']);
        $shippingAddress->addChild('Street', $request['Street']);
        $shippingAddress->addChild('HouseNumber', $request['HouseNumber']);

        if (isset($request['ApartmentNumber'])  && !empty($request['ApartmentNumber']) ){
            $shippingAddress->addChild('ApartmentNumber', $request['ApartmentNumber']);
        }

        $client->addChild('Email', $request['Email']);
        $client->addChild('PhoneNumber', $request['PhoneNumber']);

        $product = $order->addChild('Product');
        $product->addChild('ProductID', $request['ProductID']);
        if ( isset($request['ProductName']) ){
            $product->addChild('ProductName', $request['ProductName']);
        }
        $product->addChild('Quantity', $request['Quantity']);

        $productDetailedPrice = $product->addChild('DetailedPrice');
//        if ( isset($request['ValueNet']) ) {
//            $productDetailedPrice->addChild('ValueNet', $request['ValueNet']);
//        }
//        if ( isset($request['ValueVAT']) ) {
//            $productDetailedPrice->addChild('ValueVAT', $request['ValueVAT']);
//        }
        $productDetailedPrice->addChild('ValueGross', $request['ValueGross']);
        $product->addChild('VAT', $request['VAT']);

        $valueCashTotal = $request['ValueGross'] * $request['Quantity'];

        $shippingTotal = floatval( removeCommaPrice($request['ValueShippingTotal']) );

        $payableTotal = $valueCashTotal+$shippingTotal;
        $order->addChild('PayableTotal', $payableTotal);
        $order->addChild('ValueCashTotal', $valueCashTotal);
//        if ( isset($request['ValueNetTotal']) ) {
//            $order->addChild('ValueNetTotal', $request['ValueNetTotal']);
//        }
//        if ( isset($request['ValueVATTotal_']) ) {
//            $order->addChild('ValueVATTotal', $request['ValueVATTotal_']);
//        }
        $order->addChild('ValueShippingTotal', $shippingTotal);

        $gdt->xmlData = $newOrder->asXML();

        $response = $this->cli->newOrder($gdt);
        $xml = new SimpleXMLElement($response);

        $response = [];
        $response['addedOrderIds'];

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Order as $order) {
                echo "Order ID: {$order->OrderID}\n";
                echo "Status: {$order->Status}\n";
                if ( $order->Status == 'ADDED' ) {
                    $response['addedOrderIds'][] = ""."{$order->OrderId}";
                }
                if (isset($order->Error)) {
                    echo "Error code: {$order->Error->ErrorCode}\n";
                    echo "Error message: {$order->Error->ErrorMsg}\n";
                }
            }
            return $response;
        }
        return $response;
    }


    public function transferOutgoingOrdersWithMultipleProducts(array $request)
    {
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $valueCashTotal = 0;
        $orderCount = 0;
        $orderedProducts = [];

        $history = [];

        $newOrder           = new SimpleXMLElement("<NewOrder/>");
        $order              = $newOrder->addChild("Order");
        $order->addChild('OrderID', $request['OrderID']);
        $order->addChild('ShipmentMethod', $request['ShipmentMethod']);
        $order->addChild('PaymentMethod', $request['PaymentMethod']);

        $orderDate = Carbon::now();
        if ( isset( $request['OrderDateTime'] ) ) {
            $order->addChild('OrderDateTime', $orderDate);
        }

        $client = $order->addChild('Client');
        if ( isset($request['CustomerID']) ) {
            $client->addChild('CustomerID', $request['CustomerID']);
        }
        $client->addChild('ClientFirstName', $request['ClientFirstName']);
        $client->addChild('ClientLastName', $request['ClientLastName']);

        // Company nname checking
        if ( isset($request['CompanyName']) && !empty($request['CompanyName']) ) {
            $client->addChild('CompanyName', $request['CompanyName']);
        }

        $countryCode = strtolower($request['Country'])=='usa' ? 'US' : $request['Country'];

        $shippingAddress = $client->addChild('ShippingAddress');
        $shippingAddress->addChild('Country', $countryCode);
        $shippingAddress->addChild('City', $request['City']);
        $shippingAddress->addChild('ZipCode', $request['ZipCode']);
        $shippingAddress->addChild('Street', $request['Street']);
        $shippingAddress->addChild('HouseNumber', $request['HouseNumber']);

        if ( isset($request['ApartmentNumber']) && !empty($request['ApartmentNumber']) ) {
            $shippingAddress->addChild('ApartmentNumber', $request['ApartmentNumber']);
        }

        $client->addChild('Email', $request['Email']);
        $client->addChild('PhoneNumber', $request['PhoneNumber']);

        $countProducts = count($request['productIds']);
        for ( $i = 0; $i < $countProducts; $i++ ) {
            $orderedProducts[] = $request['productIds'][$i];
            $productName = substr($request['paroductNames'][$i], strpos($request['paroductNames'][$i], '-')+1);

            $product = $order->addChild('Product');
            $product->addChild('ProductID', $request['productIds'][$i]);
            if ( isset($request['ProductName']) ){
                $product->addChild('ProductName', $productName );
            }
            $product->addChild('Quantity', $request['productQty'][$i]);
            $productDetailedPrice = $product->addChild('DetailedPrice');
            $productDetailedPrice->addChild('ValueGross', $request['productValueGross'][$i]);

//            if ( isset($request['productValueNet'][$i]) && $request['productValueNet'][$i] != 0 ) {
//                $productDetailedPrice->addChild('ValueNet', $request['productValueNet'][$i]);
//                echo $request['productValueNet'][$i]."<br />";
//            }
//            if ( isset($request['productValueVat'][$i]) && $request['productValueNet'][$i] != 0 ) {
//                $productDetailedPrice->addChild('ValueVAT', $request['productValueVat'][$i]);
//            }
            $product->addChild('VAT', $request['paroductVat'][$i]);

//            Calculation
            $valueCashTotalForThisProduct = $request['productQty'][$i] * $request['productValueGross'][$i];
            $valueCashTotal += $valueCashTotalForThisProduct;

            $history['products'][] = [
                    'product_id'    => intval($request['productIds'][$i]),
                    'product_name'  => $productName,
                    'quantity'      => intval($request['productQty'][$i]),
                    'vat'           => floatval($request['paroductVat'][$i]),
                    'value_gross'   => floatval($request['productValueGross'][$i]),
                    'valueCashTotal'    => $valueCashTotal,
            ];
            $customerInfo = [
                'id'            => intval($request['drm_customer_id']) ?? '',
                'first_name'    => $request['ClientFirstName'],
                'last_name'     => $request['ClientLastName'],
                'country_code'  => $countryCode,
                'city'          => $request['City'],
                'zip_code'      => $request['ZipCode'],
                'street'        => $request['Street'],
                'house_number'  => $request['HouseNumber'],
            ];
        }

//        $createdOrder = MarketplaceSyncedOrder::create([
//            'order_id'          => 4389578,
//            'supplier_id'       => \CRUDBooster::myParentId(),
//            'product_ids'       => [1,2,3,4],
//            'status_history'    => ['ADDED'],
//            'order_date'        => Carbon::now(),
//            'order_infos'       => $request,
//            'status'            => 'ADDED',
//            'history'           => $history,
//            'customer_info'     => $customerInfo,
//        ]);
//        dd($history, $customerInfo);

        $shippingTotal = floatval( removeCommaPrice($request['ValueShippingTotal']) );

        $payableTotal = $valueCashTotal+$shippingTotal;

        $order->addChild('PayableTotal', $payableTotal);
        $order->addChild('ValueCashTotal', $valueCashTotal);
//        if ( isset($request['ValueNetTotal']) ) {
//            $order->addChild('ValueNetTotal', $request['ValueNetTotal']);
//        }
//        if ( isset($request['ValueVATTotal_']) ) {
//            $order->addChild('ValueVATTotal', $request['ValueVATTotal_']);
//        }
        $order->addChild('ValueShippingTotal', $shippingTotal);

        $gdt->xmlData = $newOrder->asXML();


        $res = $this->cli->newOrder($gdt);
        $xml = new SimpleXMLElement($res);

        $response = [];
        $response['addedOrderIds'];

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
            $response['error'] = "{$xml->Error->ErrorMsg}";
            Log::channel('uscreen')->info("XML Error");
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            try{
                foreach ($xml->Order as $order) {
                    echo "Order ID: {$order->OrderID}\n";
                    echo "Status: {$order->Status}\n";
                    if ( $order->Status == 'ADDED' ) {
                        $createdOrder = MarketplaceSyncedOrder::create([
                            'order_id' => $order->OrderID,
                            'supplier_id' => \CRUDBooster::myParentId(),
                            'product_ids' => $orderedProducts,
                            'status_history' => ['ADDED'],
                            'order_date' => $orderDate,
                            'order_infos' => $request,
                            'status'     => 'ADDED',
                            'history'    => $history,
                            'customer_info' => $customerInfo,
                        ]);
                        $orderCount++;

                        if ( $createdOrder ) {
                            Log::channel('uscreen')->info($createdOrder);
                        } else {
                            Log::channel('uscreen')->info('Not saved in database');
                        }
                    }
                    if (isset($order->Error)) {
                        echo "Error code: {$order->Error->ErrorCode}\n";
                        echo "Error message: {$order->Error->ErrorMsg}\n";
                        $response['error'] = "Error message: {$order->Error->ErrorMsg}\n";
                        Log::channel('uscreen')->info("error after order created ");
                    }
                }
                $response['successOrderCount'] = $orderCount;
                return $response;
            } catch (Exception $e) {
                $response['error'] = $e->getMessage();
            }
        }
        return $response;

    }



    public function retriveStockData ($product_id)
    {
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        try{
            $productInfo = Product::find($product_id);

            $stockReport    = new SimpleXMLElement("<GetStockReport/>");
            $product        = $stockReport->addChild("Product");
            $product->addChild("ProductID", $productInfo->marketplace_product_id);

            $gdt->xmlData   = $stockReport->asXML();
            $response       = $this->cli->getStockReport($gdt);
            $xml            = new SimpleXMLElement($response);

            if (isset($xml->Error)) {
                echo "Error code: {$xml->Error->ErrorCode}"."<br />";
                echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
            } else {
//            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
                foreach ($xml->Product as $product) {
                    if ( !isset($product->Error) ) {
                        $aux = [];
                        $stockInfo       = (array)$product;

                        $aux['stockProductId']  = $stockInfo['ProductID'];
                        $productQuantity = (array)$stockInfo['ProductQuantity'];

                        $aux['availableFree']   = (int)$productQuantity['AvailableFree'];
                        $aux['damaged']         = (int)$productQuantity['Damaged'];
                        $aux['reserved']        = (int)$productQuantity['Reserved'];
                        $aux['returned']        = (int)$productQuantity['Returned'];

                        $p = Product::find($aux['stockProductId']);

                        Product::find($aux['stockProductId'])->update([
                            'internel_stock'    => $aux['availableFree'],
                        ]);

                        return response()->json( $aux );
                    }

                    if (isset($product->Error)) {
                        return response()->json([
                            'errorMessage' => 'Product not synced yet',
                        ]);
                    }
                    else{
                        return response()->json([
                            'errorMessage' => 'Product not synced yet',
                            'data' => $xml->Product
                        ]);
                    }
                }
            }
        } catch (Exception $e) {
            return response()->json([
                'errorMessage' => $e->getMessage(),
            ]);
        }
    }

    public function retriveManyStockData (array $product_ids)
    {
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        try{
            $reports = [];
            $productInfos = Product::find($product_ids);

            $stockReport    = new SimpleXMLElement("<GetStockReport/>");


            foreach ($productInfos as $productInfo) {
                $product        = $stockReport->addChild("Product");
                $product->addChild("ProductID", $productInfo->marketplace_product_id);
            }

            $gdt->xmlData   = $stockReport->asXML();
            $response       = $this->cli->getStockReport($gdt);
            $xml            = new SimpleXMLElement($response);

            if (isset($xml->Error)) {
                echo "Error code: {$xml->Error->ErrorCode}"."<br />";
                echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
            } else {
//            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
                foreach ($xml->Product as $product) {
                    if ( !isset($product->Error) ) {
                        $aux = [];
                        $stockInfo       = (array)$product;

                        $aux['stockProductId']  = $stockInfo['ProductID'];
                        $productQuantity = (array)$stockInfo['ProductQuantity'];

                        $aux['availableFree']   = (int)$productQuantity['AvailableFree'];
                        $aux['damaged']         = (int)$productQuantity['Damaged'];
                        $aux['reserved']        = (int)$productQuantity['Reserved'];
                        $aux['returned']        = (int)$productQuantity['Returned'];

                        $reports[$aux['stockProductId']] = (int)$aux['availableFree'];
                    }

                    if (isset($product->Error)) {} else {}
                }
            }
        } catch (Exception $e) {}
        return $reports;
    }

    public function transferIncomingDeliveries (array $id_selected)
    {
        $gdt = new GeneralDataType();

        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $productsCollection = Product::whereIn('id', $id_selected)
                    ->whereNotNull('marketplace_product_id')
                    ->whereNotNull('stock')
                    ->whereNotNull('ek_price');

        $products = $productsCollection->get();

        $productsIds = $productsCollection->pluck('id')->toArray();

        $newDelivery        = new SimpleXMLElement("<NewDelivery/>");
        $delivery           = $newDelivery->addChild("Delivery");

        $deliveryId = time();

        $delivery->addChild('DeliveryID', $deliveryId);
        $delivery->addChild('DeliveryDate', Carbon::now()->todatestring());
        $productsCount = count($productsIds);

        foreach ( $products as $product ) {
//        Not mendatory
//            $vendor = $delivery->addChild('Vendor');
//            $vendor->addChild('VendorID', '02-1234');

            $productInfo = $delivery->addChild('Product');
            $productInfo->addChild('ProductID', $product->marketplace_product_id);
            $productInfo->addChild('Quantity', $product->stock);
            $productInfo->addChild('ValueNet', $product->ek_price);
        }

        $gdt->xmlData = $newDelivery->asXML();

        $response           = $this->cli->newDelivery( $gdt );
        $xml                = new SimpleXMLElement($response);

        $addedCount     = 0;
        $ommitedCount   = 0;
        $successDeliveryId  = 0;

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
//            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Delivery as $delivery) {

                if ( $delivery->Status == 'OMITTED' ) {
                    $ommitedCount++;
                } else if ( $delivery->Status == 'ADDED' ) {
                    $successDeliveryId = "{$delivery->DeliveryID}";
                    $addedCount++;


                }
                if (isset($delivery->Error)) {
                    echo "Error code: {$delivery->Error->ErrorCode}\n";
                    echo "Error message: {$delivery->Error->ErrorMsg}\n";
                }
            }
            return $deliveryInfo = compact(
                'addedCount',
                'ommitedCount',
                'successDeliveryId',
                'productsIds',
            );
        }
        return false;
    }

    public function retriveOrderStatus($orderData)
    {

        $res = [];


        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $orderStatus              = new SimpleXMLElement("<OrderStatus/>");
        $orderStatus->addChild('StatusMode', 'SHOW ALL');
        $order = $orderStatus->addChild("Order");
        $order->addChild('OrderID', $orderData->order_id);


        $gdt->xmlData       = $orderStatus->asXML();

        $response           = $this->cli->orderStatus( $gdt );
        $xml                = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
//            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Order as $o) {
                $res['OrderID']        = "{$o->OrderID}";
                $res['TrackingNumber'] = "{$o->TrackingNumber}" ?? 'N/A';
                $res['Status']         = "{$o->OrderStatus->Status}" ?? 'N/A';
                $res['Date']         = "{$o->OrderStatus->Date}" ?? 'N/A';

//                echo "Order ID: {$o->OrderID}\n";
//                echo "TrackingNumber : {$o->TrackingNumber}\n";
                if (isset($o->Error)) {
                    echo "Error code: {$o->Error->ErrorCode}\n";
                    echo "Error message: {$o->Error->ErrorMsg}\n";
                }
            }
        }
        return $res;
    }

    public function scheduleSyncOrderStatus($skip = 0, $take = 49)
    {
        $orderIds = MarketplaceSyncedOrder::whereNotNull('id')
                    ->whereNotIn('status', ['ORDER_COMPLETED', 'ORDER_RETURNED', 'ORDER_CANCELLED'])
                    ->orderBy('id', 'desc')
                    ->skip($skip)
                    ->take($take)
                    ->pluck('order_id')
                    ->toArray();
        $res = [];
        $gdt = new GeneralDataType();
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $orderStatus              = new SimpleXMLElement("<OrderStatus/>");
        $orderStatus->addChild('StatusMode', 'SHOW ALL');

        foreach ($orderIds as $orderId) {
            $order = $orderStatus->addChild("Order");
            $order->addChild('OrderID', $orderId);
        }

        $gdt->xmlData       = $orderStatus->asXML();

        $response           = $this->cli->orderStatus( $gdt );
        $xml                = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Order as $o) {
//                $res['OrderID']        = "{$o->OrderID}";
//                $res['TrackingNumber'] = "{$o->TrackingNumber}";
//                $res['Status']         = "{$o->Status}";
                $arrIndexOrderId = "{$o->OrderID}";

                $arr = [];
                $arr['OrderID']                  = "{$o->OrderID}" ?? "N/A";
                $arr['TrackingNumber']           = "{$o->TrackingNumber}" ?? "N/A";
                $arr['ShipmentProvider']         = "{$o->ShipmentProvider}" ?? "N/A";
                $arr['Status']                   = "{$o->OrderStatus->Status}" ?? "N/A";
                $arr['Date']                     = "{$o->OrderStatus->Date}" ?? "N/A";

                $res[$arrIndexOrderId ] = $arr;

                echo "Order ID: {$o->OrderID}\n";
                echo "TrackingNumber : {$o->TrackingNumber}\n";
                if (isset($o->Error)) {
                    echo "Error code: {$o->Error->ErrorCode}\n";
                    echo "Error message: {$o->Error->ErrorMsg}\n";
                }
            }
        }

        return $res;
    }

    public function scheduleSyncDeliveryStatus()
    {
        $results = [];
        Delivery::chunk(49, function($deliveries){

            $gdt = new GeneralDataType();

            $gdt->addCredentials([
                'authLogin'     => $this->authLogin,
                'authPassword'  => $this->authPassword,
                'version'       => $this->version,
            ]);

            $deliveryStatus = new SimpleXMLElement('<DeliveryStatus/>');
            $deliveryStatus->addChild('StatusMode', 'COMPLETED');

            foreach($deliveries as $delivery) {
                $deliveryContainer = $deliveryStatus->addChild('Delivery');
                $deliveryContainer->addChild('DeliveryID', $delivery->delivery_id);
            }

            $gdt->xmlData = $deliveryStatus->asXML();

            $response     = $this->cli->deliveryStatus($gdt);
            $xml          = new SimpleXMLElement($response);

            if (isset($xml->Error)) {
                echo "Error code: {$xml->Error->ErrorCode}"."<br />";
                echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
            } else {
                echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
                foreach ($xml->Delivery as $delivery) {
                    echo "Delivery ID: {$delivery->DeliveryID}"."<br />";
                    echo "Status : {$delivery->Status}"."<br />";
                    echo "Date : {$delivery->Date}"."<br />";
                    echo "<br />";

                    $d_id       = "{$delivery->DeliveryID}";
                    $d_status   = "{$delivery->Status}";
                    $d_date     = "{$delivery->Date}";

                    Delivery::where('delivery_id', $d_id)
                        ->update([
                            'status' => [
                                'delivery_id' => $d_id,
                                'status' => $d_status,
                                'data'   => $d_date,
                            ],
                        ]);
                    if (isset($delivery->Error)) {
                        echo "Error code: {$delivery->Error->ErrorCode}"."<br />";
                        echo "Error message: {$delivery->Error->ErrorMsg}"."<br />";
                    }
                }
            }
        });
        return $results;
    }
}
