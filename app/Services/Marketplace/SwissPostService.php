<?php


namespace App\Services\Marketplace;

use GuzzleHttp\Client;
use App\Services\BaseService;
use GuzzleHttp\Exception\ClientException;

class SwissPostService extends BaseService
{

    /*
    * genare Bearer token
    */
    public function generateToken($scope, $user_id)
    {
        // Get user-specific credentials dynamically
        $credentials = app(\App\Services\Marketplace\SwissOrderService::class)->getUserSwissPostCredentials($user_id);
        
        $url = app()->environment('production') ? 'https://api.post.ch/OAuth/token' : 'https://apiint.post.ch/OAuth/token';
        $clientId = $credentials['client_id'];
        $clientSecret = $credentials['client_secret'];

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query(array(
                'grant_type' => 'client_credentials',
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'scope' => $scope
            )),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
            ),
        ));

        $response = curl_exec($curl);
        info($response);

        if ($response === false) {
            return false;
        }

        curl_close($curl);

        $res = json_decode($response, true);

        return $res['access_token'] ?? false;
    }

    /*
    * Label generation for outbound label (response is a PDF label and the mailpieceId)
    */

    public function buildCurlRequest($apiAddress, $token, $method = 'GET', $data = [])
    {
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
        ];

        $ch = curl_init($apiAddress);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true); // Set method to POST
        } else if ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT'); // Set method to PUT
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $response = curl_exec($ch);
        info($response);
        // $response = json_decode($response, 1);
        // dd($response);
        $responses['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $responses['response'] = json_decode($response, true) ?? $response;

        curl_close($ch);
        return $responses;
    }

    public function preAdviceOrderCreate($apiAddress, $token, $data = [])
    {
        $client = new Client();

        try {
            $response = $client->request('PUT', $apiAddress, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                ],
                'json' => $data,
            ]);

            $statusCode = $response->getStatusCode();
            $apiResponse = $response->getBody()->getContents(); // Get the response body
            return [
                'status_code' => $statusCode
            ];
        } catch (ClientException $e) {
            // Handle the client exception
            $statusCode = $e->getResponse()->getStatusCode();
            $errorResponse = json_decode($e->getResponse()->getBody()->getContents(), true);

            return [
                'status_code' => $statusCode,
                'response' => $errorResponse
            ];
        }
    }
}
