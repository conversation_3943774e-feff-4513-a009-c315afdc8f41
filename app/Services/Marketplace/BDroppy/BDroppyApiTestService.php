<?php
	namespace App\Services\Marketplace\BDroppy;

	use App\Enums\Marketplace\ProductStatus;
	use Illuminate\Support\Facades\DB;
	use App\Services\BaseService;
	use App\Models\Marketplace\ApiCredential;
    use App\Models\Marketplace\MarketplaceBdroppyCategory;
    use App\Models\Marketplace\MarketplaceBdroppyCatelog;
    use App\Models\Marketplace\Product;
    use Log;

	class BDroppyApiTestService extends BaseService{

		public $apiService;
		public $credentials;
		public $apiAddresses;
		public $headers;
		public $ch;

        public $product;
        public $catelog;
        public $bdroppyCategory;
		public function __construct (ApiCredential $apiCredential,MarketplaceBdroppyCatelog $catelog,Product $product, MarketplaceBdroppyCategory $bdroppyCategory)
		{
			$this->apiAddresses = \App\Enums\Marketplace\ApiTestResources::BDROPPY_APIS;
			$this->credentials 	= $apiCredential->setConnection('drm_team')->where('api_id', \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_ID)
								->first();
			$this->headers           = [
	            'Authorization: Bearer '.$this->credentials->token,
	            'Content-Type: application/json',
	            'Accept: application/json',
	        ];
			$this->ch            = curl_init();

            $this->catelog = $catelog;
            $this->bdroppyCategory = $bdroppyCategory;
            $this->product = $product;
		}

		public function fetchData($apiAddress, $method="GET", $data=[])
	    {
	        $response = $this->buildRequest($apiAddress, $method, $data)
	            ->getResponse();
	        return $response;
	    }

		public function buildRequest ($apiAddress, $method='GET', $data=[]) {
	        curl_setopt($this->ch, CURLOPT_URL,htmlspecialchars_decode($apiAddress));
	        curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
	        curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
	        curl_setopt($this->ch, CURLOPT_POST, $method=="POST"?1:0);
	        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
	        if ( !empty($data) ) {
	            curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
	        }
	        return $this;
	    }

	    public function getResponse ()
	    {
	        try {
	            $response = curl_exec ($this->ch);
	            curl_close ($this->ch);
	            $this->ch = curl_init();
	            return json_decode($response, 1);
	        } catch (\Exception $e) {
	            return $e->getMessage();
	        }
	    }

		public function updateCatelogList ($catelogs)
		{
			if ( count($catelogs) ) {
				foreach ( $catelogs as $catelog ) {
                    Log::info('catelog ID :: '.$catelog['_id']);

					$this->catelog->setConnection('drm_team')->updateOrCreate([
						'catelog_id' => $catelog['_id']
					], [
						'catelog_id'=> $catelog['_id'] ?? '',
						'name'		=> $catelog['name'] ?? '',
						'count'		=> $catelog['count'] ?? 0,
						'attributes'=> $catelog,
					]);
				}
			}
		}


		public function insertProductsToMarketplace($products)
		{
            $api_id 		   = \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_ID;
            $categories 	   = $this->bdroppyCategory->setConnection('drm_team')->pluck('marketplace_category_id', 'code');
			$deliveryCompanyId = \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_DELIVERY_COMPANY_ID;
			$shippingCost 	   = \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_DEFAULT_SHIPPING_COST;
			$deliverDays 	   = \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_DEFAULT_DELIVERY_DAYS;
			$shippingMethod    = \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING;
			if(isset($products['items'])){
                $imgBaseUrl 	   = $products['imgBase'];

                foreach ( $products['items'] as $productItem ) {

                    $images 	   = [];
                    foreach($productItem['pictures'] as $image){
                        $images[]  = $imgBaseUrl.'/m/'.$image['url'];
                    }
                    $product_name  = isset($productItem['name'])?$productItem['name']:'';
                    $product_brand = isset($productItem['brand'])?$productItem['brand']:'';
                    $discriptions  = isset($productItem['descriptions']['de_DE'])?$productItem['descriptions']['de_DE']:'';
                    $categoryId    = isset($categories[$productItem['attributes']['category']])?$categories[$productItem['attributes']['category']]:'';

                    if(!empty($productItem['pictures'])){
                        foreach($productItem['models'] as $product){
                            if(!empty($product['barcode'])){
                                $api_product_id     = $product['id'];
                                $ean 				= $product['barcode'];
                                $item_number        = isset($product['code'])?$product['code']:'';
                                $ek_price           = $product['bestTaxable'];
                                $vkPrice 			= $ek_price + ($ek_price * 0.05);
                                $uvp                = $product['streetPrice'];
                                $stock				= $product['availability'];

                                $old_product = $this->product->setConnection('drm_team')->where([
                                    'api_id'		=>  $api_id,
                                    'api_product_id'=>  $api_product_id
                                ])->first();

                                if(isset($old_product)){
                                    // UPDATE
                                    if( $old_product->stock != $stock ){
                                        $old_stock 		  = $old_product->stock;
                                        $stock_updated_at = \Carbon\Carbon::now();
                                    }else{
                                        $old_stock          = $old_product->old_stock;
                                        $stock_updated_at   = $old_product->stock_updated_at;
                                    }



                                    $attributes = [
                                        'api_product_id'	 => $api_product_id,
                                        'name'				 => $product_name,
                                        'brand'				 => $product_brand,
                                        'ean'			     => $ean,
                                        'ek_price'			 => $ek_price,
                                        'vk_price'			 => $vkPrice,
                                        'uvp'				 => $uvp,
                                        'description'		 => $discriptions,
                                        'image'				 => $images,
                                        'item_color'		 => isset($product['color'])? $product['color']:'',
                                        'stock'				 => $stock,
                                        'old_stock'			 => $old_stock,
                                        'stock_updated_at'   => $stock_updated_at,
                                        'item_weight'	     => isset($productItem['weight']) ? $productItem['weight'] : '',
                                        'item_size'			 => isset($product['size'])?$product['size']:'',
                                        'gender'			 => isset($productItem['attributes']['gender'])?$productItem['attributes']['gender']:'',
                                    ];
                                    $response = $old_product->update($attributes);
                                    Log::info("BDroppy Updated - ".$product['id']);
                                }else{
                                    // INSERT
                                    $attributes = [
                                        'api_id' 			 => $api_id,
                                        'api_product_id'	 => $api_product_id,
                                        'name'				 => $product_name,
                                        'brand'				 => $product_brand,
                                        'ean'				 => $ean,
                                        'ek_price'			 => $ek_price,
                                        'vk_price'			 => $vkPrice,
                                        'uvp'			     => $uvp,
                                        'description'		 => $discriptions,
                                        'category_id'		 => $categoryId,
                                        'image'				 => $images,
                                        'item_color'		 => isset($product['color'])?$product['color']:'',
                                        'status'			 => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                                        'item_number'		 => $item_number,
                                        'shipping_method'	 => $shippingMethod,
                                        'shipping_cost'		 => $shippingCost,
                                        'stock'				 => $stock ?? 0,
                                        'item_weight'		 => isset($productItem['weight']) ? $productItem['weight'] : '',
                                        'item_size'		     => isset($product['size'])?$product['size']:'',
                                        'production_year'	 => '',
                                        'materials'			 => '',
                                        'gender'			 => isset($productItem['attributes']['gender'])?$productItem['attributes']['gender']:'',
                                        'is_top_product'     => 1,
                                        'delivery_days'	     => $deliverDays,
                                        'collection_id'      => 0,
                                        'internel_stock'     => 0,
                                        'delivery_company_id'=> $deliveryCompanyId,
                                    ];

                                    $response = $this->product->setConnection('drm_team')->create($attributes);
                                    Log::info("BDroppy Inserted - ".$response->api_product_id);
                                }
                            }
                        }

                    }
                }
            }
		}
	}
?>
