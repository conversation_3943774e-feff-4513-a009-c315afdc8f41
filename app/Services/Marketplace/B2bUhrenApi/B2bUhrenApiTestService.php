<?php


namespace App\Services\Marketplace\B2bUhrenApi;

use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\Product;
use App\Services\BaseService;
use App\Enums\Marketplace\ProductStatus;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class B2bUhrenApiTestService extends BaseService
{
    public $uid;
    public $pid;
    public $lid;
    public $key;
    public $api_version;
    public $apiAddress;
    public $headers;
    public function __construct(ApiCredential $apiCredentials)
    {
        $apiCredentials     = $apiCredentials->setConnection('drm_team')->where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)->first();

        $this->uid          = $apiCredentials->misc['uid'];
        $this->pid          = $apiCredentials->misc['pid'];
        $this->lid          = $apiCredentials->misc['lid'];
        $this->key          = $apiCredentials->token;
        $this->api_version  = $apiCredentials->misc['api_version'];
        $this->apiAddress   = $apiCredentials->api_address;

        $this->headers           = [
            'Content-Type: application/json',
            'Accept: application/json',
        ];
    }
    public function getBrands(){
        $api_request = array(
                    "uid"             => $this->uid,
                    "pid"             => $this->pid,
                    "lid"             => $this->lid,
                    "key"             => $this->key,
                    "api_version"     => $this->api_version,
                    "request"         => "get_brands"
                );
        $api_request = array('data' => json_encode($api_request));
        $data = $this->buildRequest($api_request);
        return $data;
    }

    public function getProductByBrand($brand_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "get_brand_items",
            "id_brand"     => $brand_id,
            "display_brand_name"        => true,
            "display_reference"         => true,
            "display_name"              => true,
            "display_stock"             => true,
            "display_weight"            => true,
            "display_retail_price"      => true,
            "display_discount"          => true,
            "display_price"             => true,
            "display_id_supplier"       => true,
            "display_speed_shipping"    => true,
            "display_ean"               => true,
            "display_currency"          => true,
            "display_icon_path"         => true,
            "display_image_path"        => true,
            "display_image_last_update" => true,
            "display_attributes"        => true,
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function getProductById($product_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "get_item",
            "id_product"     => $product_id,
            "display_brand_name"        => true,
            "display_reference"         => true,
            "display_name"              => true,
            "display_stock"             => true,
            "display_weight"            => true,
            "display_retail_price"      => true,
            "display_discount"          => true,
            "display_price"             => true,
            "display_id_supplier"       => true,
            "display_speed_shipping"    => true,
            "display_ean"               => true,
            "display_currency"          => true,
            "display_icon_path"         => true,
            "display_image_path"        => true,
            "display_image_last_update" => true,
            "display_attributes"        => true,
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function getProductByBrandWithFilterColumn($brand_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "id_brand"        => $brand_id,
            "display_brand_name"        => false,
            "display_reference"         => false,
            "display_name"              => false,
            "display_stock"             => true,
            "display_weight"            => false,
            "display_retail_price"      => false,
            "display_discount"          => false,
            "display_price"             => false,
            "display_id_supplier"       => false,
            "display_speed_shipping"    => false,
            "display_ean"               => false,
            "display_currency"          => false,
            "display_icon_path"         => false,
            "display_image_path"        => false,
            "display_image_last_update" => false,
            "display_attributes"        => false,
            "request"                   => "get_brand_items",
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function buildRequest ($data=[], $method='POST') {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiAddress);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $ce = curl_exec($ch);

        return $ce;

        curl_close($ch);
    }

    public function getCategoryIdOfApiProduct ($apiCatId = null)
    {
        //Team env category for test
        $localArr = [
            "UHREN"             => 1,
            "SCHMUCK"           => 2,
            "BRILLEN"           => 3,
            "MODEACCESSOIRES"   => 4,
            "POSTEN"            => 5,
        ];

        //production env category
        $liveArr = [
            "SCHMUCK"           => 31,
            "UHREN"             => 35,
            "MODEACCESSOIRES"   => 12,
            "BRILLEN"           => 31,
            "POSTEN"            => 31,
        ];

        $arr = (env('APP_ENV') == 'local') ? $localArr : $liveArr;

        try {
            if(!empty($apiCatId)){
                $category = $arr[$apiCatId];
            }else{
                $category = 1;
            }

        } catch (\Exception $e) {

        }

        return $category ?? 1;

    }


}
