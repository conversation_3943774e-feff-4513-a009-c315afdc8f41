<?php


namespace App\Services\Marketplace\B2bUhrenApi;

use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\Product;
use App\Services\BaseService;
use App\Services\Marketplace\DuplicateEanService;
use App\Enums\Marketplace\ProductStatus;
use App\Models\Marketplace\Category;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class B2bUhrenApiService extends BaseService
{
    public $api_user_name;
    public $api_password;
    public $uid;
    public $pid;
    public $lid;
    public $key;
    public $api_version;
    public $apiAddress;
    public $headers;
    public $duplicateService;
    public function __construct()
    {
        $apiCredentials     = ApiCredential::where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)->first();
        $this->uid          = $apiCredentials->misc['uid'];
        $this->pid          = $apiCredentials->misc['pid'];
        $this->lid          = $apiCredentials->misc['lid'];
        $this->key          = $apiCredentials->token;
        $this->api_version  = $apiCredentials->misc['api_version'];
        $this->apiAddress   = $apiCredentials->api_address;
        $this->api_user_name   = $apiCredentials->api_user_name;
        $this->api_password   = $apiCredentials->api_password;

        $this->headers           = [
            'Content-Type: application/json',
            'Accept: application/json',
        ];


        $this->duplicateService = new DuplicateEanService();
    }
    public function getBrands(){
        $api_request = array(
                    "uid"             => $this->uid,
                    "pid"             => $this->pid,
                    "lid"             => $this->lid,
                    "key"             => $this->key,
                    "api_version"     => $this->api_version,
                    "request"         => "get_brands"
                );
        $api_request = array('data' => json_encode($api_request));
        $data = $this->buildRequest($api_request);
        return $data;
    }

    public function getProductByBrand($brand_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "get_brand_items",
            "id_brand"     => $brand_id,
            "display_brand_name"        => true,
            "display_reference"         => true,
            "display_name"              => true,
            "display_stock"             => true,
            "display_weight"            => true,
            "display_retail_price"      => true,
            "display_discount"          => true,
            "display_price"             => true,
            "display_id_supplier"       => true,
            "display_speed_shipping"    => true,
            "display_ean"               => true,
            "display_currency"          => true,
            "display_icon_path"         => true,
            "display_image_path"        => true,
            "display_image_last_update" => true,
            "display_attributes"        => true,
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function getProductById($product_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "get_item",
            "id_product"     => $product_id,
            "display_brand_name"        => true,
            "display_reference"         => true,
            "display_name"              => true,
            "display_stock"             => true,
            "display_weight"            => true,
            "display_retail_price"      => true,
            "display_discount"          => true,
            "display_price"             => true,
            "display_id_supplier"       => true,
            "display_speed_shipping"    => true,
            "display_ean"               => true,
            "display_currency"          => true,
            "display_icon_path"         => true,
            "display_image_path"        => true,
            "display_image_last_update" => true,
            "display_attributes"        => true,
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function getProductByBrandWithFilterColumn($brand_id){
        $request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "id_brand"        => $brand_id,
            "display_brand_name"        => false,
            "display_reference"         => false,
            "display_name"              => false,
            "display_stock"             => true,
            "display_weight"            => false,
            "display_retail_price"      => false,
            "display_discount"          => false,
            "display_price"             => false,
            "display_id_supplier"       => false,
            "display_speed_shipping"    => false,
            "display_ean"               => false,
            "display_currency"          => false,
            "display_icon_path"         => false,
            "display_image_path"        => false,
            "display_image_last_update" => false,
            "display_attributes"        => false,
            "request"                   => "get_brand_items",
        );

        $request = array('data' => json_encode($request));
        $data = $this->buildRequest($request);
        return $data;
    }

    public function getJsonData()
    {

        $params = array(
            "username" => $this->api_user_name,
            "password" => $this->api_password,
            "pid"      => $this->pid,
            "lid"      => $this->lid,
            "version"  => $this->api_version,
        );

        $params = http_build_query($params);
        $data   = array('data' => $params);
        $ch     = curl_init();

        $url = "https://dropshippingb2b.com/export/json.php";

        curl_setopt($ch, CURLOPT_URL, $url );
        curl_setopt($ch, CURLOPT_SSLVERSION , CURL_SSLVERSION_TLSv1_2);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $ce = curl_exec($ch);
        curl_close($ch);
        return $ce;
    }

    public function buildRequest ($data=[], $method='POST') {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiAddress);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $ce = curl_exec($ch);

        return $ce;

        curl_close($ch);
    }

    public function pushProductsOldDBFromApi ($apiProductsArrs,$category, $insertLimit = null)
    {
        return true;
        $insertedProductsCount  = 0;

        try{
            if(isset($apiProductsArrs)){
                $apiProductsArr =collect($apiProductsArrs)->unique('ean')->toArray();

                $categories = Category::get();

                $maping_categories  = DB::table('api_category_mapping')
                                                    ->where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)
                                                    ->where('is_complete',1)
                                                    ->select('api_category_id','mp_category_id')
                                                    ->get();
                $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
                $mapped_categories =[];
                foreach($maping_categories as $m_category){
                    $mapped_categories[$m_category->api_category_id] = $m_category->mp_category_id;
                }

                $api_brands = array_unique(array_column($apiProductsArrs,'brand_name'));
                $brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);

                foreach(array_chunk($apiProductsArr,1500) as $chunk_products){

                    $local_products = Product::with('drmProducts')
                                                ->where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)
                                                ->whereIn('ean',array_column($chunk_products,'ean'))
                                                ->get();
                    $salesTrac = [];
                    foreach($chunk_products as $product){

                        $product_brand = $brands[strtoupper($product->brand_name)] ?? $product->brand_name;

                        $staticDalivaryDays = array("0"=>"10", "1"=>"4", "2"=>"1");
                        // $vkPrice = round(($product->price + ($product->price *0.05)),2);
                        $uvp     = $product->retail_price + ($product->retail_price * 0.10);
                        $vkPrice  = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($product->price, $calculation, $uvp, \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_SHIPPING_COST);

                        $oldProduct = $local_products->where('api_id' , \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)->where('ean' , $product->ean)->first();

                        if($product->stock > 2){
                            $stock = floor((($product->stock * 90)/100));
                        }else{
                            $stock = $product->stock;
                        }
                        $status = 1;

                        if(isset($oldProduct)){

                            $update_column  = [];

                            $old_ek_price   = round($oldProduct->ek_price,2);
                            $api_ek         = round($product->price,2);
                            $old_stock      = $oldProduct->stock;

                            if($oldProduct->stock != $stock){
                                $update_column['stock']              = $stock;
                                $update_column['old_stock']          = $old_stock;
                                $update_column['stock_updated_at']   = \Carbon\Carbon::now();
                            }

                            if($old_ek_price != $api_ek){
                                $update_column['ek_price'] = $api_ek;
                                $update_column['old_ek_price'] = $old_ek_price;
                                $update_column['ek_price_updated_at'] = \Carbon\Carbon::now();
                                $update_column['vk_price'] = $vkPrice;
                                $update_column['old_vk_price'] = $oldProduct->vk_price;
                                $update_column['vk_price_updated_at'] = \Carbon\Carbon::now();
                            }

                            if($oldProduct->uvp != $uvp){
                                $update_column['uvp'] = $uvp;
                            }

                            if(count($update_column) > 0 && $oldProduct->item_number == $product->reference){

                                $drm_products = $oldProduct->drmProducts;
                                if($oldProduct->api_product_id != $product->id_product){
                                    $update_column['api_product_id'] = $product->id_product;
                                }

                                $insertedModel = $oldProduct->update($update_column);

                                if($insertedModel > 0){

                                    if(count($drm_products) > 0){
                                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$update_column);
                                        Log::info('B2B DRM Products Updated: '.$product->ean);
                                    }
                                    if($old_stock > $stock){
                                        $discres_stock = $old_stock - $stock;
                                        $salesTrac[] = [
                                            'marketplace_product_id'    => $product->id,
                                            'sales_stock'               => $discres_stock,
                                            'sales_amount'              => $discres_stock * $old_ek_price,
                                            'created_at'                => \Carbon\Carbon::now(),
                                        ];
                                    }
                                    
                                    // $this->duplicateService->duplicateEanCheck($product->ean);

                                    Log::info('B2bUhren Updated - '.$product->ean, $update_column);

                                }else{
                                    Log::info('B2bUhren not Updated - '.$product->ean, $update_column);
                                }
                            }else{
                                Log::info('B2bUhren not Updated - '.$product->ean, $update_column);
                            }

                        }else{

                            if(!empty($product->price) && !empty($product->ean) && !empty($product->images)){
                                $attribut = [];

                                $valid_ean = app(\App\Services\Marketplace\ProductService::class)->validateEAN($product->ean);
                                if(!$valid_ean){
                                    Log::error('B2bUhren Invalid EAN - '.$product->ean);
                                    continue;
                                }
                                if(!empty($product->attributes_array) && count($product->attributes_array) > 0){
                                    foreach($product->attributes_array as $p_a){
                                       $attribut[$p_a->group_name] = $p_a->value_name;
                                    }
                                }


                                $category_id =  !empty($mapped_categories[$product->brand_name]) ? $mapped_categories[$product->brand_name] : '';

                                if(empty($category_id)) continue;
                                

                                $images = [];
                                foreach ( $product->images as $media ) {
                                    $images[] = $media->image_path;
                                }

                                $status_set_attributes = [
                                    'item_number'   => $product->reference ?? null, 
                                    'brand'         => $product_brand ?? '', 
                                    'ek_price'      => $product->price ?? 1, 
                                    'uvp'           => $uvp ?? 1, 
                                    'delivery_days' => $staticDalivaryDays[$product->speed_shipping] ?? \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_DELIVERY_DAYS, 
                                    'vat'           => null
                                ];

                                    $data =[
                                        'api_id' => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                                        'api_product_id' => $product->id_product ?? null,
                                        'item_number'    => $product->reference ?? null,
                                        'name'           => $product->name ?? '',
                                        'brand'          => $product_brand ?? '',
                                        'ean'            => $product->ean ?? '',
                                        'ek_price'       => $product->price ?? 1,
                                        'vk_price'       => $vkPrice,
                                        'uvp'            => $uvp ?? 1,
                                        'description'    => $product->attributes ?? '',
                                        'image'          => $images ?? [],
                                        'stock'          => $stock ?? 0,
                                        'supplier_id'    => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_SUPPLIER_ID,
                                        'delivery_company_id' => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DELIVERY_COMPANY_ID,
                                        'status'         => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product->name,$product->attributes,$images,$stock,$status_set_attributes),
                                        'category_id'    => $category_id ?? 1,
                                        'api_category_id'=> $product->brand_name ?? '',
                                        'collection_id'  => 0,
                                        'shipping_method'=> \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                                        'shipping_cost'  => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_SHIPPING_COST,
                                        'category'       => '',

                                        'materials'      => $attribut['Armbandmaterial'] ?? '',
                                        'item_color'     => '',
                                        'gender'         => $attribut['Geschlecht'] ?? '',
                                        'item_size'      => $attribut['Gehäuseabmessung'] ?? '',
                                        'item_weight'    => $product->weight ?? '',
                                        'internel_stock' => 0,
                                        'misc'           => $product->attributes,
                                        'delivery_days'  => $staticDalivaryDays[$product->speed_shipping] ?? \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_DELIVERY_DAYS,
                                    ];

                                $insertedModel = Product::create($data);

                                $this->duplicateService->duplicateEanCheck($product->ean);

                                if (!$insertedModel) Log::error($product);
                                Log::info('B2bUhren Inserted - '.$product->id_product);

                                $insertedProductsCount += $insertedModel ? 1 : 0;
                            }
                        }
                    }
                    if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
                }

            }

            return "success";
        } catch(Exception $e){
            dd($e);
        }
        return response()->json([
            'status'                    => 1,
            'products_created_count'    => $insertedProductsCount,
        ]);
    }

    public function syncProductStockFromApi($data){
        return true;
        try{
            $updatedProductsCount = 0;
            $updatedProductsIds   = [];

            foreach($data as $eachOfproducts){
                    $product = Product::where('api_product_id', $eachOfproducts->id_product)->first();
                    if(isset($product) && ($product->stock != $eachOfproducts->stock)){
                        $oldStock = $product->stock_info['stock'];

                        $updatedAttributes = [
                              'stock_info->stock'             => $eachOfproducts->stock,
                              'stock_info->old_stock'         => $product->stock_info['stock'],
                              'stock_info->stock_updated_at'  => \Carbon\Carbon::now(),
                        ];
                        $product->update($updatedAttributes);

                        $drmProducts = $product->drmProducts()->get();
                        $drmStock = [];
                        $drmProductIds = [];
                        foreach ($drmProducts as $drmProduct) {
                            if ( $drmProduct->update([
                                'stock' => $eachOfproducts->stock,
                                'old_stock' => $drmProduct->stock,
                                'stock_updated_at' => \Carbon\Carbon::now(),
                            ]) ) {
                                $drmStock[] = $drmProduct->stock;
                                $drmProductIds[] = $drmProduct->id;
                            };
                        }

                        // Keeping History
                        \App\Models\Marketplace\ApiStockSyncHistory::create([
                            'api_id' => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                            'mp_product_id' => $product->id,
                            'bike_api_id' => $product->api_product_id,
                            'drm_product_ids' => $drmProductIds,
                            'mp_updated_atock' => $product->stock_info['stock'],
                            'drm_updated_stock' => $drmStock,
                            'sync_time' => \Carbon\Carbon::now(),
                            'status' => 1,
                            'old_stock' => $oldStock,
                        ]);
                        $updatedProductsCount++;
                        $updatedProductsIds[] = $product->id;
                    }else{
                        continue;
                    }
                }

            Log::info($updatedProductsCount, $updatedProductsIds);
        } catch(Exception $e){
            return response()->json([
                'status' => 'error',
                'msg'    => $e->getMessage(),
            ]);
        }

        return response()->json([
            'updated_products_count' => $updatedProductsCount,
            'updated_products_ids' => $updatedProductsIds,
        ]);
    }

    public function pushProductFromApi($products,$category){
        $insertedProductsCount  = 0;
        return true;
        try{
            if(isset($products) && !empty($products)){
                foreach(array_chunk($products,500) as $chunk_products){
                   foreach($chunk_products as $product){
                       $oldProduct = Product::where([
                           'api_id'=>\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                           'api_product_id' => $product->id_product
                           ])->first();
                        if(!empty($oldProduct)){
                            if(!empty($product->price) && !empty($product->ean) && !empty($product->images)){
                                if($oldProduct->stock_info['stock'] != $product->stock){
                                    $new_stock = $product->stock;
                                    $old_stock = $oldProduct->stock_info['stock'];
                                    $stock_updated_at = \Carbon\Carbon::now();

                                    $drmProducts = $oldProduct->drmProducts()->get();

                                    foreach ($drmProducts as $drmProduct) {
                                        $drmProduct->update([
                                            'stock' => $new_stock,
                                            'old_stock' => $drmProduct->stock,
                                            'stock_updated_at' => \Carbon\Carbon::now(),
                                        ]);
                                    }

                                }else{
                                    $new_stock = $product->stock;
                                    $old_stock = $oldProduct->stock_info['old_stock'];
                                    $stock_updated_at = $oldProduct->stock_info['stock_updated_at'];
                                }
                                $attribute = [];
                                if(!empty($product->attributes_array) && count($product->attributes_array) > 0){
                                    foreach($product->attributes_array as $p_attribute){
                                       $attribute[$p_attribute->group_name] = $p_attribute->value_name;
                                    }
                                }
                                if(!empty($category[str_replace(' ', '',$product->brand_name)])){
                                    $category_id = $this->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand_name)]);
                                }else{
                                    $category_id = $this->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand)]);
                                }

                                $images = [];
                                if(!empty($product->images)){
                                foreach ( $product->images as $media ) {
                                        $images[] = $media->image_path;
                                    }
                                }

                                $data = [
                                  'api_id'                 => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                                  'api_product_id'         => $product->id_product ?? null,
                                  'item_number'            => $product->reference ?? $product->ean,
                                  'name'                   => $product->name ?? '',
                                  'ean'                    => $product->ean,
                                  'ek_price'               => $product->price,
                                  'vk_price'               => $product->price + ($product->price * 0.05),
                                  'uvp_price'              => $product->retail_price ?? 1,
                                  'images'                 => $images ?? [],
                                  'supplier_id'            => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_SUPPLIER_ID,
                                  'delivery_company_id'    => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DELIVERY_COMPANY_ID,
                                  'status'                 => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                                  'collection_id'          => 0,
                                  'shipping_method'        => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                                  'shipping_cost'          => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_SHIPPING_COST,
                                  'stock_info' => [
                                      'stock'                     => $new_stock,
                                      'old_stock'                 => $old_stock,
                                      'stock_updated_at'          => $stock_updated_at,
                                      'internel_stock'            => '',
                                      'old_internel_stock'        => '',
                                      'internel_stock_updated_at' => '',
                                  ],
                                  'description'            => '',
                                  'product_misc' => [
                                      'brand'          => $product->brand_name ?? '',
                                      'color'          => '',
                                      'item_weight'    => $product->weight ?? '',
                                      'item_size'      => $attribute['Gehäuseabmessung'] ?? '',
                                      'gender'         => $attribute['Geschlecht'] ?? '',
                                      'materials'      => $attribute['Armbandmaterial'] ?? '',
                                      'tags'           => '',
                                      'note'           => '',
                                      'production_year'=> '',
                                      'delivery_days'  => ($product->speed_shipping ?? 0),
                                      'country_id'     => '',
                                      'language_id'    => '',
                                  ],
                                  'misc'               => $product->attributes,
                                ];
                                $updateProduct = $oldProduct->update($data);

                             $oldProduct->category()->sync($category_id);

                             if (!$updateProduct) Log::error($product);
                             Log::info('Updated - '.$oldProduct->id);
                            }
                        }else{
                            if(!empty($product->price) && !empty($product->ean) && !empty($product->images)){
                                $attribute = [];
                                if(!empty($product->attributes_array) && count($product->attributes_array) > 0){
                                    foreach($product->attributes_array as $p_attribute){
                                       $attribute[$p_attribute->group_name] = $p_attribute->value_name;
                                    }
                                }
                                if(!empty($category[str_replace(' ', '',$product->brand_name)])){
                                    $category_id = $this->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand_name)]);
                                }else{
                                    $category_id = $this->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand)]);
                                }

                                $images = [];
                                if(!empty($product->images)){
                                foreach ( $product->images as $media ) {
                                        $images[] = $media->image_path;
                                    }
                                }

                                $data = [
                                  'api_id'                 => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                                  'api_product_id'         => $product->id_product ?? null,
                                  'item_number'            => $product->reference ?? $product->ean,
                                  'name'                   => $product->name ?? '',
                                  'ean'                    => $product->ean,
                                  'ek_price'               => $product->price,
                                  'vk_price'               => $product->price + ($product->price * 0.05),
                                  'uvp_price'              => $product->retail_price ?? 1,
                                  'images'                 => $images ?? [],
                                  'supplier_id'            => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_SUPPLIER_ID,
                                  'delivery_company_id'    => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DELIVERY_COMPANY_ID,
                                  'status'                 => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                                  'collection_id'          => 0,
                                  'shipping_method'        => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                                  'shipping_cost'          => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_DEFAULT_SHIPPING_COST,
                                  // 'profit'                 => '',
                                  // 'vat'                    => '',
                                  'stock_info' => [
                                      'stock'                     => $product->stock,
                                      'old_stock'                 => '',
                                      'stock_updated_at'          => '',
                                      'internel_stock'            => '',
                                      'old_internel_stock'        => '',
                                      'internel_stock_updated_at' => '',
                                  ],
                                  'description'            => '',
                                  'product_misc' => [
                                      'brand'          => $product->brand_name ?? '',
                                      'color'          => '',
                                      'item_weight'    => $product->weight ?? '',
                                      'item_size'      => $attribute['Gehäuseabmessung'] ?? '',
                                      'gender'         => $attribute['Geschlecht'] ?? '',
                                      'materials'      => $attribute['Armbandmaterial'] ?? '',
                                      'tags'           => '',
                                      'note'           => '',
                                      'production_year'=> '',
                                      'delivery_days'  => ($product->speed_shipping ?? 0),
                                      'country_id'     => '',
                                      'language_id'    => '',
                                  ],
                                  'misc'                   => $product->attributes,
                                ];

                             $insertedModel = Product::updateOrCreate(['api_product_id' => $data['api_product_id']], $data);


                             $insertedModel->category()->sync($category_id);

                             if (!$insertedModel) Log::error($product);
                             Log::info('inserted - '.$product->id_product);
                             $insertedProductsCount += $insertedModel ? 1 : 0;
                            }
                        }
                   }
                }
            }

            return "success";
        } catch(Exception $e){
            dd($e);
        }
        return response()->json([
            'status'                    => 1,
            'products_created_count'    => $insertedProductsCount,
        ]);
    }

    public function getCategoryIdOfApiProduct ($apiCatId = null)
    {
        //Team env category for test
        $localArr = [
            "SCHMUCK"           => 31,
            "UHREN"             => 35,
            "MODEACCESSOIRES"   => 12,
            "BRILLEN"           => 31,
            "POSTEN"            => 31,
        ];

        //production env category
        $liveArr = [
            "SCHMUCK"           => 31,
            "UHREN"             => 35,
            "MODEACCESSOIRES"   => 12,
            "BRILLEN"           => 31,
            "POSTEN"            => 31,
        ];

        $arr = (env('APP_ENV') == 'local') ? $localArr : $liveArr;

        try {
            if(!empty($apiCatId)){
                $category = $arr[$apiCatId];
            }else{
                $category = 1;
            }

        } catch (\Exception $e) {

        }

        return $category ?? 1;

    }

    public function getShippingCountry(){
        $api_request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "get_shipping_country"
        );
        $api_request = array('data' => json_encode($api_request));
        $data = $this->buildRequest($api_request);
        return $data;
    }
    // Basket
    public function addItemsToBasket($items = []){
        $api_request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "add_items_to_basket",
            "items"           => $items,
        );

        $api_request = array('data' => json_encode($api_request));
        $data = $this->buildRequest($api_request);
        return $data;

    }

    public function orderCheckout(){
        $api_request = array(
            "uid"             => $this->uid,
            "pid"             => $this->pid,
            "lid"             => $this->lid,
            "key"             => $this->key,
            "api_version"     => $this->api_version,
            "request"         => "checkout",
            // "payment_method"  => "A",
            // "cc_number"       => "378282246310005",
            // "cc_exp_month"    => "05",
            // "cc_exp_year"     => "2017",
            // "cc_ccv"          => "1325"
        );
        $api_request = array('data' => json_encode($api_request));
        $data = $this->buildRequest($api_request);
        return $data;
    }

    public function shippingCountryArr(){
        return [
                "AF" => "1",
                "IT" => "2",
                "AT" => "5",
                "BE" => "6",
                "DK" => "7",
                "FI" => "8",
                "FR" => "9",
                "DE" => "10",
                "GR" => "13",
                "IE" => "14",
                "LU" => "16",
                "NL" => "18",
                "PT" => "19",
                "ES" => "20",
                "SE" => "21",
                "EE" => "22",
                "LV" => "24",
                "LT" => "25",
                "MT" => "26",
                "PL" => "28",
                "CZ" => "29",
                "SI" => "30",
                "BG" => "61",
                "CY" => "71",
                "HR" => "79",
                "RO" => "183",
                "SK" => "199",
                "HU" => "223",
                "PT" => "465",
                "AFs" => "476",
                "ES" => "477",
                "FR" => "478",
                "SM" => "3",
                "AD" => "4",
                "GI" => "11",
                "GB" => "12",
                "LI" => "15",
                "MC" => "17",
                "IL" => "23",
                "NO" => "27",
                "US" => "31",
                "CH" => "32",
                "AF" => "33",
                "AL" => "34",
                "DZ" => "35",
                "AO" => "36",
                "AI" => "37",
                "AQ" => "38",
                "AG" => "39",
                "AN" => "40",
                "SA" => "41",
                "AR" => "42",
                "AM" => "43",
                "AW" => "44",
                "AU" => "45",
                "AZ" => "46",
                "BS" => "47",
                "BH" => "48",
                "BD" => "49",
                "BB" => "50",
                "BZ" => "51",
                "BJ" => "52",
                "BM" => "53",
                "BT" => "54",
                "BY" => "55",
                "BO" => "56",
                "BA" => "57",
                "BW" => "58",
                "BR" => "59",
                "BN" => "60",
                "BF" => "62",
                "BI" => "63",
                "KH" => "64",
                "CM" => "65",
                "CA" => "66",
                "CV" => "67",
                "TD" => "68",
                "CL" => "69",
                "CN" => "70",
                "CO" => "72",
                "KM" => "73",
                "CG" => "74",
                "KR" => "75",
                "KP" => "76",
                "CI" => "77",
                "CR" => "78",
                "CU" => "80",
                "DM" => "81",
                "EC" => "82",
                "EG" => "83",
                "SV" => "84",
                "AE" => "85",
                "ER" => "86",
                "ET" => "87",
                "PH" => "88",
                "GA" => "89",
                "GM" => "90",
                "GE" => "91",
                "GS" => "92",
                "GH" => "93",
                "JM" => "94",
                "JP" => "95",
                "DJ" => "96",
                "JO" => "97",
                "GD" => "98",
                "GL" => "99",
                "GP" => "100",
                "GU" => "101",
                "GT" => "102",
                "GF" => "103",
                "GN" => "104",
                "GQ" => "105",
                "GW" => "106",
                "GY" => "107",
                "HT" => "108",
                "HN" => "109",
                "HK" => "110",
                "IN" => "111",
                "ID" => "112",
                "IR" => "113",
                "IQ" => "114",
                "IS" => "115",
                "BV" => "116",
                "KY" => "117",
                "CX" => "118",
                "CC" => "119",
                "CK" => "120",
                "FO" => "121",
                "FJ" => "122",
                "MP" => "123",
                "MH" => "124",
                "NF" => "125",
                "SB" => "126",
                "TC" => "127",
                "VG" => "128",
                "VI" => "129",
                "RS" => "130",
                "KZ" => "131",
                "KE" => "132",
                "KG" => "133",
                "KI" => "134",
                "KW" => "135",
                "LA" => "136",
                "LS" => "137",
                "LB" => "138",
                "LR" => "139",
                "LY" => "140",
                "MO" => "141",
                "MG" => "142",
                "MW" => "143",
                "MV" => "144",
                "MY" => "145",
                "ML" => "146",
                "MA" => "147",
                "MQ" => "148",
                "MR" => "149",
                "MU" => "150",
                "YT" => "151",
                "MX" => "152",
                "FM" => "153",
                "MD" => "154",
                "MN" => "155",
                "MS" => "156",
                "MZ" => "157",
                "MM" => "158",
                "NA" => "159",
                "NR" => "160",
                "NP" => "161",
                "NI" => "162",
                "NE" => "163",
                "NG" => "164",
                "NU" => "165",
                "NC" => "166",
                "NZ" => "167",
                "OM" => "168",
                "PK" => "169",
                "PW" => "170",
                "PA" => "171",
                "PG" => "172",
                "PY" => "173",
                "PE" => "174",
                "PN" => "175",
                "PF" => "176",
                "PR" => "177",
                "QA" => "178",
                "CF" => "179",
                "CG" => "180",
                "DO" => "181",
                "RE" => "182",
                "RW" => "184",
                "RU" => "185",
                "KN" => "186",
                "LC" => "187",
                "PM" => "188",
                "VC" => "189",
                "WS" => "190",
                "AS" => "191",
                "SH" => "192",
                "ST" => "193",
                "SN" => "194",
                "SC" => "195",
                "SL" => "196",
                "SG" => "197",
                "SY" => "198",
                "SO" => "200",
                "LK" => "201",
                "ZA" => "202",
                "SD" => "203",
                "SR" => "204",
                "SJ" => "205",
                "SZ" => "206",
                "TJ" => "207",
                "TH" => "208",
                "TW" => "209",
                "TZ" => "210",
                "IO" => "211",
                "TL" => "212",
                "TG" => "213",
                "TK" => "214",
                "TO" => "215",
                "TT" => "216",
                "TN" => "217",
                "TR" => "218",
                "TM" => "219",
                "TV" => "220",
                "UA" => "221",
                "UG" => "222",
                "UY" => "224",
                "UZ" => "225",
                "VU" => "226",
                "VE" => "227",
                "VN" => "228",
                "WF" => "229",
                "YE" => "230",
                "ZM" => "231",
                "ZW" => "232",
                "IC" => "466",
                "EA" => "467",
                "MK" => "468",
                "KS" => "469",
                "ME" => "470",
                "JE-GG" => "471",
            ];
    }
}
