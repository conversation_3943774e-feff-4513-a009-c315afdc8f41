<?php

namespace App\Services\Marketplace\BigBuyApi;

use App\Services\BaseService;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\Product;
use App\Models\ProductSync;
use Illuminate\Support\Facades\Redis;
use Log;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\AllApiUpdateSchedule;

class BigBuyApiService extends BaseService
{

    public $apiBaseURL;
    public $headers;
    public $ch;
    public $apiToken = 'NDY3ZDZjNmM0MWIzNzRjM2MxMmZmZWQ0NzMyMTI1NzkzOWNjYTMyZmM5MDFjZjNhZDEyNjcwOGI1NmUxMDQ2ZQ';

    public function __construct()
    {
        $this->apiBaseURL = \App\Enums\Marketplace\ApiResources::BigBuy;
        $this->headers           = [
            'Authorization: Bearer ' . $this->apiToken,
            'Content-Type: application/json',
            'Accept: application/json',
        ];
        $this->ch            = curl_init();
    }

    /**
     * Set API token based on cms_user_id
     * 
     * @param int|null $cms_user_id
     * @return string Original token for resetting later
     */
    private function setDynamicApiToken($cms_user_id = null)
    {
        $originalToken = $this->apiToken;
        if (isset($cms_user_id) && $cms_user_id == 4219) {
            $this->apiToken = 'NjUzMTE4Y2Q1YjE4NzQ4YjlmNWNlMzg1NjJmMzc3MTllOGY0ZWZkZjZmYjI0MTlhMzlmMjU3ZThkYTUzMGQ1Zg';
            // Update headers with new token
            $this->headers = [
                'Authorization: Bearer ' . $this->apiToken,
                'Content-Type: application/json',
                'Accept: application/json',
            ];
        }
        
        return $originalToken;
    }
    
    /**
     * Reset API token to original value
     * 
     * @param string $originalToken
     * @param int|null $cms_user_id
     * @return void
     */
    private function resetApiToken($originalToken, $cms_user_id = null)
    {
        if (isset($cms_user_id) && $cms_user_id == 4219) {
            $this->apiToken = $originalToken;
            // Reset headers to original token
            $this->headers = [
                'Authorization: Bearer ' . $this->apiToken,
                'Content-Type: application/json',
                'Accept: application/json',
            ];
        }
    }
    
    public function fetchData($apiAddress, $method = 'GET', $isoCode = 'de', $data = [], $cms_user_id = null)
    {
        $originalToken = $this->setDynamicApiToken($cms_user_id);
        $response = $this->buildRequest($apiAddress, $method, $isoCode, $data)
            ->getResponse();

        $this->resetApiToken($originalToken, $cms_user_id);
        return $response;
    }

    public function buildRequest($apiAddress, $method = 'GET', $isoCode = 'de', $data = [])
    {
        $url = $this->apiBaseURL . $apiAddress . '.json?isoCode=' . $isoCode;
        curl_setopt($this->ch, CURLOPT_URL, $url);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
        curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($this->ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
        if (!empty($data)) {
            curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
        }
        return $this;
    }

    public function getResponse()
    {
        try {
            $response = curl_exec($this->ch);
            // $status_code = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
            curl_close($this->ch);
            $this->ch = curl_init();
            // $response = json_decode($response, 1);
            // $response['status_code'] = $status_code;
            return json_decode($response, 1);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    // public function productStockSync(){
    //     $productStocks = collect(json_decode(Redis::get('bigbuy_product_stock'), true));
    //     Product::with('drmProducts')
    //                             ->where('api_id', 4)
    //                             ->select('marketplace_products.id','stock','old_stock','ean','api_product_id')
    //                             ->chunk(1500, function($local_products) use($productStocks){
    //         $product_stocks = $productStocks->whereIn('id',$local_products->pluck('api_product_id')->toArray());
    //         foreach($local_products as $local_product){
    //             $new_stock = $product_stocks->where('id',$local_product->api_product_id)
    //                                                     ->first()['stocks'][0]['quantity'] ?? 0;
    //             if(isset($new_stock)){
    //                 if($local_product->stock != $new_stock){
    //                     $local_product->stock = $new_stock;
    //                     $local_product->stock_updated_at = \Carbon\Carbon::now();
    //                     $local_product->old_stock = $local_product->stock;
    //                     Log::info('BigBuy product update - '.$local_product->ean);
    //                 }
    //             }else{
    //                 $local_product->stock = 0 ;
    //                 $local_product->stock_updated_at = \Carbon\Carbon::now();
    //                 $local_product->stock =  $local_product->stock ?? null;
    //             }
    //             $local_product->update();
    //         }
    //     });
    // }


    public function productStockSync($productStocks)
    {

        // Insert or update for next update time
        $api_id = 4;
        $update_time = \Carbon\Carbon::now()->addMinutes(10);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id, $update_time);

        // $api_update_table = new AllApiUpdateSchedule();
        // $api_update_schedule = $api_update_table->where('api_id', 4)->first();
        // if($api_update_schedule){
        //     $api_update_schedule->next_update_time = \Carbon\Carbon::now()->addMinutes(10);
        //     $api_update_schedule->update();
        //     Log::info('BigBuy api scheduler update for next update time');
        // }else{
        //     $attributes = [
        //         'api_id' 			 => 4,
        //         'next_update_time'	 => \Carbon\Carbon::now()->addMinutes(10),
        //     ];
        //     $api_update_table->create($attributes);
        //     Log::info('BigBuy api scheduler create for next update time');
        // }
        // End insert or update for next update time
        // Start stock sync
        // $stocks = [];
        // foreach ($productStocks as $stock) {
        //     array_push($stocks, [$stock['id'] => $stock['stocks'][0]['quantity']]);
        // }
        $api_stock = [];
        foreach ($productStocks as $key => $stock) {
            $api_stock[$key] = $stock['quantity'];
        }
        $local_products = collect(DB::table('marketplace_products')->where('api_id', 4)
            ->where('is_varient', 0)
            ->where('shipping_method', 1)
            ->where('country_id', 1)
            ->select('stock', 'api_product_id')
            ->get());
        $local_stocks = $local_products->pluck('stock', 'api_product_id')->toArray();
        $new_stocks = array_diff_assoc($api_stock, $local_stocks);
        $old_stocks = array_diff_assoc($local_stocks, $api_stock);
        $product = new Product();
        $salesTrac = [];
        foreach ($new_stocks as $key => $stock) {
            if (array_key_exists($key, $local_stocks)) {

                $product = $product->with('drmProducts')
                    ->where('api_id', 4)
                    ->where('shipping_method', 1)
                    ->where('country_id', 1)
                    ->where('is_varient', 0)
                    ->where('api_product_id', $key)
                    ->first();

                $mp_db_old_stock = $product->stock;
                $product->stock = $stock ?? 0;
                $product->stock_updated_at = \Carbon\Carbon::now();
                $product->old_stock = $mp_db_old_stock ?? 0;
                $drm_products = $product->drmProducts;

                if ($mp_db_old_stock > $stock) {
                    $discres_stock = $mp_db_old_stock - $stock;
                    $salesTrac[] = [
                        'marketplace_product_id'    => $product->id,
                        'sales_stock'               => $discres_stock,
                        'sales_amount'              => $discres_stock * $product->ek_price,
                        'created_at'                => \Carbon\Carbon::now(),
                    ];
                }

                if (count($drm_products) > 0) {
                    $data['stock'] = $stock ?? 0;
                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                    Log::info("BigBuy DRM product stock sync-" . $product->ean);
                }
                $product->update();
                Log::info('BigBuy product stock update - ' . $product->ean);
            }
        }
        $stock_out_sales_trac = [];
        foreach ($old_stocks as $key => $stock) {
            if (array_key_exists($key, $api_stock)) {
            } else {
                $product = $product->with('drmProducts')
                    ->where('api_id', 4)
                    ->where('shipping_method', 1)
                    ->where('is_varient', 0)
                    ->where('country_id', 1)
                    ->where('api_product_id', $key)->first();
                if ($product->stock > 0) {

                    $stock_out_sales_trac[] = [
                        'marketplace_product_id' => $product->id,
                        'sales_stock'           => $product->stock,
                        'sales_amount'          => $product->stock * $product->ek_price,
                        'created_at'            => \Carbon\Carbon::now(),
                    ];

                    $product->stock = "0";
                    $product->stock_updated_at = \Carbon\Carbon::now();
                    $product->old_stock = $product->stock ?? 0;
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $data['stock'] = "0";
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        Log::info("BigBuy DRM old product stock sync-" . $product->ean);
                    }
                    $product->update();
                    Log::info('BigBuy old product stock update - ' . $product->ean);
                }
            }
        }
        if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);

        if (count($stock_out_sales_trac) > 0) DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);

        // End stock sync
    }

    public function productPriceSync($productPrices)
    {
        info("BigBuy price sync srvice found");
        if (isset($productPrices)) {
            info("BigBuy api all price data found");
        }
        $productPrices = collect($productPrices);
        $api_product_prices = $productPrices->pluck('wholesalePrice', 'id')->toArray();
        $local_product = collect(DB::table('marketplace_products')->where('api_id', 4)->where('country_id', 1)
            ->select(DB::raw('CAST(ek_price AS DOUBLE) AS ek_price'), 'api_product_id')->where('is_varient',0)->get());

        if (isset($local_product)) {
            info("BigBuy local all product price found");
        }
        $local_product_price = $local_product->pluck('ek_price', 'api_product_id')->toArray();
        $new_prices          = array_diff_assoc($api_product_prices, $local_product_price);

        if (count($new_prices) < 1) {
            info("BigBuy price sync difference not found");
            return true;
        }

        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        $local_category_im = DB::table('marketplace_categories')->pluck('im_handel','id')->toArray();
        $enterpriceOrTrialUserList = app(\App\Services\Marketplace\ProductService::class)->enterpriceOrTrialUserList() ?? [];

        foreach ($new_prices as $key => $price) {
            if (array_key_exists($key, $local_product_price)) {
                $local_product = Product::with('drmProducts')
                    ->where('api_id', 4)
                    ->where('api_product_id', $key)
                    ->where('is_varient',0)
                    ->where('country_id', 1)
                    ->select('id','ean','uvp','api_id','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','im_handel','category_id','item_number','shipping_cost','update_status','real_shipping_cost','misc')
                    ->first();

                app(\App\Services\Marketplace\ProductService::class)->mpNewPriceCalculation($local_product,$price,$calculation,$local_category_im,$local_product->real_shipping_cost,false,$enterpriceOrTrialUserList);
                
                // $local_product->ek_price = $price;
                // $new_vk_price = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($price, $calculation, $local_product->uvp, $local_product->shipping_cost);

                // $local_product->vk_price = $new_vk_price;
                // $drm_products = $local_product->drmProducts;

                // if (count($drm_products) > 0) {

                //     $data['vk_price'] = round($new_vk_price, 2);
                //     app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                //     info("BigBuy DRM product price sync-" . $local_product->ean);
                // }
                // $local_product->update();
                // info('BigBuy product price update - ' . $local_product->ean);
            }
        }
    }

    public function productUVPSync($productPrices)
    {
        Log::info("BigBuy price sync srvice found");
        if (isset($productPrices)) {
            Log::info("BigBuy api all price data found");
        }
        $api_id = 4;
        $productPrices = collect($productPrices)->pluck('retailPrice', 'id')->toArray();

        $api_product_prices = array_map(function ($value) {
            return round(($value + ($value * 0.10)), 2);
        }, $productPrices);

        $local_product_price = Product::where('api_id', $api_id)->where('country_id', 1)->where('is_varient',0)->pluck('uvp', 'api_product_id')->toArray();

        if (isset($local_product_price)) {
            Log::info("BigBuy local all product price found");
        }

        $new_price_ean  = array_diff_assoc($api_product_prices, array_map('floatval', $local_product_price));
        $api_product_ids = array_keys(array_intersect_key($new_price_ean, $local_product_price));

        if (isset($api_product_ids)) {
            Log::info("BigBuy price sync difference found");
        }

        foreach (array_chunk($api_product_ids, 10000) as $ids) {
            $local_mp_products = Product::with('drmProducts')
                ->select('id', 'api_id', 'api_product_id', 'uvp', 'ean')
                ->where('api_id', $api_id)
                ->where('is_varient',0)
                ->where('country_id', 1)
                ->whereIn('api_product_id', $ids)
                ->get();

            foreach ($local_mp_products as $product) {
                $new_uvp = $new_price_ean[$product->api_product_id];

                if ($product->uvp !=  $new_uvp) {

                    $product->uvp = $new_uvp;

                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $data['uvp'] = round($new_uvp, 2);
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        Log::info("Bigbuy API DRM product uvp sync-" . $product->ean);
                    }

                    $product->update();
                    Log::info("Bigbuy API product uvp sync-" . $product->ean);
                }
            }
        }
    }

    public function insertProductsToMarketplace($products)
    {
        $api_id            = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        $deliveryCompanyId = \App\Enums\Marketplace\ApiResources::BIGBUY_DELIVERY_COMPANY_ID;
        $shippingMethod    = \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING;

        $maping_categories  = DB::table('api_category_mapping')
            ->where('api_id', $api_id)
            ->where('is_complete', 1)
            ->select('api_category_id', 'mp_category_id')
            ->get();

        $categories = [];
        foreach ($maping_categories as $m_category) {
            $categories[$m_category->api_category_id] = $m_category->mp_category_id;
        }

        $brand_url = 'rest/catalog/manufacturers';
        $product_brands = app(BigBuyApiService::class)->fetchData($brand_url);

        $api_brands = array_unique(array_column($product_brands, 'name'));
        $local_brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);
        $product_brands = collect($product_brands)->pluck('name', 'id')->toArray();
        try {

            if (isset($products)) {
                $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
                Log::info('Redis find all product for insert');
                // BigBuy Stock
                $bigbuy_product_stock = json_decode(Redis::get('bigbuy_product_stock'), true);

                // BigBuy All Products Information
                $bigbuy_product_information = json_decode(Redis::get('bigbuy_product_information'), true);
                $bigbuy_product_information = (array)$bigbuy_product_information;

                // BigBuy All Products Image
                $images = json_decode(Redis::get('bigbuy_product_images'), true);
                $images = (array)$images;

                // BigBuy All Products Category
                $productCategory = json_decode(Redis::get('bigbuy_product_category'), true);
                $productCategory = (array)$productCategory;

                // BigBuy All Products Shippingcosts
                $productShippingCosts = json_decode(Redis::get('bigbuy_product_shippingcosts'), true);
                $productShippingCosts = (array)$productShippingCosts;

                // BigBuy All Products Brand
                $productBrand = json_decode(Redis::get('bigbuy_product_brand'), true);

                foreach ($products as $product) {
                    $images_arr        = [];

                    if (isset($product['id']) && array_key_exists($product['id'], $images)) {
                        foreach ($images[$product['id']] as $image) {
                            $images_arr[] = $image['url'];
                        }
                    }
                    if (isset($product['id']) && array_key_exists($product['id'], $bigbuy_product_information)) {
                        $product_name  = $bigbuy_product_information[$product['id']]['name'];
                        $discriptions  = $bigbuy_product_information[$product['id']]['description'];
                    }

                    if (isset($product['id']) && array_key_exists($product['id'], $bigbuy_product_stock)) {
                        $product_stock = $bigbuy_product_stock[$product['id']]['stocks'][0]['quantity'] ?? 0;
                        $product_handling_day = $bigbuy_product_stock[$product['id']]['stocks'][0]['minHandlingDays'] ?? 2;
                    }
                    if (isset($product['sku']) && array_key_exists($product['sku'], $productShippingCosts)) {
                        $product_shipping_cost = $productShippingCosts[$product['sku']]['cost'] ?? 6.60;
                        $product_carrier_name = $productShippingCosts[$product['sku']]['carrierName'] ?? 'GLS';
                    } else {
                        $product_shipping_cost = 6.60;
                        $product_carrier_name = 'GLS';
                    }

                    // if(isset($product->manufacturer) && array_key_exists($product->manufacturer,$productBrand)){
                    //     $product_brand = $productBrand[$product->manufacturer]['name'];
                    // }
                    if (isset($product['manufacturer'])) {
                        $product_brand = $local_brands[$productBrand[$product['manufacturer']]];
                    }

                    if ($product['condition'] == 'NEW' && !empty($product['ean13']) && !empty($product_name) && !empty($images_arr)) {
                        $ean                = $product['ean13'];
                        $api_product_id     = $product['id'];
                        $item_number        = $product['sku'];
                        $ek_price           = $product['wholesalePrice'];
                        // $vkPrice 			= $ek_price + ($ek_price * 0.05);
                        $uvp                = $product['retailPrice'] + ($product['retailPrice'] * 0.10);
                        $vat                = $product['taxRate'];
                        $tax_type           = $product['taxId'];
                        $vkPrice            = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, $product_shipping_cost);
                        if (array_key_exists($product['id'], $productCategory)) {
                            $product_category = !empty($categories[$productCategory[$product['id']]['category']]) ? $categories[$productCategory[$product['id']]['category']] : 68;
                            // $product_category  = $this->getCategoryIdOfApiProduct($productCategory[$product['id']]['category']);
                            $product_api_category_id = $productCategory[$product['id']]['category'];
                        }

                        // if(isset($product_category) && $product_category != 68){
                        //     $product_status = \App\Enums\Marketplace\ProductStatus::ACTIVE;
                        // }else{
                        //     $product_status = \App\Enums\Marketplace\ProductStatus::PENDING;
                        // }
                        $all_local_product = new Product();
                        $old_product = $all_local_product->where([
                            'api_id'        => $api_id,
                            'ean'           => $ean
                        ])->first();

                        if (isset($old_product)) {
                        } else {

                            // INSERT
                            // $product = new Product();
                            // $product->setConnection('drm_team');
                            // $exist_product = $product->where('name', $product_name)->exists();

                            $status_set_attributes = [
                                'item_number'   => $item_number, 
                                'brand'         => $product_brand, 
                                'ek_price'      => $ek_price, 
                                'uvp'           => $uvp, 
                                'delivery_days' => $product_handling_day, 
                                'vat'           => $vat
                            ];
    
                            $attributes = [
                                'api_id'              => $api_id,
                                'api_product_id'     => $api_product_id,
                                'name'                 => $product_name ?? '',
                                'brand'                 => $product_brand ?? '',
                                'ean'                 => $ean ?? '',
                                'ek_price'             => $ek_price ?? '',
                                'vk_price'             => $vkPrice ?? '',
                                'uvp'                 => $uvp ?? '',
                                'description'         => $discriptions ?? '',
                                'category_id'         => $product_category,
                                'api_category_id'    => $product_api_category_id ?? '',
                                'image'                 => $images_arr ?? '',
                                'item_color'         => '',
                                'status'             => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name, $discriptions, $images_arr, $product_stock, $status_set_attributes),
                                'item_number'         => $item_number ?? '',
                                'shipping_method'     => $shippingMethod,
                                'shipping_cost'         => $product_shipping_cost,
                                'stock'                 => $product_stock ?? 0,
                                'item_weight'         => '',
                                'item_size'             => '',
                                'production_year'     => '',
                                'materials'             => '',
                                'gender'             => '',
                                'vat'                => $vat,
                                'is_top_product'     => 1,
                                'delivery_days'         => isset($product_handling_day) ? ($product_handling_day + 1) : 2,
                                'collection_id'      => 0,
                                'internel_stock'     => 0,
                                'delivery_company_id' => $deliveryCompanyId,
                                'misc'               => $product_carrier_name,
                                'tax_type'           => $tax_type
                            ];

                            if ($attributes['category_id'] == 68) continue;

                            $final_all_local_product = new Product();
                            // $final_all_local_product->setConnection('drm_team');
                            $final_all_local_product->create($attributes);
                            dd($final_all_local_product);
                            Log::info("BigBuy Inserted Ean- " . $ean);
                        }
                    }
                    // break;
                }
                Log::info("BigBuy All New Products Update successfully");
            }
        } catch (\Exception $e) {

            return $e->getMessage();
        }
    }

    public function newBuildRequest($apiAddress, $method = 'GET', $isoCode = 'de', $data = [], $cms_user_id = null)
    {

        try {
            $originalToken = $this->setDynamicApiToken($cms_user_id);
            $url = $this->apiBaseURL . $apiAddress . '.json?isoCode=' . $isoCode;
            curl_setopt($this->ch, CURLOPT_URL, $url);
            curl_setopt($this->ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
            curl_setopt($this->ch, CURLOPT_POST, $method == "POST" ? 1 : 0);
            curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
            if (!empty($data)) {
                curl_setopt($this->ch, CURLOPT_POSTFIELDS, $data);
            }

            $response = curl_exec($this->ch);
            $status_code = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
            curl_close($this->ch);
            $this->ch = curl_init();
            $response = json_decode($response, 1);
            $response['status_code'] = $status_code;
            $this->resetApiToken($originalToken, $cms_user_id);
            return $response;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }


    public function getCategoryIdOfApiProduct($apiCatId)
    {
        //local evn category for test
        $localArrOwnMachin = [
            1 => 15, 2 => 15, 3 => 13, 4 => 15, 5 => 15, 6 => 16, 7 => 15,
            8 => 17, 9 => 18, 10 => 19, 11 => 19, 12 => 20, 13 => 21, 14 => 22,
        ];
        //Team env category for test
        $localArr = [
            3477 =>    111,
            3473 =>    51,
            3472 =>    83,
            3475 =>    82,
            3470 =>    64,
            3465 =>    66,
            3464 =>    79,
            3463 =>    29,
            3467 =>    78,
            3462 =>    41,
            3466 =>    79,
            3469 =>    83,
            3468 =>    81,
            // 3468 =>	81,80,
            3471 =>    29,
            3476 =>    25,
            3474 =>    66,
            3198 =>    16,
            3018 =>    142,
            3004 =>    98,
            2584 =>    31,
            3433 =>    33,
            2841 =>    16,
            2568 =>    87,
            2514 =>    91,
            3415 =>    87,
            2849 =>    20,
            2648 =>    194,
            2868 =>    21,
            2793 =>    28,
            3053 =>    40,
            3058 =>    40,
            3117 =>    40,
            3138 =>    40,
            3139 =>    40,
            3069 =>    40,
            2651 =>    28,
            3261 =>    145,
            2709 =>    203,
            2531 =>    87,
            3080 =>    40,
            3077 =>    40,
            3452 =>    12,
            2429 =>    60,
            2602 =>    31,
            2635 =>    35,
            2839 =>    16,
            3181 =>    179,
            2636 =>    46,
            2834 =>    46,
            2640 =>    46,
            3091 =>    111,
            2725 =>    111,
            3314 =>    34,
            3378 =>    34,
            3377 =>    34,
            3381 =>    34,
            3460 =>    36,
            3112 =>    11,
            2533 =>    87,
            2736 =>    67,
            3055 =>    40,
            2671 =>    68,
            3188 =>    36,
            2956 =>    36,
            3459 =>    36,
            3186 =>    36,
            3456 =>    36,
            3454 =>    36,
            3445 =>    36,
            3224 =>    36,
            3455 =>    36,
            2574 =>    9,
            3211 =>    9,
            2593 =>    9,
            2470 =>    59,
            2424 =>    60,
            3284 =>    60,
            3312 =>    183,
            2987 =>    183,
            3281 =>    183,
            3278 =>    183,
            3283 =>    60,
            3282 =>    60,
            2597 =>    60,
            3243 =>    159,
            3344 =>    31,
            3326 =>    163,
            3400 =>    159,
            2623 =>    194,
            3246 =>    34,
            3228 =>    34,
            3231 =>    34,
            3230 =>    34,
            3229 =>    176,
            2951 =>    194,
            3089 =>    194,
            3068 =>    40,
            3307 =>    174,
            3032 =>    142,
            2459 =>    70,
            3066 =>    40,
            3428 =>    94,
            3426 =>    94,
            3423 =>    94,
            2598 =>    31,
            2962 =>    12,
            2402 =>    28,
            2938 =>    47,
            3133 =>    40,
            3396 =>    15,
            3391 =>    15,
            2941 =>    46,
            2943 =>    119,
            2923 =>    46,
            2638 =>    119,
            2986 =>    12,
            2518 =>    85,
            3108 =>    40,
            3260 =>    46,
            2561 =>    87,
            2565 =>    61,
            2576 =>    128,
            2954 =>    31,
            2456 =>    187,
            2475 =>    95,
            3127 =>    40,
            2720 =>    111,
            3059 =>    40,
            3394 =>    15,
            3402 =>    15,
            2915 =>    26,
            3142 =>    185,
            3311 =>    185,
            2965 =>    12,
            3019 =>    33,
            2879 =>    18,
            2553 =>    87,
            2548 =>    87,
            3350 =>    133,
            3027 =>    142,
            2858 =>    16,
            2831 =>    191,
            2803 =>    191,
            2642 =>    16,
            2913 =>    16,
            2650 =>    16,
            2791 =>    16,
            2525 =>    86,
            2479 =>    20,
            3215 =>    32,
            2999 =>    198,
            3103 =>    12,
            2995 =>    35,
            2457 =>    110,
            3436 =>    34,
            2878 =>    16,
            2610 =>    28,
            2400 =>    28,
            2474 =>    83,
            2440 =>    28,
            3167 =>    61,
            2550 =>    61,
            3168 =>    61,
            2993 =>    16,
            2792 =>    16,
            3047 =>    40,
            3021 =>    48,
            2843 =>    191,
            2922 =>    16,
            3398 =>    27,
            3050 =>    40,
            2823 =>    191,
            2918 =>    119,
            2628 =>    84,
            2645 =>    15,
            2880 =>    15,
            3180 =>    28,
            3008 =>    95,
            2884 =>    18,
            2930 =>    18,
            2808 =>    18,
            2909 =>    14,
            2978 =>    186,
            2477 =>    20,
            3187 =>    28,
            2629 =>    172,
            3156 =>    105,
            2405 =>    20,
            2904 =>    33,
            2882 =>    46,
            2946 =>    194,
            2905 =>    36,
            3200 =>    33,
            2617 =>    124,
            3202 =>    20,
            2932 =>    26,
            2931 =>    48,
            3134 =>    40,
            2649 =>    48,
            2409 =>    95,
            2502 =>    179,
            2506 =>    179,
            2713 =>    195,
            2957 =>    179,
            3076 =>    40,
            3148 =>    40,
            3088 =>    31,
            3086 =>    40,
            3011 =>    40,
            3116 =>    40,
            3095 =>    40,
            3100 =>    40,
            3109 =>    40,
            3083 =>    40,
            3044 =>    40,
            3081 =>    40,
            3082 =>    40,
            3084 =>    40,
            3087 =>    40,
            3111 =>    40,
            3110 =>    40,
            2641 =>    36,
            2896 =>    36,
            3016 =>    200,
            2566 =>    88,
            3238 =>    171,
            3237 =>    171,
            3364 =>    171,
            3447 =>    171,
            3022 =>    142,
            2910 =>    48,
            3367 =>    165,
            2926 =>    121,
            2619 =>    106,
            2806 =>    16,
            2408 =>    108,
            3028 =>    48,
            3339 =>    158,
            2675 =>    158,
            2939 =>    158,
            2492 =>    158,
            3193 =>    17,
            3303 =>    187,
            2734 =>    157,
            2412 =>    95,
            3280 =>    34,
            3313 =>    32,
            2975 =>    32,
            3393 =>    15,
            2530 =>    86,
            2436 =>    95,
            2972 =>    32,
            2415 =>    96,
            3249 =>    34,
            2940 =>    106,
            3221 =>    27,
            3331 =>    160,
            3324 =>    160,
            3333 =>    160,
            3332 =>    160,
            3338 =>    160,
            3334 =>    160,
            3325 =>    160,
            3330 =>    174,
            3171 =>    61,
            3328 =>    160,
            3060 =>    40,
            2860 =>    21,
            2890 =>    21,
            2862 =>    21,
            2865 =>    21,
            2864 =>    21,
            2861 =>    21,
            2863 =>    21,
            2867 =>    21,
            2866 =>    21,
            2869 =>    21,
            2859 =>    21,
            2988 =>    21,
            2885 =>    21,
            2889 =>    21,
            2435 =>    22,
            2735 =>    130,
            2952 =>    22,
            3113 =>    193,
            3359 =>    27,
            2596 =>    92,
            2726 =>    92,
            3149 =>    128,
            2537 =>    87,
            2536 =>    87,
            2676 =>    61,
            2444 =>    61,
            3340 =>    174,
            2733 =>    187,
            3051 =>    40,
            2462 =>    92,
            2532 =>    86,
            3122 =>    40,
            3140 =>    40,
            3135 =>    40,
            3136 =>    40,
            2953 =>    13,
            3251 =>    44,
            2480 =>    187,
            2887 =>    20,
            2812 =>    16,
            2704 =>    30,
            2438 =>    44,
            2434 =>    44,
            3341 =>    158,
            2977 =>    32,
            2979 =>    31,
            3351 =>    27,
            2544 =>    86,
            3170 =>    201,
            2526 =>    86,
            2520 =>    86,
            3194 =>    86,
            2528 =>    86,
            2652 =>    86,
            3195 =>    201,
            3192 =>    201,
            3327 =>    163,
            3023 =>    73,
            3118 =>    53,
            2906 =>    125,
            3244 =>    164,
            2800 =>    27,
            3120 =>    40,
            3003 =>    94,
            2519 =>    12,
            3075 =>    202,
            2674 =>    28,
            2974 =>    32,
            2505 =>    32,
            2443 =>    25,
            3010 =>    25,
            2912 =>    48,
            2825 =>    16,
            2706 =>    94,
            2468 =>    105,
            2452 =>    43,
            2963 =>    12,
            2998 =>    198,
            2994 =>    35,
            3146 =>    140,
            3252 =>    34,
            3014 =>    142,
            2980 =>    12,
            3217 =>    12,
            2820 =>    16,
            2829 =>    16,
            3130 =>    40,
            2920 =>    119,
            2851 =>    47,
            3204 =>    33,
            2891 =>    61,
            3208 =>    196,
            2985 =>    12,
            3248 =>    165,
            3432 =>    68,
            2835 =>    16,
            2821 =>    16,
            3157 =>    64,
            2417 =>    141,
            3383 =>    15,
            2936 =>    26,
            2535 =>    86,
            3258 =>    166,
            2700 =>    107,
            2604 =>    31,
            3121 =>    40,
            2908 =>    36,
            2917 =>    36,
            3001 =>    198,
            3421 =>    87,
            3009 =>    90,
            3435 =>    172,
            3041 =>    142,
            3417 =>    87,
            2997 =>    136,
            2958 =>    120,
            2503 =>    120,
            3049 =>    40,
            3057 =>    40,
            3405 =>    10,
            2971 =>    12,
            2722 =>    104,
            2573 =>    12,
            2902 =>    12,
            3444 =>    12,
            2451 =>    98,
            2450 =>    98,
            2407 =>    101,
            2950 =>    194,
            2433 =>    102,
            3144 =>    27,
            2716 =>    120,
            2933 =>    26,
            3300 =>    185,
            3203 =>    33,
            3137 =>    40,
            3119 =>    40,
            2627 =>    21,
            2916 =>    21,
            2921 =>    119,
            2944 =>    119,
            2715 =>    120,
            2446 =>    120,
            2728 =>    62,
            3299 =>    12,
            3040 =>    142,
            2488 =>    142,
            3098 =>    140,
            3429 =>    25,
            2673 =>    28,
            2428 =>    101,
            2467 =>    187,
            2730 =>    187,
            2729 =>    187,
            3005 =>    95,
            2469 =>    94,
            2439 =>    95,
            3368 =>    15,
            2658 =>    188,
            3316 =>    188,
            3017 =>    205,
            3226 =>    60,
            3370 =>    12,
            2832 =>    47,
            3450 =>    10,
            2942 =>    16,
            2621 =>    125,
            2703 =>    13,
            2818 =>    16,
            2798 =>    16,
            3212 =>    168,
            3357 =>    168,
            3214 =>    168,
            2637 =>    46,
            2421 =>    13,
            3449 =>    36,
            3295 =>    12,
            3375 =>    12,
            2852 =>    48,
            2850 =>    48,
            2578 =>    128,
            2654 =>    206,
            2562 =>    88,
            3318 =>    112,
            2489 =>    62,
            3392 =>    15,
            3101 =>    12,
            2569 =>    88,
            2551 =>    61,
            3085 =>    87,
            2453 =>    97,
            2815 =>    98,
            3362 =>    185,
            2538 =>    88,
            3038 =>    88,
            2557 =>    88,
            2546 =>    99,
            3106 =>    12,
            2984 =>    12,
            2615 =>    15,
            3360 =>    27,
            3397 =>    12,
            3037 =>    202,
            2504 =>    179,
            2959 =>    120,
            2656 =>    16,
            2677 =>    48,
            2455 =>    187,
            3182 =>    28,
            2847 =>    119,
            2425 =>    95,
            3151 =>    95,
            2795 =>    16,
            3024 =>    142,
            2406 =>    101,
            2708 =>    203,
            2618 =>    124,
            2644 =>    47,
            2804 =>    47,
            3458 =>    36,
            2888 =>    46,
            2809 =>    191,
            2892 =>    17,
            2914 =>    46,
            2966 =>    12,
            2591 =>    12,
            2721 =>    120,
            3225 =>    120,
            2413 =>    120,
            2466 =>    190,
            2447 =>    29,
            2830 =>    191,
            3102 =>    12,
            3227 =>    191,
            2816 =>    191,
            2838 =>    191,
            3104 =>    40,
            2797 =>    47,
            3461 =>    36,
            3410 =>    15,
            3411 =>    15,
            3380 =>    15,
            3152 =>    102,
            2661 =>    31,
            3172 =>    29,
            2840 =>    191,
            2482 =>    29,
            3399 =>    15,
            3067 =>    40,
            2874 =>    15,
            2876 =>    15,
            3026 =>    33,
            2496 =>    159,
            2679 =>    208,
            3290 =>    34,
            2710 =>    28,
            2508 =>    90,
            2510 =>    90,
            2509 =>    90,
            2511 =>    90,
            3145 =>    75,
            3039 =>    140,
            2945 =>    16,
            2811 =>    16,
            3132 =>    40,
            3074 =>    40,
            3072 =>    40,
            3070 =>    40,
            3114 =>    40,
            2595 =>    31,
            3036 =>    142,
            2657 =>    192,
            2458 =>    192,
            2655 =>    33,
            3030 =>    142,
            2929 =>    46,
            2969 =>    12,
            2401 =>    168,
            3031 =>    142,
            3096 =>    140,
            2947 =>    48,
            3052 =>    40,
            2805 =>    16,
            2558 =>    88,
            2967 =>    12,
            3366 =>    202,
            2581 =>    122,
            2454 =>    70,
            2579 =>    126,
            2577 =>    12,
            3061 =>    40,
            2828 =>    52,
            3390 =>    15,
            2639 =>    46,
            2927 =>    46,
            3235 =>    171,
            3240 =>    171,
            3191 =>    91,
            3190 =>    91,
            3189 =>    91,
            2903 =>    28,
            3048 =>    40,
            2875 =>    16,
            2877 =>    16,
            2701 =>    28,
            2590 =>    12,
            2607 =>    12,
            2731 =>    187,
            3090 =>    70,
            3361 =>    209,
            2445 =>    53,
            3358 =>    209,
            2437 =>    95,
            3033 =>    205,
            2587 =>    33,
            3438 =>    174,
            3045 =>    210,
            2600 =>    31,
            3201 =>    33,
            2982 =>    12,
            3169 =>    61,
            2589 =>    172,
            3266 =>    172,
            3150 =>    211,
            3147 =>    40,
            2826 =>    191,
            3292 =>    27,
            3308 =>    27,
            3365 =>    34,
            3213 =>    168,
            3219 =>    168,
            3220 =>    168,
            2410 =>    95,
            2845 =>    194,
            3373 =>    32,
            2432 =>    95,
            3013 =>    30,
            2461 =>    110,
            2846 =>    16,
            2441 =>    100,
            2827 =>    191,
            2485 =>    29,
            2599 =>    120,
            3302 =>    185,
            3105 =>    12,
            2899 =>    75,
            2560 =>    88,
            2702 =>    31,
            2540 =>    61,
            3388 =>    52,
            2719 =>    52,
            3384 =>    52,
            3349 =>    158,
            2976 =>    32,
            2582 =>    32,
            2724 =>    32,
            2613 =>    143,
            2611 =>    143,
            3448 =>    36,
            3404 =>    87,
            3408 =>    87,
            3322 =>    204,
            3276 =>    204,
            3272 =>    204,
            3389 =>    204,
            3386 =>    204,
            2549 =>    91,
            2563 =>    61,
            3414 =>    61,
            3413 =>    61,
            3407 =>    15,
            2622 =>    125,
            3457 =>    36,
            2534 =>    87,
            2842 =>    191,
            2799 =>    16,
            2712 =>    195,
            3093 =>    40,
            3107 =>    40,
            3078 =>    40,
            3034 =>    40,
            3099 =>    142,
            2523 =>    61,
            2603 =>    196,
            2471 =>    196,
            2836 =>    191,
            3210 =>    196,
            3097 =>    40,
            2723 =>    94,
            3254 =>    182,
            3205 =>    125,
            3206 =>    125,
            2620 =>    125,
            2634 =>    134,
            3293 =>    12,
            3424 =>    121,
            2714 =>    195,
            2853 =>    16,
            2420 =>    197,
            2586 =>    198,
            2559 =>    61,
            2552 =>    61,
            3317 =>    112,
            3416 =>    86,
            3420 =>    86,
            2556 =>    61,
            2554 =>    87,
            3409 =>    87,
            3419 =>    87,
            3412 =>    87,
            3241 =>    34,
            2925 =>    46,
            2810 =>    46,
            2807 =>    16,
            2813 =>    16,
            2817 =>    16,
            2698 =>    121,
            2934 =>    26,
            2601 =>    123,
            3434 =>    33,
            2585 =>    123,
            2448 =>    33,
            3315 =>    123,
            2572 =>    123,
            3273 =>    174,
            2919 =>    119,
            3294 =>    174,
            3216 =>    174,
            3274 =>    174,
            3297 =>    174,
            3269 =>    174,
            2614 =>    26,
            3268 =>    34,
            3298 =>    174,
            2973 =>    174,
            3270 =>    174,
            3207 =>    46,
            3131 =>    40,
            3179 =>    28,
            3255 =>    177,
            3356 =>    34,
            2848 =>    191,
            3342 =>    158,
            3427 =>    120,
            3372 =>    32,
            3387 =>    15,
            2594 =>    51,
            3079 =>    40,
            3143 =>    42,
            3320 =>    209,
            3323 =>    209,
            3379 =>    209,
            3319 =>    209,
            3054 =>    40,
            2883 =>    48,
            2837 =>    191,
            3335 =>    174,
            3035 =>    21,
            2970 =>    12,
            2964 =>    12,
            2937 =>    47,
            2626 =>    47,
            2588 =>    27,
            3304 =>    185,
            3401 =>    15,
            2464 =>    92,
            2646 =>    47,
            2732 =>    28,
            3321 =>    204,
            3288 =>    177,
            3285 =>    177,
            3287 =>    177,
            3286 =>    177,
            3291 =>    177,
            3289 =>    177,
            2414 =>    94,
            3382 =>    15,
            3385 =>    15,
            3012 =>    33,
            2699 =>    121,
            2717 =>    121,
            2707 =>    94,
            3256 =>    178,
            2416 =>    95,
            2539 =>    61,
            2460 =>    192,
            3329 =>    160,
            3336 =>    160,
            2814 =>    46,
            2802 =>    47,
            2907 =>    18,
            3310 =>    188,
            3275 =>    12,
            2928 =>    46,
            3346 =>    160,
            3197 =>    86,
            2711 =>    121,
            2924 =>    46,
            2705 =>    121,
            2625 =>    48,
            2911 =>    48,
            2718 =>    48,
            2996 =>    35,
            3000 =>    198,
            2727 =>    28,
            2575 =>    12,
            3264 =>    204,
            2605 =>    209,
            2833 =>    191,
            2955 =>    20,
            3437 =>    46,
            3363 =>    185,
            2583 =>    142,
            3094 =>    142,
            2493 =>    40,
            3071 =>    40,
            3056 =>    40,
            3063 =>    40,
            3064 =>    40,
            3065 =>    40,
            3062 =>    40,
            2886 =>    48,
            3209 =>    196,
            2871 =>    21,
            3257 =>    181,
            3431 =>    94,
            2427 =>    94,
            2824 =>    191,
            2431 =>    95,
            3395 =>    126,
            2881 =>    105,
            2404 =>    138,
            3006 =>    105,
            2463 =>    93,
            3007 =>    105,
            2430 =>    95,
            2844 =>    16,
            2633 =>    139,
            3025 =>    71,
            2992 =>    71,
            2487 =>    62,
            2606 =>    31,
            2419 =>    28,
            2580 =>    12,
            2442 =>    95,
            2616 =>    143,
            2660 =>    33,
            2738 =>    187,
            3296 =>    12,
            2948 =>    194,
            3073 =>    40,
            2564 =>    88,
            3301 =>    185,
            2949 =>    194,
            2801 =>    16,
            3199 =>    16,
            2624 =>    125,
            2900 =>    15,
            2664 =>    75,
            2399 =>    28,
            2555 =>    199,
            2689 =>    75,
            2527 =>    87,
            2403 =>    28,
            2547 =>    61,
            3046 =>    40,
            2666 =>    34,
            2694 =>    75,
            2653 =>    199,
            2491 =>    34,
            2692 =>    75,
            2501 =>    11,
            2659 =>    90,
            2682 =>    75,
            2507 =>    90,
            2570 =>    12,
            2687 =>    211,
            2686 =>    187,
            2571 =>    142,
            2690 =>    75,
            2683 =>    75,
            2688 =>    75,
            2685 =>    75,
            2609 =>    16,
            2696 =>    40,
            2691 =>    75,
            2684 =>    9,
            2693 =>    75,
            2669 =>    75,
            2663 =>    75,
            2668 =>    12,
            2667 =>    75,
            2695 =>    75,
            2672 =>    208,
            2678 =>    210,

        ];

        //produc=>tion env category
        $liveArr = [
            3477 =>    111,
            3473 =>    51,
            3472 =>    83,
            3475 =>    82,
            3470 =>    64,
            3465 =>    66,
            3464 =>    79,
            3463 =>    29,
            3467 =>    78,
            3462 =>    41,
            3466 =>    79,
            3469 =>    83,
            3468 =>    81,
            // 3468 =>	81,80,
            3471 =>    29,
            3476 =>    25,
            3474 =>    66,
            3198 =>    16,
            3018 =>    142,
            3004 =>    98,
            2584 =>    31,
            3433 =>    33,
            2841 =>    16,
            2568 =>    87,
            2514 =>    91,
            3415 =>    87,
            2849 =>    20,
            2648 =>    194,
            2868 =>    21,
            2793 =>    28,
            3053 =>    40,
            3058 =>    40,
            3117 =>    40,
            3138 =>    40,
            3139 =>    40,
            3069 =>    40,
            2651 =>    28,
            3261 =>    145,
            2709 =>    203,
            2531 =>    87,
            3080 =>    40,
            3077 =>    40,
            3452 =>    12,
            2429 =>    60,
            2602 =>    31,
            2635 =>    35,
            2839 =>    16,
            3181 =>    179,
            2636 =>    46,
            2834 =>    46,
            2640 =>    46,
            3091 =>    111,
            2725 =>    111,
            3314 =>    34,
            3378 =>    34,
            3377 =>    34,
            3381 =>    34,
            3460 =>    36,
            3112 =>    11,
            2533 =>    87,
            2736 =>    67,
            3055 =>    40,
            2671 =>    68,
            3188 =>    36,
            2956 =>    36,
            3459 =>    36,
            3186 =>    36,
            3456 =>    36,
            3454 =>    36,
            3445 =>    36,
            3224 =>    36,
            3455 =>    36,
            2574 =>    9,
            3211 =>    9,
            2593 =>    9,
            2470 =>    59,
            2424 =>    60,
            3284 =>    60,
            3312 =>    183,
            2987 =>    183,
            3281 =>    183,
            3278 =>    183,
            3283 =>    60,
            3282 =>    60,
            2597 =>    60,
            3243 =>    159,
            3344 =>    31,
            3326 =>    163,
            3400 =>    159,
            2623 =>    194,
            3246 =>    34,
            3228 =>    34,
            3231 =>    34,
            3230 =>    34,
            3229 =>    176,
            2951 =>    194,
            3089 =>    194,
            3068 =>    40,
            3307 =>    174,
            3032 =>    142,
            2459 =>    70,
            3066 =>    40,
            3428 =>    94,
            3426 =>    94,
            3423 =>    94,
            2598 =>    31,
            2962 =>    12,
            2402 =>    28,
            2938 =>    47,
            3133 =>    40,
            3396 =>    15,
            3391 =>    15,
            2941 =>    46,
            2943 =>    119,
            2923 =>    46,
            2638 =>    119,
            2986 =>    12,
            2518 =>    85,
            3108 =>    40,
            3260 =>    46,
            2561 =>    87,
            2565 =>    61,
            2576 =>    128,
            2954 =>    31,
            2456 =>    187,
            2475 =>    95,
            3127 =>    40,
            2720 =>    111,
            3059 =>    40,
            3394 =>    15,
            3402 =>    15,
            2915 =>    26,
            3142 =>    185,
            3311 =>    185,
            2965 =>    12,
            3019 =>    33,
            2879 =>    18,
            2553 =>    87,
            2548 =>    87,
            3350 =>    133,
            3027 =>    142,
            2858 =>    16,
            2831 =>    191,
            2803 =>    191,
            2642 =>    16,
            2913 =>    16,
            2650 =>    16,
            2791 =>    16,
            2525 =>    86,
            2479 =>    20,
            3215 =>    32,
            2999 =>    198,
            3103 =>    12,
            2995 =>    35,
            2457 =>    110,
            3436 =>    34,
            2878 =>    16,
            2610 =>    28,
            2400 =>    28,
            2474 =>    83,
            2440 =>    28,
            3167 =>    61,
            2550 =>    61,
            3168 =>    61,
            2993 =>    16,
            2792 =>    16,
            3047 =>    40,
            3021 =>    48,
            2843 =>    191,
            2922 =>    16,
            3398 =>    27,
            3050 =>    40,
            2823 =>    191,
            2918 =>    119,
            2628 =>    84,
            2645 =>    15,
            2880 =>    15,
            3180 =>    28,
            3008 =>    95,
            2884 =>    18,
            2930 =>    18,
            2808 =>    18,
            2909 =>    14,
            2978 =>    186,
            2477 =>    20,
            3187 =>    28,
            2629 =>    172,
            3156 =>    105,
            2405 =>    20,
            2904 =>    33,
            2882 =>    46,
            2946 =>    194,
            2905 =>    36,
            3200 =>    33,
            2617 =>    124,
            3202 =>    20,
            2932 =>    26,
            2931 =>    48,
            3134 =>    40,
            2649 =>    48,
            2409 =>    95,
            2502 =>    179,
            2506 =>    179,
            2713 =>    195,
            2957 =>    179,
            3076 =>    40,
            3148 =>    40,
            3088 =>    31,
            3086 =>    40,
            3011 =>    40,
            3116 =>    40,
            3095 =>    40,
            3100 =>    40,
            3109 =>    40,
            3083 =>    40,
            3044 =>    40,
            3081 =>    40,
            3082 =>    40,
            3084 =>    40,
            3087 =>    40,
            3111 =>    40,
            3110 =>    40,
            2641 =>    36,
            2896 =>    36,
            3016 =>    200,
            2566 =>    88,
            3238 =>    171,
            3237 =>    171,
            3364 =>    171,
            3447 =>    171,
            3022 =>    142,
            2910 =>    48,
            3367 =>    165,
            2926 =>    121,
            2619 =>    106,
            2806 =>    16,
            2408 =>    108,
            3028 =>    48,
            3339 =>    158,
            2675 =>    158,
            2939 =>    158,
            2492 =>    158,
            3193 =>    17,
            3303 =>    187,
            2734 =>    157,
            2412 =>    95,
            3280 =>    34,
            3313 =>    32,
            2975 =>    32,
            3393 =>    15,
            2530 =>    86,
            2436 =>    95,
            2972 =>    32,
            2415 =>    96,
            3249 =>    34,
            2940 =>    106,
            3221 =>    27,
            3331 =>    160,
            3324 =>    160,
            3333 =>    160,
            3332 =>    160,
            3338 =>    160,
            3334 =>    160,
            3325 =>    160,
            3330 =>    174,
            3171 =>    61,
            3328 =>    160,
            3060 =>    40,
            2860 =>    21,
            2890 =>    21,
            2862 =>    21,
            2865 =>    21,
            2864 =>    21,
            2861 =>    21,
            2863 =>    21,
            2867 =>    21,
            2866 =>    21,
            2869 =>    21,
            2859 =>    21,
            2988 =>    21,
            2885 =>    21,
            2889 =>    21,
            2435 =>    22,
            2735 =>    130,
            2952 =>    22,
            3113 =>    193,
            3359 =>    27,
            2596 =>    92,
            2726 =>    92,
            3149 =>    128,
            2537 =>    87,
            2536 =>    87,
            2676 =>    61,
            2444 =>    61,
            3340 =>    174,
            2733 =>    187,
            3051 =>    40,
            2462 =>    92,
            2532 =>    86,
            3122 =>    40,
            3140 =>    40,
            3135 =>    40,
            3136 =>    40,
            2953 =>    13,
            3251 =>    44,
            2480 =>    187,
            2887 =>    20,
            2812 =>    16,
            2704 =>    30,
            2438 =>    44,
            2434 =>    44,
            3341 =>    158,
            2977 =>    32,
            2979 =>    31,
            3351 =>    27,
            2544 =>    86,
            3170 =>    201,
            2526 =>    86,
            2520 =>    86,
            3194 =>    86,
            2528 =>    86,
            2652 =>    86,
            3195 =>    201,
            3192 =>    201,
            3327 =>    163,
            3023 =>    73,
            3118 =>    53,
            2906 =>    125,
            3244 =>    164,
            2800 =>    27,
            3120 =>    40,
            3003 =>    94,
            2519 =>    12,
            3075 =>    202,
            2674 =>    28,
            2974 =>    32,
            2505 =>    32,
            2443 =>    25,
            3010 =>    25,
            2912 =>    48,
            2825 =>    16,
            2706 =>    94,
            2468 =>    105,
            2452 =>    43,
            2963 =>    12,
            2998 =>    198,
            2994 =>    35,
            3146 =>    140,
            3252 =>    34,
            3014 =>    142,
            2980 =>    12,
            3217 =>    12,
            2820 =>    16,
            2829 =>    16,
            3130 =>    40,
            2920 =>    119,
            2851 =>    47,
            3204 =>    33,
            2891 =>    61,
            3208 =>    196,
            2985 =>    12,
            3248 =>    165,
            3432 =>    68,
            2835 =>    16,
            2821 =>    16,
            3157 =>    64,
            2417 =>    141,
            3383 =>    15,
            2936 =>    26,
            2535 =>    86,
            3258 =>    166,
            2700 =>    107,
            2604 =>    31,
            3121 =>    40,
            2908 =>    36,
            2917 =>    36,
            3001 =>    198,
            3421 =>    87,
            3009 =>    90,
            3435 =>    172,
            3041 =>    142,
            3417 =>    87,
            2997 =>    136,
            2958 =>    120,
            2503 =>    120,
            3049 =>    40,
            3057 =>    40,
            3405 =>    10,
            2971 =>    12,
            2722 =>    104,
            2573 =>    12,
            2902 =>    12,
            3444 =>    12,
            2451 =>    98,
            2450 =>    98,
            2407 =>    101,
            2950 =>    194,
            2433 =>    102,
            3144 =>    27,
            2716 =>    120,
            2933 =>    26,
            3300 =>    185,
            3203 =>    33,
            3137 =>    40,
            3119 =>    40,
            2627 =>    21,
            2916 =>    21,
            2921 =>    119,
            2944 =>    119,
            2715 =>    120,
            2446 =>    120,
            2728 =>    62,
            3299 =>    12,
            3040 =>    142,
            2488 =>    142,
            3098 =>    140,
            3429 =>    25,
            2673 =>    28,
            2428 =>    101,
            2467 =>    187,
            2730 =>    187,
            2729 =>    187,
            3005 =>    95,
            2469 =>    94,
            2439 =>    95,
            3368 =>    15,
            2658 =>    188,
            3316 =>    188,
            3017 =>    205,
            3226 =>    60,
            3370 =>    12,
            2832 =>    47,
            3450 =>    10,
            2942 =>    16,
            2621 =>    125,
            2703 =>    13,
            2818 =>    16,
            2798 =>    16,
            3212 =>    168,
            3357 =>    168,
            3214 =>    168,
            2637 =>    46,
            2421 =>    13,
            3449 =>    36,
            3295 =>    12,
            3375 =>    12,
            2852 =>    48,
            2850 =>    48,
            2578 =>    128,
            2654 =>    206,
            2562 =>    88,
            3318 =>    112,
            2489 =>    62,
            3392 =>    15,
            3101 =>    12,
            2569 =>    88,
            2551 =>    61,
            3085 =>    87,
            2453 =>    97,
            2815 =>    98,
            3362 =>    185,
            2538 =>    88,
            3038 =>    88,
            2557 =>    88,
            2546 =>    99,
            3106 =>    12,
            2984 =>    12,
            2615 =>    15,
            3360 =>    27,
            3397 =>    12,
            3037 =>    202,
            2504 =>    179,
            2959 =>    120,
            2656 =>    16,
            2677 =>    48,
            2455 =>    187,
            3182 =>    28,
            2847 =>    119,
            2425 =>    95,
            3151 =>    95,
            2795 =>    16,
            3024 =>    142,
            2406 =>    101,
            2708 =>    203,
            2618 =>    124,
            2644 =>    47,
            2804 =>    47,
            3458 =>    36,
            2888 =>    46,
            2809 =>    191,
            2892 =>    17,
            2914 =>    46,
            2966 =>    12,
            2591 =>    12,
            2721 =>    120,
            3225 =>    120,
            2413 =>    120,
            2466 =>    190,
            2447 =>    29,
            2830 =>    191,
            3102 =>    12,
            3227 =>    191,
            2816 =>    191,
            2838 =>    191,
            3104 =>    40,
            2797 =>    47,
            3461 =>    36,
            3410 =>    15,
            3411 =>    15,
            3380 =>    15,
            3152 =>    102,
            2661 =>    31,
            3172 =>    29,
            2840 =>    191,
            2482 =>    29,
            3399 =>    15,
            3067 =>    40,
            2874 =>    15,
            2876 =>    15,
            3026 =>    33,
            2496 =>    159,
            2679 =>    208,
            3290 =>    34,
            2710 =>    28,
            2508 =>    90,
            2510 =>    90,
            2509 =>    90,
            2511 =>    90,
            3145 =>    75,
            3039 =>    140,
            2945 =>    16,
            2811 =>    16,
            3132 =>    40,
            3074 =>    40,
            3072 =>    40,
            3070 =>    40,
            3114 =>    40,
            2595 =>    31,
            3036 =>    142,
            2657 =>    192,
            2458 =>    192,
            2655 =>    33,
            3030 =>    142,
            2929 =>    46,
            2969 =>    12,
            2401 =>    168,
            3031 =>    142,
            3096 =>    140,
            2947 =>    48,
            3052 =>    40,
            2805 =>    16,
            2558 =>    88,
            2967 =>    12,
            3366 =>    202,
            2581 =>    122,
            2454 =>    70,
            2579 =>    126,
            2577 =>    12,
            3061 =>    40,
            2828 =>    52,
            3390 =>    15,
            2639 =>    46,
            2927 =>    46,
            3235 =>    171,
            3240 =>    171,
            3191 =>    91,
            3190 =>    91,
            3189 =>    91,
            2903 =>    28,
            3048 =>    40,
            2875 =>    16,
            2877 =>    16,
            2701 =>    28,
            2590 =>    12,
            2607 =>    12,
            2731 =>    187,
            3090 =>    70,
            3361 =>    209,
            2445 =>    53,
            3358 =>    209,
            2437 =>    95,
            3033 =>    205,
            2587 =>    33,
            3438 =>    174,
            3045 =>    210,
            2600 =>    31,
            3201 =>    33,
            2982 =>    12,
            3169 =>    61,
            2589 =>    172,
            3266 =>    172,
            3150 =>    211,
            3147 =>    40,
            2826 =>    191,
            3292 =>    27,
            3308 =>    27,
            3365 =>    34,
            3213 =>    168,
            3219 =>    168,
            3220 =>    168,
            2410 =>    95,
            2845 =>    194,
            3373 =>    32,
            2432 =>    95,
            3013 =>    30,
            2461 =>    110,
            2846 =>    16,
            2441 =>    100,
            2827 =>    191,
            2485 =>    29,
            2599 =>    120,
            3302 =>    185,
            3105 =>    12,
            2899 =>    75,
            2560 =>    88,
            2702 =>    31,
            2540 =>    61,
            3388 =>    52,
            2719 =>    52,
            3384 =>    52,
            3349 =>    158,
            2976 =>    32,
            2582 =>    32,
            2724 =>    32,
            2613 =>    143,
            2611 =>    143,
            3448 =>    36,
            3404 =>    87,
            3408 =>    87,
            3322 =>    204,
            3276 =>    204,
            3272 =>    204,
            3389 =>    204,
            3386 =>    204,
            2549 =>    91,
            2563 =>    61,
            3414 =>    61,
            3413 =>    61,
            3407 =>    15,
            2622 =>    125,
            3457 =>    36,
            2534 =>    87,
            2842 =>    191,
            2799 =>    16,
            2712 =>    195,
            3093 =>    40,
            3107 =>    40,
            3078 =>    40,
            3034 =>    40,
            3099 =>    142,
            2523 =>    61,
            2603 =>    196,
            2471 =>    196,
            2836 =>    191,
            3210 =>    196,
            3097 =>    40,
            2723 =>    94,
            3254 =>    182,
            3205 =>    125,
            3206 =>    125,
            2620 =>    125,
            2634 =>    134,
            3293 =>    12,
            3424 =>    121,
            2714 =>    195,
            2853 =>    16,
            2420 =>    197,
            2586 =>    198,
            2559 =>    61,
            2552 =>    61,
            3317 =>    112,
            3416 =>    86,
            3420 =>    86,
            2556 =>    61,
            2554 =>    87,
            3409 =>    87,
            3419 =>    87,
            3412 =>    87,
            3241 =>    34,
            2925 =>    46,
            2810 =>    46,
            2807 =>    16,
            2813 =>    16,
            2817 =>    16,
            2698 =>    121,
            2934 =>    26,
            2601 =>    123,
            3434 =>    33,
            2585 =>    123,
            2448 =>    33,
            3315 =>    123,
            2572 =>    123,
            3273 =>    174,
            2919 =>    119,
            3294 =>    174,
            3216 =>    174,
            3274 =>    174,
            3297 =>    174,
            3269 =>    174,
            2614 =>    26,
            3268 =>    34,
            3298 =>    174,
            2973 =>    174,
            3270 =>    174,
            3207 =>    46,
            3131 =>    40,
            3179 =>    28,
            3255 =>    177,
            3356 =>    34,
            2848 =>    191,
            3342 =>    158,
            3427 =>    120,
            3372 =>    32,
            3387 =>    15,
            2594 =>    51,
            3079 =>    40,
            3143 =>    42,
            3320 =>    209,
            3323 =>    209,
            3379 =>    209,
            3319 =>    209,
            3054 =>    40,
            2883 =>    48,
            2837 =>    191,
            3335 =>    174,
            3035 =>    21,
            2970 =>    12,
            2964 =>    12,
            2937 =>    47,
            2626 =>    47,
            2588 =>    27,
            3304 =>    185,
            3401 =>    15,
            2464 =>    92,
            2646 =>    47,
            2732 =>    28,
            3321 =>    204,
            3288 =>    177,
            3285 =>    177,
            3287 =>    177,
            3286 =>    177,
            3291 =>    177,
            3289 =>    177,
            2414 =>    94,
            3382 =>    15,
            3385 =>    15,
            3012 =>    33,
            2699 =>    121,
            2717 =>    121,
            2707 =>    94,
            3256 =>    178,
            2416 =>    95,
            2539 =>    61,
            2460 =>    192,
            3329 =>    160,
            3336 =>    160,
            2814 =>    46,
            2802 =>    47,
            2907 =>    18,
            3310 =>    188,
            3275 =>    12,
            2928 =>    46,
            3346 =>    160,
            3197 =>    86,
            2711 =>    121,
            2924 =>    46,
            2705 =>    121,
            2625 =>    48,
            2911 =>    48,
            2718 =>    48,
            2996 =>    35,
            3000 =>    198,
            2727 =>    28,
            2575 =>    12,
            3264 =>    204,
            2605 =>    209,
            2833 =>    191,
            2955 =>    20,
            3437 =>    46,
            3363 =>    185,
            2583 =>    142,
            3094 =>    142,
            2493 =>    40,
            3071 =>    40,
            3056 =>    40,
            3063 =>    40,
            3064 =>    40,
            3065 =>    40,
            3062 =>    40,
            2886 =>    48,
            3209 =>    196,
            2871 =>    21,
            3257 =>    181,
            3431 =>    94,
            2427 =>    94,
            2824 =>    191,
            2431 =>    95,
            3395 =>    126,
            2881 =>    105,
            2404 =>    138,
            3006 =>    105,
            2463 =>    93,
            3007 =>    105,
            2430 =>    95,
            2844 =>    16,
            2633 =>    139,
            3025 =>    71,
            2992 =>    71,
            2487 =>    62,
            2606 =>    31,
            2419 =>    28,
            2580 =>    12,
            2442 =>    95,
            2616 =>    143,
            2660 =>    33,
            2738 =>    187,
            3296 =>    12,
            2948 =>    194,
            3073 =>    40,
            2564 =>    88,
            3301 =>    185,
            2949 =>    194,
            2801 =>    16,
            3199 =>    16,
            2624 =>    125,
            2900 =>    15,
            2664 =>    75,
            2399 =>    28,
            2555 =>    199,
            2689 =>    75,
            2527 =>    87,
            2403 =>    28,
            2547 =>    61,
            3046 =>    40,
            2666 =>    34,
            2694 =>    75,
            2653 =>    199,
            2491 =>    34,
            2692 =>    75,
            2501 =>    11,
            2659 =>    90,
            2682 =>    75,
            2507 =>    90,
            2570 =>    12,
            2687 =>    211,
            2686 =>    187,
            2571 =>    142,
            2690 =>    75,
            2683 =>    75,
            2688 =>    75,
            2685 =>    75,
            2609 =>    16,
            2696 =>    40,
            2691 =>    75,
            2684 =>    9,
            2693 =>    75,
            2669 =>    75,
            2663 =>    75,
            2668 =>    12,
            2667 =>    75,
            2695 =>    75,
            2672 =>    208,
            2678 =>    210,
        ];
        // $arr = (env('APP_ENV') == 'local') ? $localArr : $liveArr;
        try {
            $team_category = new Category();
            // $category = $team_category->setConnection('drm_team')->where('id', $localArr[$apiCatId])->first();
            $category = \App\Models\Marketplace\Category::where('id', $localArr[$apiCatId])->first();
        } catch (\Exception $e) {
        }
        return $category->id ?? 68;
    }

    public function varientProductStockSync($productStocks)
    {

        // Insert or update for next update time
        $api_id = 4;
        $update_time = \Carbon\Carbon::now()->addMinutes(10);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id, $update_time);

        // $stocks = [];
        // foreach ($productStocks as $stock) {
        //     array_push($stocks, [$stock['id'] => $stock['stocks'][0]['quantity']]);
        // }
        // $api_stock = [];
        // foreach ($stocks as $stock) {
        //     $api_stock[key($stock)] = current($stock);
        // }

        $api_stock = [];
        foreach ($productStocks as $key => $stock) {
            $api_stock[$key] = $stock['quantity'];
        }
        $local_products = collect(DB::table('marketplace_products')->where('api_id', 4)
            ->where('is_varient', 1)
            ->select('stock', 'api_product_id')
            ->get());
        $local_stocks = $local_products->pluck('stock', 'api_product_id')->toArray();
        $new_stocks = array_diff_assoc($api_stock, $local_stocks);
        $old_stocks = array_diff_assoc($local_stocks, $api_stock);
        
        $product = new Product();
        $salesTrac = [];
        foreach ($new_stocks as $key => $stock) {
            if (array_key_exists($key, $local_stocks)) {

                $product = $product->with('drmProducts')
                    ->where('api_id', 4)
                    ->where('is_varient', 1)
                    ->where('api_product_id', $key)
                    ->first();

                $mp_db_old_stock = $product->stock;
                $product->stock = $stock ?? 0;
                $product->stock_updated_at = \Carbon\Carbon::now();
                $product->old_stock = $mp_db_old_stock ?? 0;
                $drm_products = $product->drmProducts;

                if ($mp_db_old_stock > $stock) {
                    $discres_stock = $mp_db_old_stock - $stock;
                    $salesTrac[] = [
                        'marketplace_product_id'    => $product->id,
                        'sales_stock'               => $discres_stock,
                        'sales_amount'              => $discres_stock * $product->ek_price,
                        'created_at'                => \Carbon\Carbon::now(),
                    ];
                }

                if (count($drm_products) > 0) {
                    $data['stock'] = $stock ?? 0;
                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                    Log::info("BigBuy DRM product stock sync-" . $product->ean);
                }
                $product->update();
                Log::info('BigBuy product stock update - ' . $product->ean);
            }
        }
        $stock_out_sales_trac = [];
        foreach ($old_stocks as $key => $stock) {
            if (array_key_exists($key, $api_stock)) {
            } else {
                $product = $product->with('drmProducts')
                    ->where('api_id', 4)
                    ->where('is_varient', 1)
                    ->where('api_product_id', $key)->first();
                if ($product->stock > 0) {

                    $stock_out_sales_trac[] = [
                        'marketplace_product_id' => $product->id,
                        'sales_stock'           => $product->stock,
                        'sales_amount'          => $product->stock * $product->ek_price,
                        'created_at'            => \Carbon\Carbon::now(),
                    ];

                    $product->stock = "0";
                    $product->stock_updated_at = \Carbon\Carbon::now();
                    $product->old_stock = $product->stock ?? 0;
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $data['stock'] = "0";
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        Log::info("BigBuy DRM old product stock sync-" . $product->ean);
                    }
                    $product->update();
                    Log::info('BigBuy old product stock update - ' . $product->ean);
                }
            }
        }
        if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);

        if (count($stock_out_sales_trac) > 0) DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);

        // End stock sync
    }

    public function variantProductPriceUpdate($apiProducts)
    {

        $api_product_prices = collect($apiProducts)->pluck('wholesalePrice', 'id')->toArray();

        $local_product_price = Product::where('api_id', 4)
            ->where('is_varient', 1)
            ->pluck('ek_price', 'api_product_id')
            ->toArray();

        $new_prices = array_diff_assoc($api_product_prices, $local_product_price);
        $array_api_ids = array_keys(array_intersect_key($local_product_price, $new_prices));
       
        if (count($array_api_ids) < 1) {
            info("BigBuy varient price sync difference not found");
            return true;
        }

        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        $local_category_im = DB::table('marketplace_categories')->pluck('im_handel','id')->toArray();

        foreach (array_chunk($array_api_ids, 1500) as $ids) {

            $local_products = Product::with('drmProducts')
                ->where('is_varient', 1)
                ->where('api_id', 4)
                ->whereIn('api_product_id', $ids)
                ->select('id','ean','uvp','api_id','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','im_handel','category_id','item_number','shipping_cost','update_status','real_shipping_cost','misc')
                ->get();

            foreach ($local_products as $local_product) {
                if(isset($new_prices[$local_product->api_product_id])){
                    $new_price = $new_prices[$local_product->api_product_id];
                    if ($local_product->ek_price != $new_price) {
                        app(\App\Services\Marketplace\ProductService::class)->mpNewPriceCalculation($local_product,$new_price,$calculation,$local_category_im,$local_product->real_shipping_cost,false);
                        
                        // $local_product->ek_price = number_format($new_price, 2);
                        // $new_vk_price = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($new_price, $calculation, $local_product->uvp, $local_product->shipping_cost);
                        // $local_product->vk_price = number_format($new_vk_price, 2);

                        // $drm_products = $local_product->drmProducts;

                        // if (count($drm_products) > 0) {
                        //     $data['vk_price'] = round($new_vk_price, 2);
                        //     app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        //     info("BigBuy variant DRM product price sync-" . $local_product->ean);
                        // }
                        // $local_product->update();
                        // info("BigBuy variant product price sync-" . $local_product->ean);
                    }
                }
            }
        }
    }
}
