<?php

namespace App\Services\Marketplace\BigBuyApi;

use App\Models\Marketplace\Product;
use App\Enums\Marketplace\MpProductCountry;
use App\Services\Marketplace\BigBuyApi\StockUpdate;

class StockSyncProcess
{

    private const API_ID = 4;
    private const SHIPPING_METHOD = 1;

    public function process($productStocks)
    {
        $update_time = \Carbon\Carbon::now()->addMinutes(10);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule(self::API_ID, $update_time);

        $api_stock = [];
        foreach ($productStocks as $key => $stock) {
            $api_stock[$key] = $stock['quantity'];
        }

        $local_products = Product::where([
            'api_id' => self::API_ID,
            'is_varient' =>  0,
            'shipping_method' => self::SHIPPING_METHOD
        ])
            ->select('stock', 'api_product_id', 'country_id')
            ->get();

        $merged_api_ids = $this->syncStockForCountries($local_products, $api_stock);
        app(StockUpdate::class)->stockUpdate($merged_api_ids, $api_stock);

        $this->getStockOutProductIds($local_products, $api_stock);
        dd("updated");
    }

    /**
     * Synchronize product stock for specified countries.
     *
     * @param \Illuminate\Support\Collection $localProducts
     * @param array $apiStock
     * @return array
     */
    private function syncStockForCountries($local_products, $api_stock): array
    {
        $country_ids = [
            MpProductCountry::GERMANY => 'Germany',
            MpProductCountry::SPAIN => 'Spain',
            MpProductCountry::SWITZERLAND => 'Switzerland',
            MpProductCountry::AUSTRIA => 'Austria'
        ];

        $merged_api_ids = [];

        foreach ($country_ids as $country_id => $country_name) {
            info("BigBuy $country_name stock sync process..............");
            $local_ids = $local_products->where('country_id', $country_id)->pluck('stock', 'api_product_id')->toArray();
            $new_stocks = array_diff_assoc($api_stock, $local_ids);
            $apiProductIdsToUpdate = array_keys(array_intersect_key($new_stocks, $local_ids));

            if (!empty($apiProductIdsToUpdate)) {
                $merged_api_ids = array_merge($merged_api_ids, $apiProductIdsToUpdate);
            } else {
                info("No stock updates required for $country_name.");
            }
        }

        return array_unique($merged_api_ids);
    }

    private function getStockOutProductIds($local_products, $api_stock)
    {
        $local_product_id = $local_products->where('stock', '!=', 0)->pluck('api_product_id')->toArray();
        $stock_out_ids        = array_diff($local_product_id, array_keys($api_stock));

        app(StockUpdate::class)->stockOutUpdate($stock_out_ids);
    }
}
