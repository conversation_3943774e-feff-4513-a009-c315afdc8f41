<?php

namespace App\Services\Marketplace\BigBuyApi;

use App\Enums\V2UserAccess;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;


class StockUpdate
{
    private const API_ID = 4;
    private const SHIPPING_METHOD = 1;
    private const CHUNK_SIZE = 1500;

    public function stockUpdate($api_product_ids, $new_stocks)
    {
        foreach (array_chunk($api_product_ids, self::CHUNK_SIZE) as $ids) {

            $products = Product::with('drmProducts', 'drmProductsV2')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->where('is_varient', 0)
                ->whereIn('api_product_id', $ids)
                ->select('id', 'ean', 'stock', 'old_stock', 'api_product_id', 'stock_updated_at', 'ek_price')
                ->get();

            $salesTrac = [];
            $product_sync_data = [];
            foreach ($products as $product) {
                $n_stock = $new_stocks[$product->api_product_id];

                if ($product->stock !=  $n_stock) {

                    $old_stock = $product->stock;

                    $product->stock             = $n_stock;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if ($old_stock > $n_stock) {

                        $discres_stock = $old_stock - $n_stock;
                        $salesTrac[] = [
                            'marketplace_product_id'    => $product->id,
                            'sales_stock'               => $discres_stock,
                            'sales_amount'              => $discres_stock * $product->ek_price,
                            'created_at'                => \Carbon\Carbon::now(),
                        ];
                    }

                    $data['stock'] = $n_stock;
                    $drm_products = $product->drmProducts;
                    if ($drm_products->isNotEmpty()) {
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                    }

                    $drmProductsV2 = $product->drmProductsV2;
                    if ($drmProductsV2->isNotEmpty()) {
                        foreach ($drmProductsV2 as $v2_product) {
                            if (in_array($v2_product->user_id, V2UserAccess::USERS)) {
                                $product_sync_data[] = [
                                    'marketplace_product_id' => $v2_product->marketplace_product_id,
                                    'user_id'  => $v2_product->user_id,
                                    'country_id' => $v2_product->country_id,
                                    'metadata' => json_encode($data),
                                    'status' => 1,
                                    'created_at' => \Carbon\Carbon::now(),
                                    'updated_at' => \Carbon\Carbon::now(),
                                ];
                            }
                        }
                    }

                    $product->update();

                    info("BigBuy API country product sync-" . $product->ean);
                }
            }

            if (!blank($product_sync_data)) {
                DB::table('mp_product_sync_histories')->insert($product_sync_data);
                $product_sync_data = [];
            }
            if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        }
    }

    public function stockOutUpdate($api_product_ids)
    {
        $existingMarketplaceIds = [];
        foreach (array_chunk($api_product_ids, self::CHUNK_SIZE) as $ids) {

            $products = Product::with('drmProducts', 'drmProductsV2')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->where('is_varient', 0)
                ->whereIn('api_product_id', $ids)
                ->select('id', 'ean', 'stock', 'old_stock', 'api_product_id', 'stock_updated_at', 'ek_price')
                ->get();

            $salesTrac = [];
            $product_sync_data = [];
            foreach ($products as $product) {
                if ($product->stock > 0) {

                    $old_stock = $product->stock;

                    $product->stock             = 0;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if (!array_key_exists($product->id, $existingMarketplaceIds)) {
                        $salesTrac[] = [
                            'marketplace_product_id' => $product->id,
                            'sales_stock'           => $old_stock,
                            'sales_amount'          => $old_stock * $product->ek_price,
                            'created_at'            => \Carbon\Carbon::now(),
                        ];
                        $existingMarketplaceIds[$product->id] = 0;
                    }
                    $data['stock'] = 0;
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        info("Bigbuy stock out drm sync-" . $product->ean);
                    }

                    $drmProductsV2 = $product->drmProductsV2;
                    if ($drmProductsV2->isNotEmpty()) {
                        foreach ($drmProductsV2 as $v2_product) {
                            if (in_array($v2_product->user_id, V2UserAccess::USERS)) {
                                $product_sync_data[] = [
                                    'marketplace_product_id' => $v2_product->marketplace_product_id,
                                    'user_id'  => $v2_product->user_id,
                                    'country_id' => $v2_product->country_id,
                                    'metadata' => json_encode($data),
                                    'status' => 1,
                                    'created_at' => \Carbon\Carbon::now(),
                                    'updated_at' => \Carbon\Carbon::now(),
                                ];
                            }
                        }
                    }

                    $product->update();
                    info("bigbuy stock out sync-" . $product->ean);
                }
            }

            if (!blank($product_sync_data)) {
                DB::table('mp_product_sync_histories')->insert($product_sync_data);
                $product_sync_data = [];
            }
            if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        }
    }
}
