<?php

namespace App\Services\Marketplace\BigBuyApi;

use Illuminate\Support\Facades\DB;
use App\Enums\Marketplace\MpProductCountry;

class PriceSyncProcess
{

    private const API_ID = 4;
    private const SHIPPING_METHOD = 1;

    public function process($apiProductPrices)
    {
        $local_product = collect(DB::table('marketplace_products')
            ->where('api_id', self::API_ID)
            ->where('shipping_method', self::SHIPPING_METHOD)
            ->where('is_varient', 0)
            ->select(DB::raw('CAST(ek_price AS DOUBLE) AS ek_price'), 'api_product_id', 'country_id', 'uvp')
            ->get());

        $this->syncPriceForCountries($local_product, $apiProductPrices);
        $this->syncUVPForCountries($local_product, $apiProductPrices);
    }

    /**
     * Synchronize product price for specified countries.
     *
     * @param \Illuminate\Support\Collection $localProducts
     * @param array $apiStock
     * @return array
     */
    private function syncPriceForCountries($local_products, $apiProductPrices)
    {
        $country_ids = [
            MpProductCountry::GERMANY => 'Germany',
            MpProductCountry::SPAIN => 'Spain',
            MpProductCountry::SWITZERLAND => 'Switzerland',
            MpProductCountry::AUSTRIA => 'Austria'
        ];

        $api_product_prices = collect($apiProductPrices)->pluck('wholesalePrice', 'id')->toArray();

        $merged_api_ids = [];

        foreach ($country_ids as $country_id => $country_name) {
            info("BigBuy $country_name stock sync process..............");

            $local_ids = $local_products->where('country_id', $country_id)
                ->pluck('ek_price', 'api_product_id')
                ->toArray();

            $new_prices = array_diff_assoc($api_product_prices, $local_ids);
            $apiProductIdsToUpdate = array_keys(array_intersect_key($new_prices, $local_ids));

            if (!empty($apiProductIdsToUpdate)) {
                $merged_api_ids = array_merge($merged_api_ids, $apiProductIdsToUpdate);
            } else {
                info("No stock updates required for $country_name.");
            }
        }
        $unique_ids = array_unique($merged_api_ids) ?? [];

        if (!empty($unique_ids)) {
            app(PriceUpdate::class)->pricekUpdate($unique_ids, $api_product_prices);
        } else {
            info("bigbuy price update id not found..................");
        }

        return true;
    }

    /**
     * Synchronize product uvp for specified countries.
     *
     * @param \Illuminate\Support\Collection $localProducts
     * @param array $apiStock
     * @return array
     */
    public function syncUVPForCountries($local_products, $apiProductPrices)
    {
        $country_ids = [
            MpProductCountry::GERMANY => 'Germany',
            MpProductCountry::SPAIN => 'Spain',
            MpProductCountry::SWITZERLAND => 'Switzerland',
            MpProductCountry::AUSTRIA => 'Austria'
        ];

        $productPrices = collect($apiProductPrices)->pluck('retailPrice', 'id')->toArray();

        $api_product_prices = array_map(function ($value) {
            return round(($value + ($value * 0.10)), 2);
        }, $productPrices);

        $merged_api_ids = [];

        foreach ($country_ids as $country_id => $country_name) {
            info("BigBuy $country_name stock sync process..............");

            $local_ids = $local_products->where('country_id', $country_id)
                ->pluck('uvp', 'api_product_id')
                ->toArray();

            $new_prices = array_diff_assoc($api_product_prices, $local_ids);
            $apiProductIdsToUpdate = array_keys(array_intersect_key($new_prices, $local_ids));

            if (!empty($apiProductIdsToUpdate)) {
                $merged_api_ids = array_merge($merged_api_ids, $apiProductIdsToUpdate);
            } else {
                info("No uvp updates required for $country_name.");
            }
        }

        $unique_ids = array_unique($merged_api_ids) ?? [];

        if (!empty($unique_ids)) {
            app(PriceUpdate::class)->uvpUpdate($unique_ids, $api_product_prices);
        } else {
            info("bigbuy uvp update id not found..................");
        }

        return true;
    }
}
