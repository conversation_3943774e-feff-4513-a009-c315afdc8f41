<?php

namespace App\Services\Marketplace\BigBuyApi;

use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\DB;


class PriceUpdate
{
    private const API_ID = 4;
    private const SHIPPING_METHOD = 1;
    private const CHUNK_SIZE = 1500;

    public function pricekUpdate($api_product_ids, $new_prices)
    {
        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();
        $enterpriceOrTrialUserList = app(\App\Services\Marketplace\ProductService::class)->enterpriceOrTrialUserList() ?? [];

        foreach (array_chunk($api_product_ids, self::CHUNK_SIZE) as $ids) {

            $products = Product::with('drmProducts', 'drmProductsV2')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->where('is_varient', 0)
                ->whereIn('api_product_id', $ids)
                ->select('id', 'ean', 'uvp', 'api_id', 'api_product_id', 'ek_price', 'old_ek_price', 'ek_price_updated_at', 'vk_price', 'old_vk_price', 'vk_price_updated_at', 'im_handel', 'category_id', 'item_number', 'shipping_cost', 'update_status', 'real_shipping_cost', 'misc')
                ->get();

            foreach ($products as $product) {
                $new_ek_price = $new_prices[$product->api_product_id];

                if ($product->ek_price !=  $new_ek_price) {

                    app(\App\Services\Marketplace\ProductService::class)->mpNewPriceCalculation($product, $new_ek_price, $calculation, $local_category_im, $product->real_shipping_cost, false, $enterpriceOrTrialUserList);
                    info("Bigbuy API country product sync-" . $product->ean);
                }
            }
        }
    }


    /**
     * uvp update
     */
    public function uvpUpdate($api_product_ids, $new_prices)
    {
        foreach (array_chunk($api_product_ids, self::CHUNK_SIZE) as $ids) {
            $local_mp_products = Product::with('drmProducts')
                ->where('api_id', self::API_ID)
                ->where('shipping_method', self::SHIPPING_METHOD)
                ->where('is_varient', 0)
                ->whereIn('api_product_id', $ids)
                ->select('id', 'api_id', 'api_product_id', 'uvp', 'ean')
                ->get();

            foreach ($local_mp_products as $product) {
                $new_uvp = $new_prices[$product->api_product_id];

                if ($product->uvp !=  $new_uvp) {

                    $product->uvp = $new_uvp;

                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $data['uvp'] = round($new_uvp, 2);
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        info("Bigbuy country drm uvp sync-" . $product->ean);
                    }

                    $product->update();
                    info("Bigbuy country uvp sync-" . $product->ean);
                }
            }
        }
    }
}
