<?php

namespace App\Services\Marketplace\BigBuyApi;

use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;

class InsertProduct
{

    /**
     * BigBuy Product Insert New Method
     * 
     * This method inserts a new product from the BigBuy API into the database.
     * 
     * @param array  $api_products         Array of products from the BigBuy API.
     * @param array  $api_product_stock_array Array of stock information for the products.
     * @param array  $api_image_array      Array of product images.
     * @param array  $api_product_info     Array of product details.
     * @param array  $api_product_costinfo Array of product cost information.
     * @param array  $api_product_category Array of product category information.
     * @param array  $api_product_brands   Array of product brand information.
     * @param array  $local_category_im    Local category IM Handle information.
     * @param array  $categories           Local categories to API mapping categories.
     * @param int    $country_id           ID of the country for localization.
     */

    public function insert(
        $api_products,
        $api_product_stock_array,
        $api_image_array,
        $api_product_info,
        $api_product_costinfo,
        $api_product_category,
        $api_product_brands,
        $local_category_im,
        $categories,
        $country_id,
        $is_variant
    ) {

        $api_id            = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        $deliveryCompanyId = \App\Enums\Marketplace\ApiResources::BIGBUY_DELIVERY_COMPANY_ID;
        $shippingMethod    = \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING;

        $api_brands     = array_unique(array_column($api_product_brands, 'name'));
        $local_brands   = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);
        $product_brands = collect($api_product_brands)->pluck('name', 'id')->toArray();

        if (isset($api_products)) {
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();

            foreach (array_chunk($api_products, 10000) as $api_chunk_products) {

                $exist_product = Product::where('api_id', $api_id)
                    ->whereIn('ean', array_column($api_chunk_products, 'ean13'))
                    ->select('id', 'api_id', 'ean', 'category_id', 'api_category_id', 'status', 'country_id')
                    ->get();

                $excludeEans = $exist_product->where('country_id', $country_id)->pluck('ean')->toArray();
                $api_chunk_products = array_filter($api_chunk_products, function ($product) use ($excludeEans) {
                    return isset($product['ean13']) && !in_array($product['ean13'], $excludeEans);
                });
                $api_chunk_products = array_values($api_chunk_products);

                foreach ($api_chunk_products as $product) {

                    info('Bigbuy product inserting............');
                    if (empty($product['ean13']) || empty($product['id']) || empty($product['sku'])) continue;

                    $shippingAndCarrierInfo = $this->shippingAndCarrierInfo($product['sku'], $api_product_costinfo);
                    $product_shipping_cost  = $shippingAndCarrierInfo['product_shipping_cost'] ?? 0.00;

                    if ($country_id == 83 && $product_shipping_cost <= 0) continue;

                    $old_product = $exist_product->where('ean', $product['ean13'])
                        ->where('country_id', $country_id)
                        ->first();

                    if (isset($old_product)) {
                        continue;
                    }

                    $images_arr = $this->images($product['id'], $api_image_array) ?? [];

                    $productNameAndDiscription = $this->nameAndDiscription($product['id'], $api_product_info);
                    $product_name = $productNameAndDiscription['product_name'] ?? '';

                    $stockAndHandlingInfo = $this->stockAndHandlingInfo($product['id'], $api_product_stock_array);
                    $product_stock        = $stockAndHandlingInfo['product_stock'] ?? 0;
                    $product_handling_day = $stockAndHandlingInfo['product_handling_day'] ?? 2;

                    $product_brand = 0;
                    if (isset($product['manufacturer']) && isset($product_brands[$product['manufacturer']])) {
                        $product_brand = $local_brands[strtoupper($product_brands[$product['manufacturer']])] ?? 0;
                    }

                    if (($product['condition'] ?? '' == 'NEW' || $is_variant == 1) && !empty($product['ean13']) && !empty($product_name) && !empty($images_arr) && $product_stock > 0) {

                        $ean                = $product['ean13'];
                        $api_product_id     = $product['id'];
                        $item_number        = $product['sku'];
                        $ek_price           = $product['wholesalePrice'];
                        $uvp                = $product['retailPrice'] + ($product['retailPrice'] * 0.10);
                        $vat                = $product['taxRate'] ?? 19;
                        $tax_type           = $product['taxId'] ?? 1;

                        $product_length = $product['depth'] ?? 0.0;
                        $product_width  = $product['width'] ?? 0.0;
                        $product_height = $product['height'] ?? 0.0;
                        $item_weight    = $product['weight'] ?? 0.0;

                        $new_shipping_with_percentage = $product_shipping_cost > 0 ? $product_shipping_cost * 1.10 : 5.20;

                        if ($new_shipping_with_percentage > 35) {
                            $vkPrice  = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, 35);
                            $vkPrice += $new_shipping_with_percentage - 35;
                            $new_shipping_with_percentage = 35;
                        } else {
                            $vkPrice            = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, $new_shipping_with_percentage);
                        }

                        $another_country_exist = $exist_product->where('api_id', $api_id)
                            ->where('ean', $product['ean13'])
                            ->first();

                        $product_category   = 68;
                        $product_api_category_id = '';
                        $product_status = 0;
                        if ($another_country_exist && $country_id != 1) {
                            $product_status = $another_country_exist->status;
                            $product_category = $another_country_exist->category_id;
                            $product_api_category_id = $another_country_exist->api_category_id;
                        } else {
                            if (array_key_exists($product['id'], $api_product_category)) {
                                $product_category = !empty($categories[$api_product_category[$product['id']]]) ? $categories[$api_product_category[$product['id']]] : 68;
                                $product_api_category_id = $api_product_category[$product['id']] ?? '';
                            }
                            $status_set_attributes = [
                                'item_number'   => $item_number, 
                                'brand'         => $product_brand, 
                                'ek_price'      => $ek_price, 
                                'uvp'           => $uvp, 
                                'delivery_days' => $product_handling_day, 
                                'vat'           => $vat
                            ];
                            $product_status = app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name, $productNameAndDiscription['product_discription'], $images_arr, $product_stock, $status_set_attributes);
                        }

                        $im_handel = 0;
                        if (isset($local_category_im[$product_category]) && $local_category_im[$product_category] > 0) {
                            $im_handel =  $vkPrice * (1 + $local_category_im[$product_category] / 100) ?? 0;
                        }
                        $attributes = [
                            'api_id'             => $api_id,
                            'api_product_id'     => $api_product_id,
                            'name'               => $product_name ?? '',
                            'brand'              => $product_brand ?? '',
                            'ean'                => $ean ?? '',
                            'ek_price'           => $ek_price ?? '',
                            'vk_price'           => $vkPrice ?? '',
                            'uvp'                => $uvp ?? '',
                            'description'        => $productNameAndDiscription['product_discription'] ?? '',
                            'category_id'        => $product_category ?? '',
                            'api_category_id'    => $product_api_category_id ?? '',
                            'image'              => $images_arr ?? '',
                            'item_color'         => '',
                            'status'             => $product_status,
                            'item_number'        => $item_number ?? '',
                            'shipping_method'    => $shippingMethod,
                            'shipping_cost'      => $new_shipping_with_percentage,
                            'real_shipping_cost' => $product_shipping_cost,
                            'stock'              => $product_stock ?? 0,
                            'item_weight'        => $item_weight,
                            'vat'                => $vat,
                            'is_top_product'     => 1,
                            'delivery_days'      => isset($product_handling_day) ? ($product_handling_day + 1) : 2,
                            'collection_id'      => 0,
                            'internel_stock'     => 0,
                            'delivery_company_id' => $deliveryCompanyId,
                            'misc'               => $shippingAndCarrierInfo['product_carrier_name'] ?? 'GLS',
                            'tax_type'           => $tax_type,
                            'im_handel'          => $im_handel ?? 0,
                            'country_id'         => $country_id,
                            'is_varient'         => $is_variant,
                        ];

                        $product_insert_id = Product::create($attributes);
                        if ($product_insert_id->id) {
                            $volume = (($product_length * $product_width * $product_height) / 1000000);

                            $additionalInfo['product_length'] =  $product_length ?? 0.0;
                            $additionalInfo['product_width']  =  $product_width ?? 0.0;
                            $additionalInfo['product_height'] =  $product_height ?? 0.0;
                            $additionalInfo['item_unit']      =  'Centimeter';
                            $additionalInfo['volume'] = number_format($volume, 6);
                            $additionalInfo['product_id'] =  $product_insert_id->id;
                            $additionalInfo['custom_tariff_number'] = $product['manufacturer'] ?? '';
                            $additionalInfo['manufacturer_id'] = $product['partNumber'] ?? '';

                            DB::table('mp_product_additional_info')->insert($additionalInfo);
                        }
                    }
                }

                info("BigBuy All New Products Update successfully");
            }
        }
    }

    private function images($api_product_id, $api_image_array)
    {
        $images_arr        = [];
        if (array_key_exists($api_product_id, $api_image_array)) {
            foreach ($api_image_array[$api_product_id] as $image) {
                $images_arr[] = $image['url'];
            }
        }

        return $images_arr;
    }

    private function nameAndDiscription($api_product_id, $api_product_info)
    {
        $product_name = '';
        $product_discription = '';
        if (array_key_exists($api_product_id, $api_product_info)) {
            $product_name  = $api_product_info[$api_product_id]['name'];
            $product_discription  = $api_product_info[$api_product_id]['description'];
        }

        return [
            'product_name' => $product_name,
            'product_discription' => $product_discription,
        ];
    }

    private function stockAndHandlingInfo($api_product_id, $api_product_stock_array)
    {
        $product_stock          = 0;
        $product_handling_day   = 2;
        if (array_key_exists($api_product_id, $api_product_stock_array)) {
            $product_stock        = $api_product_stock_array[$api_product_id]['quantity'] ?? 0;
            $product_handling_day = $api_product_stock_array[$api_product_id]['maxHandlingDays'] ?? 2;
        }

        return [
            'product_stock' => $product_stock,
            'product_handling_day' => $product_handling_day,
        ];
    }

    private function shippingAndCarrierInfo($item_number, $api_product_costinfo)
    {
        $product_shipping_cost = 0;
        $product_carrier_name  = 'GLS';

        if (array_key_exists($item_number, $api_product_costinfo)) {
            $product_shipping_cost = $api_product_costinfo[$item_number]['cost'] ?? 0;
            $product_carrier_name = $api_product_costinfo[$item_number]['carrierName'] ?? 'GLS';
        }

        return [
            'product_shipping_cost' => $product_shipping_cost,
            'product_carrier_name' => $product_carrier_name,
        ];
    }
}
