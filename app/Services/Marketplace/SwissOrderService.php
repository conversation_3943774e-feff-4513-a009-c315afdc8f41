<?php

namespace App\Services\Marketplace;

use App\Models\DrmOrder;
use App\Models\SwissOrderDuplication;
use App\Models\SwissPostCredential;
use App\Services\BaseService;
use Illuminate\Support\Facades\Log;

class SwissOrderService extends BaseService
{
    /**
     * Check if customer address is in Switzerland
     */
    public function isSwissAddress($customerInfo)
    {
        return isset($customerInfo['country']) &&
               (strtoupper($customerInfo['country']) === 'CH' ||
                strtoupper($customerInfo['country']) === 'CHE' ||
                strtoupper($customerInfo['country']) === 'SWITZERLAND');
    }

    /**
     * Process Swiss order - create duplicate with MyCargoGate address
     */
    public function processSwissOrder($orderInformations)
    {
        $customerInfo = $orderInformations['customer_info'];
        $orderInfo = $orderInformations['order_info'];

        // Check if it's a Swiss address
        if (!$this->isSwissAddress($customerInfo)) {
            return $orderInformations; // Return original order if not Swiss
        }

        // Check if already duplicated
        if (SwissOrderDuplication::isDuplicated($orderInfo['order_id'])) {
            Log::info("Swiss order {$orderInfo['order_id']} already duplicated");
            return $this->getProcessedSwissOrder($orderInformations);
        }

        Log::info("Processing Swiss order {$orderInfo['order_id']} - creating duplicate with MyCargoGate address");

        // Create duplicate order with MyCargoGate address
        $duplicateOrder = $this->createDuplicateOrder($orderInformations);

        if ($duplicateOrder) {
            // Record the duplication
            $this->recordOrderDuplication($orderInfo['order_id'], $duplicateOrder->id, $customerInfo);

            // Return modified order information with MyCargoGate address for fulfillment
            return $this->getModifiedOrderForFulfillment($orderInformations, $duplicateOrder);
        }

        Log::error("Failed to create duplicate order for Swiss order {$orderInfo['order_id']}");
        return $orderInformations;
    }

    /**
     * Create duplicate order with MyCargoGate German address
     */
    private function createDuplicateOrder($orderInformations)
    {
        try {
            $originalOrder = DrmOrder::find($orderInformations['order_info']['order_id']);

            if (!$originalOrder) {
                Log::error("Original order not found: {$orderInformations['order_info']['order_id']}");
                return null;
            }

            // Create duplicate order data
            $duplicateData = $originalOrder->toArray();
            unset($duplicateData['id']);
            unset($duplicateData['created_at']);
            unset($duplicateData['updated_at']);

            // Modify shipping address to MyCargoGate
            $originalShipping = json_decode($duplicateData['shipping'], true);
            $myCargoGateAddress = SwissOrderDuplication::getMyCargoGateAddress();

            // Compose full address with address_addition
            $fullAddress = $myCargoGateAddress['street'] . ' ' . $myCargoGateAddress['house_no'];
            if (!empty($myCargoGateAddress['address_addition'])) {
                $fullAddress = $myCargoGateAddress['address_addition'] . ', ' . $fullAddress;
            }

            $modifiedShipping = array_merge($originalShipping, $myCargoGateAddress);
            $modifiedShipping['address'] = $fullAddress;
            $duplicateData['shipping'] = json_encode($modifiedShipping);

            // Mark as Swiss duplicate
            $duplicateData['order_notes'] = ($duplicateData['order_notes'] ?? '') . ' [SWISS_DUPLICATE_MYCARGOGATE]';

            // Create the duplicate order
            $duplicateOrder = DrmOrder::create($duplicateData);

            Log::info("Created duplicate order {$duplicateOrder->id} for original Swiss order {$originalOrder->id}");

            return $duplicateOrder;

        } catch (\Exception $e) {
            Log::error("Error creating duplicate order: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Record the order duplication in tracking table
     */
    private function recordOrderDuplication($originalOrderId, $duplicateOrderId, $originalAddress)
    {
        SwissOrderDuplication::create([
            'original_order_id' => $originalOrderId,
            'duplicate_order_id' => $duplicateOrderId,
            'original_address' => $originalAddress,
            'mycargogate_address' => SwissOrderDuplication::getMyCargoGateAddress(),
            'status' => 'created'
        ]);
    }

    /**
     * Get modified order information for fulfillment (with MyCargoGate address)
     */
    private function getModifiedOrderForFulfillment($orderInformations, $duplicateOrder)
    {
        // Keep original products and order info, but modify customer info for fulfillment
        $myCargoGateAddress = SwissOrderDuplication::getMyCargoGateAddress();

        $modifiedCustomerInfo = [
            'first_name' => 'MyCargoGate',
            'last_name' => 'Germany GmbH',
            'email' => $orderInformations['customer_info']['email'], // Keep original email
            'street' => $myCargoGateAddress['street'],
            'house_no' => $myCargoGateAddress['house_no'],
            'zipcode' => $myCargoGateAddress['zip_code'],
            'city' => $myCargoGateAddress['city'],
            'country' => $myCargoGateAddress['country'],
            'address' => $myCargoGateAddress['street'] . ' ' . $myCargoGateAddress['house_no'],
            'company' => $myCargoGateAddress['company'],
            'phone' => $orderInformations['customer_info']['phone'] ?? '49000000000'
        ];

        return [
            'customer_info' => $modifiedCustomerInfo,
            'products' => $orderInformations['products'],
            'order_info' => array_merge($orderInformations['order_info'], [
                'order_id' => $duplicateOrder->id, // Use duplicate order ID for fulfillment
                'is_swiss_duplicate' => true,
                'original_order_id' => $orderInformations['order_info']['order_id']
            ])
        ];
    }

    /**
     * Get already processed Swiss order information
     */
    private function getProcessedSwissOrder($orderInformations)
    {
        $duplication = SwissOrderDuplication::where('original_order_id', $orderInformations['order_info']['order_id'])->first();

        if ($duplication && $duplication->duplicateOrder) {
            return $this->getModifiedOrderForFulfillment($orderInformations, $duplication->duplicateOrder);
        }

        return $orderInformations;
    }

    /**
     * Get Swiss order status for customer display
     */
    public function getSwissOrderStatus($orderId)
    {
        $duplication = SwissOrderDuplication::where('original_order_id', $orderId)->first();

        if ($duplication) {
            return [
                'is_swiss_order' => true,
                'original_address' => $duplication->original_address,
                'mycargogate_address' => $duplication->mycargogate_address,
                'status' => $duplication->status,
                'fulfillment_note' => 'Your order will be shipped to MyCargoGate Germany and then forwarded to your Swiss address.'
            ];
        }

        return ['is_swiss_order' => false];
    }

    /**
     * Get user Swiss Post credentials
     */
    public function getUserSwissPostCredentials($userId)
    {
        $credentials = SwissPostCredential::where('user_id', $userId)
                                            ->where('is_validated', true)
                                            ->first();

        if ($credentials && app()->environment('production')) {
            return [
                'client_id' => $credentials->client_id,
                'client_secret' => $credentials->client_secret
            ];
        }

        // Return default/fallback credentials if no user-specific ones found
        return $this->getDefaultCredentials($userId);
    }

    /**
     * Get default credentials (existing hardcoded logic as fallback)
     */
    private function getDefaultCredentials($userId)
    {
        $isAllowedUser = in_array($userId, [3984, 4175]);

        if (app()->environment('production')) {
            return [
                'client_id' => $isAllowedUser ? 'c9f8b60e81c9a76b6b11b53f4895cdc2' : '249e556e1e296aa358f33e7870d3cb8f',
                'client_secret' => $isAllowedUser ? '8890b73b1d2e833c29d329df9a798646' : 'aed4c8c08422461a8b6ca31aa711c486'
            ];
        }

        // Development credentials
        return [
            'client_id' => 'ade38eaa0d318e7111bd4c0cc0977dd6',
            'client_secret' => '846254ef3bed6d03148f7e7379c66afe'
        ];
    }
}
