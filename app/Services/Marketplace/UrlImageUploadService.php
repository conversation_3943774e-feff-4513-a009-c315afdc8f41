<?php

    namespace App\Services\Marketplace;

    use App\Services\BaseService;
    use App\Models\Marketplace\Product;
    use Illuminate\Support\Facades\Storage;
    use Log;
    use DB;

    class UrlImageUploadService extends BaseService{

        public function upload_url_image(object $products)
        {
            ini_set('max_execution_time', '0'); 
            foreach($products as $product){
                $imagesUrl = [];
                $images = $product->image;
                if(!empty($images)){
                    foreach($images as $image){
                        try{
                            if(parse_url($image, PHP_URL_HOST) == 'drm-file.fra1.digitaloceanspaces.com'){
                                $imagesUrl[] = $image;
                            }else{
                                $path = 'marketplace-products/' . $product->id;
                                $extension = pathinfo($image, PATHINFO_EXTENSION) ?? '';
                                $randomStr = md5(uniqid(rand(), true));
                                $fileName = $path . '/' . $randomStr . "." .$extension;
                                $fileContent = @file_get_contents(str_replace(' ', '%20', $image));
                                if ($fileContent) {
                                    Storage::disk('spaces')->put($fileName, $fileContent, 'public');
                                    $imagesUrl[] = Storage::disk('spaces')->url($fileName) ?? null;
                                }
                            }
                        }catch(\Exception $e){
                            Log::info(["Image cloud upload process error".$product->id]);
                        }
                    }
                    $data['image'] = $imagesUrl;
                    if($product->old_images == null) $data['old_images'] = $product->image;
                    $data['is_image_process'] = true;
                    Product::where('id',$product->id)->update($data);
                    // drm image column update
                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $drmData['image'] = $data['image'];
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$drmData);
                        Log::info("DRM product image sync-" . $product->ean);
                    }
                    Log::info("successful image update");
                }
            }
            Log::info("Product Image upload End");
        }
    }

?>
