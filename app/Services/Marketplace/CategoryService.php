<?php


namespace App\Services\Marketplace;


use App\Models\Marketplace\Category;
use App\Services\BaseService;
use Illuminate\Http\UploadedFile;

class CategoryService extends BaseService
{
    public function all(array $filters = [])
    {
        $query = Category::query();

        if(!empty($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if(!empty($filters['excepts'])) {
            $query->whereNotIn('id', $filters['excepts']);
        }

        if(!empty($filters['search'])) {
            $query->where(\DB::raw('LOWER(name)'), 'like', '%'. strtolower($filters['search']). '%');
        }

        return $query->paginate();
    }

    public function mainCategories () {
        return Category::where('is_active', 1)->whereDoesntHave('parent')->get();
    }

    public function childCategories ()
    {
        return Category::has('parent')->get();
    }

    public function getById($id)
    {
        return Category::find($id);
    }

    public function store(array $data, UploadedFile $image = null)
    {
        return $this->saveCategory($data, $image);
    }

    public function update($id, array $data, UploadedFile $image = null)
    {
        return $this->saveCategory($data, $image, $id);
    }

    public function destroy($id)
    {
        return Category::find($id)->delete();
    }

    private function saveCategory($data, $image, $id = null)
    {
        $category = Category::findOrNew($id);
        $category->fill($data);
        $category->save();

        if($image) {
            $fileName = md5($image->getClientOriginalName() . uniqid()) . "." . $image->getClientOriginalExtension();
            $image->move(public_path('/uploads/categories/'), $fileName);

            $category->photo = $fileName;
            $category->save();
        }

        return $category;
    }
}
