<?php


namespace App\Services\Marketplace;

use League\Csv\Reader;
use App\DeliveryCompany;
use App\Enums\V2UserAccess;
use App\Models\DrmOrder;
use App\Models\DrmProduct;
use Illuminate\Support\Arr;
use App\Services\BaseService;
use Illuminate\Support\Carbon;
use App\Enums\VisibilityStatus;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Cache;
use App\Models\MarketplaceProfitCalculation;
use App\Models\Marketplace\AllApiUpdateSchedule;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class ProductService extends BaseService
{
    public function all(array $filters = [], $paginate = true)
    {

        if ( \CRUDBooster::myPrivilegeName() == 'Customer' && !\CRUDBooster::isDropMatrix() ) {
            $query = Product::query()->whereNotIn('supplier_id', \App\Enums\Marketplace\FakeSuppliers::FAKE_IDS)->select('marketplace_products.*');
        } else {
            $query = Product::query()->select('marketplace_products.*');
        }

        if(!empty($filters['supplier_id'])) {
            $query->where('supplier_id', $filters['supplier_id']);
        }

        if(!empty($filters['status'])) {
            $status = $filters['status'];
            if(!is_array($status)){
                $status = [$status];
            }
            $query->whereIn('marketplace_products.status', $status);
        }

        if(!empty($filters['category_ids'])) {
            if(!is_array($filters['category_ids'])) {
                $filters['category_ids'] = [$filters['category_ids']];
            }
            $query->whereIn('category_id', $filters['category_ids']);
        }

        if(!empty($filters['collection_id'])) {
            $query->where('collection_id', $filters['collection_id']);
        }

        if(!empty($filters['excepts'])) {
            $query->whereNotIn('marketplace_products.id', $filters['excepts']);
        }

        if(!empty($filters['q'])) {
            $query->where(\DB::raw('LOWER(marketplace_products.name)'), 'like', '%'. strtolower($filters['q']). '%');
        }

        return $paginate ? $query->paginate() : $query->get();
    }

    public function getById($id)
    {
        return Product::find($id);
    }

    public function store(array $data)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);

        return $this->saveProduct($data);
    }

    public function update($id, array $data)
    {
        return $this->saveProduct($data, $id);
    }

    public function destroy($id)
    {
        return Product::find($id)->delete();
    }

    private function saveProduct($data, $id = null)
    {
        $product = Product::findOrNew($id);
        $product->fill($data);
        $product->save();

        return $product;
    }

    public function saveCollectionProducts($data)
    {
        foreach ($data['product_ids'] as $productId) {
            $product = Product::find($productId)->replicate();
            $product->collection_id = $data['collection_id'];
            $product->parent_id = $productId;
            $product->price = data_get($data, "product_prices.{$productId}");
            if ($product->name != data_get($data, "product_titles.{$productId}")) {
                $product->misc = ['title_manual_overwrite' => true];
            }
            $product->name = data_get($data, "product_titles.{$productId}");
            $product->status = VisibilityStatus::ACTIVE;
            $product->save();

            foreach ($data['product_listing_price'] as $listingPrice) {
                $product->listing_prices()->create(
                    array_merge($listingPrice, ['listable_id' => $product->id, 'listable_type' => get_class($product)])
                );
            }
        }
    }

    public function newProducts ($q = null)
    {

    }

    public function fetchTopProducts ($q = null)
    {
        $query = Product::query();
        if ( \CRUDBooster::myPrivilegeName() == 'Customer' && !\CRUDBooster::isDropMatrix() ) {
            $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myId())->first();
            $accessableCategories = array_map('intval',$accessRow->accessable_categories??[]);
            $query->whereNotIn('supplier_id', \App\Enums\Marketplace\FakeSuppliers::FAKE_IDS);
            if ( !empty($accessRow) && !empty($accessableCategories) ) {
                if ( !empty(array_map('intval',$accessRow->accessable_categories??[])) ) {
                    $query->whereIn('category_id', $accessableCategories);
                }
            }
        }

        $query->where('is_top_product', 1);
        $query->where('status', \App\Enums\Marketplace\Status::ACTIVE);
        $query->orderBy('id', 'desc');

        return $query->get();
    }

    public function countProducts ($q = null)
    {


        $query = Product::query();


        if ( $q['category'] != null ) $query->where('category_id', $q['category']);

        if ( $q['status'] != null ) $query->where('status', $q['status']);

        if ( $q['supplier'] != null )  $query->where('supplier_id', $q['supplier']);

        if ( \CRUDBooster::myPrivilegeName() == 'Customer' && !\CRUDBooster::isDropMatrix() ) {
            $query->whereNotIn('supplier_id', \App\Enums\Marketplace\FakeSuppliers::FAKE_IDS);
        }
        return $query->count();
    }

    public function getDashboardProductTotal ($id = null)
    {
        if ( $id ) {
            return \App\Models\Marketplace\Product::where('supplier_id', $id)->count();
        } else {
            if ( CRUDBooster::isDropMatrix() || CRUDBooster::isSuperAdmin() ) {
                return \App\Models\Marketplace\Product::where('shipping_method', \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING)->count();
            } elseif ( CRUDBooster::isSupplier() ) {
                return \App\Models\Marketplace\Product::where('supplier_id', CRUDBooster::myId())->count();
            }
        }

    }

    public function validateEAN($ean) {
        $ean = trim($ean);
        if ( strlen($ean) > 8 && strlen($ean) < 14  ) {
            return true;
        } else {
            return false;
        }
    }

    public function getDeliverCompanyCategory( $supplier_id )
    {
        $str = null;
        $dropShipping   = \App\Models\Marketplace\Product::where('shipping_method', \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING)
                        ->where('supplier_id', $supplier_id)
                        ->first();
        $fullfilment    = \App\Models\Marketplace\Product::where('shipping_method', \App\Enums\Marketplace\ShippingMethod::FULFILLment)
                        ->where('supplier_id', $supplier_id)
                        ->first();

        if ( $dropShipping && $fullfilment ) {
            $str = 'DropShipping & Fullfilment';
        } elseif ( $dropShipping ) {
            $str = 'DropShipping';
        } elseif ( $fullfilment ) {
            $str = 'Fullfilment';
        } else {
            $str = '-';
        }
        return $str;

    }

    public function getDeliveryCompanies()
    {
        // $superAdminId = 71;
        $dropmatrixId = \App\Enums\Apps::DROPMATIX_ID;
        return DB::table('delivery_companies')->select('id', 'name', 'supplier_id', 'email', 'user_id')
                                ->WhereIn('user_id', [$dropmatrixId])
                                ->orderBy('name', 'desc')
                                ->get();
    }

    public function getDeliveryCompaniesForFilterInProducts ()
    {
        $dropmatrixId = \App\Enums\Apps::DROPMATIX_ID;
        return DB::table('delivery_companies')->select('id', 'name', 'supplier_id', 'email', 'user_id')
                                ->WhereIn('user_id', [$dropmatrixId])
                                ->orWhere('is_marketplace_supplier', 1)
                                ->orWhere('supplier_id', '>', 0)
                                ->orderBy('name', 'asc')
                                ->get();
    }

    public function buildQueryBySearch($query, $searchBy, $queryValue) {

        $searchBy       = $_REQUEST['search_by_select'];
        $delimeter      = '=';
        $queryValue     = trim($_REQUEST['search_by_query']);

        if ( $searchBy == 'name' ) {
            $delimeter  = 'LIKE';
            $queryValue = "%".$queryValue."%";
        }
        return $query->where($searchBy, $delimeter, $queryValue);
    }

    public function buildQueryByFilterColumnSorting ($query, $filter_column) {

        foreach ($filter_column as $key => $fc) {
            $value = @$fc['value'];
            $type = @$fc['type'];
            $sorting = @$fc['sorting'];

            if ($sorting != '') {
                if ($key) {
                    $query->orderby($key, $sorting);
                }
            }
            if ($type == 'between') {
                if ($key && $value) {
                    $query->whereBetween($key, $value);
                }
            } else {
                continue;
            }
        }

        return $query;
    }

    public function syncDrmProduct($drm_products, $datas, $enterpriceOrTrialUserList = [])
    {

        // $product_sync_data = [];
        foreach ($drm_products as $drmProduct) {
            $drm_stock = $drmProduct->stock;
            $updateableColumns = [];
            $update_status = json_decode($drmProduct->update_status, 1);
            foreach ($update_status as $k => $v) {
                if ($update_status[$k] == 1) {

                    if ($k == 'description') {
                        if (isset($datas['description'])) {
                            $updateableColumns[$k] = [
                                'de' => $datas['description'] ?? null,
                            ];
                        }
                    }

                    // if ( $k == 'ek_price' ) {
                    //     if(!empty($datas['vk_price']) && $datas['vk_price'] > 0){
                    //         $mp_vk_price = $datas['vk_price'];

                    //         if (!blank($drmProduct->mp_price_markup)) {
                    //             $mp_vk_price += ($drmProduct->mp_price_markup * $mp_vk_price) / 100;
                    //         }

                    //         if (!blank($drmProduct->mp_category_offer)) {
                    //             $mp_vk_price -= ($drmProduct->mp_category_offer * $mp_vk_price) / 100;
                    //         }

                    //         $mp_new_vk_price = round($mp_vk_price, 2);

                    //         if ($drmProduct->ek_price != $mp_new_vk_price) {
                    //             $updateableColumns[$k] = $mp_new_vk_price;
                    //         }
                    //     } 
                    // }

                    // if ($k == 'stock') {
                    //     if (isset($datas['stock'])) {

                    //         if ($drmProduct->stock != $datas['stock']) {
                    //             $updateableColumns[$k] = $datas['stock'];
                    //             $updateableColumns['old_stock'] = $drmProduct->stock ? $drmProduct->stock : 0;
                    //             $updateableColumns['stock_updated_at'] = \Carbon\Carbon::now();
                    //         }
                    //     }
                    // }
                    // if ( $k == 'uvp' ) {
                    //     if(!empty($datas['uvp']) && $drmProduct->uvp != $datas['uvp']) $updateableColumns[$k] = $datas['uvp'];
                    // }

                    if ($k == 'delivery_days') {
                        if (!empty($datas['delivery_days']) && $drmProduct->delivery_days != $datas['delivery_days']) $updateableColumns[$k] = $datas['delivery_days'];
                    }

                    if ( $k == 'image' && !empty($datas['image'])) {
                        $updateableColumns['image'] = $datas['image'];
                    }
                }
            }


            if (isset($datas['stock'])) {

                if ($drmProduct->stock != $datas['stock']) {
                    $updateableColumns['stock'] = $datas['stock'];
                    $updateableColumns['old_stock'] = $drmProduct->stock ? $drmProduct->stock : 0;
                    $updateableColumns['stock_updated_at'] = \Carbon\Carbon::now();
                }
            }

            if (!empty($datas['vk_price']) && $datas['vk_price'] > 0) {
                $mp_vk_price = $datas['vk_price'];

                $mp_price_markup_discount = !blank($drmProduct->mp_price_markup) ? ($drmProduct->mp_price_markup * $mp_vk_price) / 100 : 0;
                $cat_offer_discount = !blank($drmProduct->mp_category_offer) ? ($drmProduct->mp_category_offer * $mp_vk_price) / 100 : 0;

                $mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);

                if($drmProduct->user_id == 2872){
                    $mp_new_vk_price = round(($mp_new_vk_price - ($mp_new_vk_price * 0.08)),2);
                }

                if($drmProduct->user_id == 3938 || in_array($drmProduct->user_id, [4293, 3602]) && $drmProduct->marketplace_delivery_company_id == 15021){
                    $mp_new_vk_price = round(($mp_new_vk_price - ($mp_new_vk_price * 0.05)),2);
                }

                if ($drmProduct->user_id == 4296) {
                    $mp_new_vk_price = round(($mp_new_vk_price - ($mp_new_vk_price * 0.15)),2);
                }

                if ($drmProduct->ek_price != $mp_new_vk_price && $drmProduct->user_id != 3420) {
                    $updateableColumns['ek_price'] = $mp_new_vk_price;
                }
            }

            if (!empty($datas['ch_vk_price']) && $datas['ch_vk_price'] > 0) {
                $mp_vk_price = $datas['ch_vk_price'];

                $mp_price_markup_discount = !blank($drmProduct->mp_price_markup) ? ($drmProduct->mp_price_markup * $mp_vk_price) / 100 : 0;
                $cat_offer_discount = !blank($drmProduct->mp_category_offer) ? ($drmProduct->mp_category_offer * $mp_vk_price) / 100 : 0;
                $mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);

                if ($drmProduct->ek_price != $mp_new_vk_price) {
                    $updateableColumns['ek_price'] = $mp_new_vk_price;
                }
            }

            if (isset($datas['shipping_cost']) && $datas['shipping_cost'] > 0) {
                
                $drm_shippinng_cost = $datas['shipping_cost'];
                if(in_array($drmProduct->user_id, $enterpriceOrTrialUserList) &&  isset($datas['real_shipping_cost']) && $datas['real_shipping_cost'] == 0){
                    $drm_shippinng_cost = 0.00;
                }
                if ($drmProduct->shipping_cost != $drm_shippinng_cost && $drmProduct->user_id != 3420) {
                    $updateableColumns['shipping_cost'] = round($drm_shippinng_cost, 2);
                }
            }

            if (isset($datas['ch_shipping_cost']) && $datas['ch_shipping_cost'] > 0) {

                if ($drmProduct->shipping_cost != $datas['ch_shipping_cost']) {
                    $updateableColumns['shipping_cost'] = $datas['ch_shipping_cost'];
                }
            }

            if (isset($datas['uvp']) && $datas['uvp'] > 0) {
                $new_uvp = $datas['uvp'];
                $mp_uvp_markup_discount = !blank($drmProduct->mp_price_markup) ? (($drmProduct->mp_price_markup * $new_uvp) / 100) : 0;
                $mp_new_uvp_price       = round($new_uvp + $mp_uvp_markup_discount, 2);
                if ($drmProduct->uvp != $mp_new_uvp_price) {
                    $updateableColumns['uvp'] = $mp_new_uvp_price;
                }
            }

            if (isset($datas['item_unit'])) {
                $updateableColumns['item_unit'] = $datas['item_unit'];
            }
            if (isset($datas['item_weight'])) {
                $updateableColumns['item_weight'] = $datas['item_weight'];
            }
            if (isset($datas['im_handel']) && $datas['im_handel'] != $drmProduct->im_handel) {
                $new_im_handel = $datas['im_handel'];
                $mp_im_handel_markup_discount = !blank($drmProduct->mp_price_markup) ? (($drmProduct->mp_price_markup * $new_im_handel) / 100) : 0;
                $mp_new_im_handel       = round($new_im_handel + $mp_im_handel_markup_discount, 2);
                if ($drmProduct->im_handel != $mp_new_im_handel) {
                    $updateableColumns['im_handel'] = $mp_new_im_handel;
                }
            }
            if (isset($datas['custom_tariff_number']) && $datas['custom_tariff_number'] != $drmProduct->custom_tariff_number) {
                
                $updateableColumns['custom_tariff_number'] = $datas['custom_tariff_number'];
                
            }

	        if (isset($datas['brand']) && $datas['brand'] != $drmProduct->brand) {
                
                $updateableColumns['brand'] = $datas['brand'];
                
            }

            if (count($updateableColumns) > 0) {
                $drmProduct->update($updateableColumns);

                if (!isset($datas['collection']) && isset($datas['stock']) &&  $datas['stock'] < 2) {
                    if ($drm_stock != $datas['stock']) {
                        $updateableColumns['stock'] = 0;
                    }
                }

                $data['product_id'] = $drmProduct->id;
                $data['user_id'] = $drmProduct->user_id;
                $data['metadata'] = $updateableColumns;
                
                $this->buildRequest(json_encode($data));

                // if(in_array($drmProduct->user_id, V2UserAccess::USERS)){
                //     $product_sync_data[] = [
                //         'marketplace_product_id' => $drmProduct->marketplace_product_id,
                //         'user_id'  => $drmProduct->user_id,
                //         'country_id' => 1,
                //         'metadata' => json_encode($updateableColumns),
                //         'created_at' => \Carbon\Carbon::now(),
                //         'updated_at' => \Carbon\Carbon::now(),
                //     ];
                // }
                
            }
        }

        // if(!blank($product_sync_data)) {
        //     DB::table('mp_product_sync_histories')->insert($product_sync_data);
        // }
    }

    public function syncDrmProductV2($drm_products, $datas, $enterpriceOrTrialUserList = []){

        $product_sync_data = [];
        foreach ($drm_products as $drmProduct) {

            $updateableColumns = [];

            if (isset($datas['stock'])) {
                $updateableColumns['stock'] = $datas['stock'];
            }

            if (!empty($datas['vk_price']) && $datas['vk_price'] > 0) {
                $updateableColumns['ek_price'] = round($datas['vk_price'],2);
            }

            if (isset($datas['shipping_cost']) && $datas['shipping_cost'] > 0) {
                
                $drm_shippinng_cost = $datas['shipping_cost'];
                if(in_array($drmProduct->user_id, $enterpriceOrTrialUserList) &&  isset($datas['real_shipping_cost']) && $datas['real_shipping_cost'] == 0){
                    $drm_shippinng_cost = 0.00;
                }
                
                $updateableColumns['shipping_cost'] = round($drm_shippinng_cost, 2);
            }

            if (isset($datas['uvp']) && $datas['uvp'] > 0) {
                $updateableColumns['uvp'] = round($datas['uvp'],2);
            }

            if (isset($datas['im_handel'])) {
                $updateableColumns['im_handel'] = round($datas['im_handel'],2);
            }

            if (count($updateableColumns) > 0) {

                if(in_array($drmProduct->user_id, V2UserAccess::USERS)){
                    $product_sync_data[] = [
                        'marketplace_product_id' => $drmProduct->marketplace_product_id,
                        'user_id'  => $drmProduct->user_id,
                        'country_id' => $drmProduct->country_id,
                        'status' => 1,
                        'metadata' => json_encode($updateableColumns),
                        'created_at' => \Carbon\Carbon::now(),
                        'updated_at' => \Carbon\Carbon::now(),
                    ];
                }
            }
        }

        if(!blank($product_sync_data)) {
            DB::table('mp_product_sync_histories')->insert($product_sync_data);
        }

    }

    public function buildRequest ($data=[]) {
        if(!isLocal()){
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://***********/product/sync');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                array(
                    'Content-Type:application/json'
                )
            );
            $ce = curl_exec($ch);

            return $ce;

            curl_close($ch);
        }
    }

    public function updateAssignCalc($calculation_id,$ek_price,$uvp,$shipping_cost){
        try {

            $calculation = MarketplaceProfitCalculation::find($calculation_id);
            return $this->calculatePrice($ek_price, $calculation, $uvp, $shipping_cost);
        } catch (\Throwable $th) {
            return false;
        }

    }

    public function calculatePrice($price, $calculation, $uvp = 0, $shipping_cost = 0)
    {
        if ( $calculation->dynamic_shipping_cost && $shipping_cost > 0 ) {
            $calculation_shipping_cost = $shipping_cost;
        } else {
            $calculation_shipping_cost = $calculation->shipping_cost;
        }
        try {
            if ($calculation->uvp) {
                $price = $uvp;
            } else {
                $price = $price + $price
                    * ($calculation->profit_percent / 100)
                    + $calculation_shipping_cost
                    + $calculation->additional_charge;

                if ($calculation->round_scale != null) {
                    $prices = explode('.', $price);
                    if ($prices[1] != 0) {
                        $price = $prices[0] + $calculation->round_scale;
                    }
                }
            }
            $calculation_price = (float)str_replace(',', '', number_format($price, 2));
            return $calculation_price;

        } catch (\Throwable $th) {
            return $price;
        }
    }

    public function sendOrderTrackingToDRM($data){
        if(!isLocal()){
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://drm.software/api/order-tracking-status-sync');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                array(
                    'Content-Type:application/json'
                )
            );
            $ce = curl_exec($ch);

            return $ce;

            curl_close($ch);
        }
    }


    public function updateApiSyncSchedule(int $api_id,$update_time){

        if(isset($api_id) && isset($update_time)){
            AllApiUpdateSchedule::where('api_id',$api_id)->update([
                'next_update_time' => $update_time
            ]);
        }

    }

    public function updateDrmOrder($drm_order_id,$mp_api_id = 0){
        if($drm_order_id){
            DB::connection('drm_core')->table('new_orders')
                ->where('id',$drm_order_id)
                ->update(['mp_api_id'=> $mp_api_id ?? 0]);
        }
    }

    public function buildCurlRequest($apiAddress, $method='GET', $data=[]){
        if(!isLocal()){
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiAddress);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);
            curl_setopt($ch, CURLOPT_POST, $method=="POST"?1:0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            if ( !empty($data) && $method=="POST" ) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                array(
                    'Content-Type:application/json'
                )
            );
            $response = curl_exec($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close ($ch);

            $response = json_decode($response, 1);
            $response['status_code'] = $status_code;
            return $response;
        }
    }

    public function generateStringToArray($path,$delimiter = null){
        $reader = Reader::createFromString($path);
        $reader->setDelimiter($delimiter);
        $reader->setHeaderOffset(0);
        $reader->addStreamFilter('convert.iconv.ISO-8859-15/UTF-8');
        return $reader->getRecords();
    }

    public function brandInsertAndUpdate(array $api_brands):array
    {
        if(!blank($api_brands)){
            $local_brands = DB::table('marketplace_product_brand')
                            ->pluck('brand_name')
                            ->toArray();

            $api_brand_arr   = array_map('strtoupper', $api_brands);
            $local_brand_arr = array_map('strtoupper', $local_brands);

            $brands = array_diff($api_brand_arr,$local_brand_arr);
            
            if(count($brands) > 0){
                $new_brand = [];
                foreach ($brands as $key => $brand){
                    if(!blank($brand)){
                        $new_brand[] = [
                            'brand_name' => $brand,
                            'user_id'    => 2455,
                            'brand_logo' => null,
                        ];
                    }
                }
                DB::table('marketplace_product_brand')->insert($new_brand);
            }
        }

        $local_all_brands =  DB::table('marketplace_product_brand')->pluck('id','brand_name')->toArray();
        return array_change_key_case($local_all_brands,CASE_UPPER);
    }
    
    public function fulfilProductStockDecreaseByIdAndStock(int $prduct_id,int $stock){
        
        if(isset($prduct_id) && isset($stock)){
            $stock = (int)$stock;

            $product = Product::with('drmProducts', 'additionalInfo:product_id,product_length,product_width,product_height')
                        ->where('id',$prduct_id)
                        ->where('shipping_method',2)
                        ->select('id','ean','internel_stock','old_internel_stock','internel_stock_updated_at','ek_price')
                        ->first();

            if($product){
                
                $old_stock = $product->internel_stock;

                if(isset($old_stock) && $old_stock > 0){
                    $new_stock = max(((int)$old_stock - $stock),0);

                    $product->internel_stock             = $new_stock;
                    $product->old_internel_stock         = $old_stock;
                    $product->internel_stock_updated_at  = \Carbon\Carbon::now();
                    
                    $salesTrac = [
                        'marketplace_product_id'    => $product->id,
                        'sales_stock'               => $stock,
                        'sales_amount'              => $stock * $product->ek_price,
                        'created_at'                => \Carbon\Carbon::now(),
                    ];
                    

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['stock'] = $new_stock;
                        $this->syncDrmProduct($drm_products,$data);
                        info("DRM product stock update-".$product->ean);
                    }
                    if($product->additionalInfo){
                        $volume = (($product->additionalInfo->product_length ?? 0) * ($product->additionalInfo->product_width ?? 0) * ($product->additionalInfo->product_height ?? 0))/1000000;
                        $newCubicMeter = $volume * $new_stock;
                        $product->cubic_meters = $newCubicMeter;
                    }
                    $product->update();
                    DB::table('marketplace_product_sales_information')->insert($salesTrac);
                    info("MP product stock update-".$product->ean);
                }
            }else{
                info('product not found');
            }
        }else{
            info('product id or stock not found');
        }
            
    }

    public function checkRequiredField(?string $product_name = null, ?string $product_description = null, ?array $images = null, int $product_stock = 0, $status_set_attributes = []): int 
    {
        if (blank($product_name) || blank($product_description) || blank($images) || $product_stock == 0 || 
        (!blank($status_set_attributes) && (
            blank(Arr::get($status_set_attributes, 'item_number')) || 
            blank(Arr::get($status_set_attributes, 'brand')) || 
            blank(Arr::get($status_set_attributes, 'ek_price')) || 
            blank(Arr::get($status_set_attributes, 'uvp')) || 
            blank(Arr::get($status_set_attributes, 'vat')) || 
            blank(Arr::get($status_set_attributes, 'delivery_days')) 
        ))){
            return \App\Enums\Marketplace\ProductStatus::QUALITY_DEFECT;
        }
        
        return \App\Enums\Marketplace\ProductStatus::PENDING;
    }

    public function vkPriceCalculation($price,$calculation = null,$uvp,$shipping_cost){
        if(!blank($calculation)){

            if ($calculation->dynamic_shipping_cost && $shipping_cost > 0) {
                $calculation_shipping_cost = $shipping_cost;
            } else {
                $calculation_shipping_cost = $calculation->shipping_cost;
            }
            
            if ($calculation->uvp) {
                $price = $uvp;
            } else {
                $price = $price + $price
                    * ($calculation->profit_percent / 100)
                    + $calculation_shipping_cost
                    + $calculation->additional_charge;
    
                if ($calculation->round_scale != null) {
                    $prices = explode('.', $price);
                    if ($prices[1] != 0) {
                        $price = $prices[0] + $calculation->round_scale;
                    }
                }
            }

            return (float)str_replace(',', '', number_format($price, 2));

        }else{
            return round(($price + ($price *0.06)),2);
        }
    }

    /**
     * @param $product
     * @param $ek_price
     * @param $calculation
     * @param $local_category_im
     * @param $shipping_cost
     * @param bool $isDefaultShipping
     */
    public function mpNewPriceCalculation($product,$ek_price,$calculation,$local_category_im,$shipping_cost,$isDefaultShipping = false, $enterpriceOrTrialUserList = []){
        
        $data = [];
        if($isDefaultShipping){
            $new_vk_price = $this->vkPriceCalculation($ek_price, $calculation, $product->uvp, $product->shipping_cost);
        }else{
            if($product->real_shipping_cost != $shipping_cost){
                $product->real_shipping_cost = $shipping_cost;
            }
    
            $calculat_shipping_cost = $shipping_cost > 0 ? ($shipping_cost * 1.10) : 5.20;
    
            if($product->api_id == 4){
                if ("DACHSER" == strtoupper($product->misc) || "PALLET DELIVERY" == strtoupper($product->misc)) {
                        $calculat_shipping_cost =  $calculat_shipping_cost * 1.25;
                    }
            }
    
            if($calculat_shipping_cost > 35){
                $new_vk_price = $this->vkPriceCalculation($ek_price, $calculation, $product->uvp, 35);
                $new_vk_price += $calculat_shipping_cost - 35;
                if($product->shipping_cost != 35){
                    $product->shipping_cost = 35;
                    $data['shipping_cost'] = 35;
                    $data['real_shipping_cost'] = $shipping_cost;
                }
            }else{
                $new_vk_price = $this->vkPriceCalculation($ek_price, $calculation, $product->uvp, $calculat_shipping_cost);
                if($product->shipping_cost != number_format($calculat_shipping_cost, 2)){
                    $product->shipping_cost = $calculat_shipping_cost;
                    $data['shipping_cost'] = $calculat_shipping_cost;
                    $data['real_shipping_cost'] = $shipping_cost;
                }
            }
        }

        if($product->ek_price != number_format($ek_price, 2)){
            $product->old_ek_price        = $product->ek_price;
            $product->ek_price_updated_at = \Carbon\Carbon::now();
            $product->ek_price            = $ek_price;
        }

        if($product->vk_price != number_format($new_vk_price, 2)){
            $product->old_vk_price        = $product->vk_price;
            $product->vk_price_updated_at = \Carbon\Carbon::now();
            $product->vk_price            = $new_vk_price;
            $data['vk_price']             = round($new_vk_price, 2);
        }

        $new_im_handel = 0;
        if(isset($local_category_im[$product->category_id]) && $local_category_im[$product->category_id] > 0){
            $new_im_handel =  $new_vk_price + (($new_vk_price * $local_category_im[$product->category_id]) / 100);
        }

        if($product->im_handel != number_format($new_im_handel, 2)){
            $product->im_handel = $new_im_handel;
            $data['im_handel']   = $new_im_handel;
        }

        if(count($data)){
            $drm_products           = $product->drmProducts;
            if (count($drm_products) > 0) {
                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data, $enterpriceOrTrialUserList);
                info("Drm product price sync -" . $product->ean);
            }
    
            $drmProductsV2 = $product->drmProductsV2 ?? [];
            if (count($drmProductsV2) > 0) {
                $this->syncDrmProductV2($drmProductsV2, $data, $enterpriceOrTrialUserList);
                info("Drm v2 price sync -" . $product->ean);
            }

        }
        
        $product->update();
        info("MP product price sync-" . $product->ean);
        return true;
    }

    public function getCategoryIdWithIMHandel(){
        return Cache::remember('marketplace_categories_im_handel', 60 * 24, function() {
            return DB::table('marketplace_categories')->pluck('im_handel', 'id')->toArray();
        });
    }
    
    public function enterpriceOrTrialUserList()
    {
        $today = Carbon::now();
        $checkUserTariff = DB::Connection('drm_core')->table('purchase_import_plans')
            ->where('import_plan_id', 27)
            ->whereDate('end_date', '>=', $today)
            ->pluck('cms_user_id')
            ->toArray() ?? [];
            
        $is_trial_user = DB::Connection('drm_core')->table('app_trials')
            ->where('app_id', 0)
            ->whereRaw("DATE_ADD(start_date, INTERVAL trial_days DAY) >= '$today'")
            ->pluck('user_id')
            ->toArray() ?? [];

        return array_merge($checkUserTariff, $is_trial_user) ?? [];
    }

}
