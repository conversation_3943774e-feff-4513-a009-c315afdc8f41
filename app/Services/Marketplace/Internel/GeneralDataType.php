<?php


namespace App\Services\Marketplace\Internel;


use App\Enums\Marketplace\Credentials;
use App\Models\Marketplace\ApiCredential;
use App\Services\Marketplace\InternelAPISoapClient;

class GeneralDataType
{
    public $authLogin;
    public $authPassword;
    public $version;
    public $xmlData;

    public function __construct()
    {
        $apiCredentials = ApiCredential::take(1)->first();
        $this->addCredentials([
            'authLogin'     => $apiCredentials->api_user_name,
            'authPassword'  => $apiCredentials->api_password,
            'version'       => Credentials::API_VERSION,
        ]);
    }

    public function addCredentials (array $data)
    {
        $this->authLogin    = $data['authLogin'];
        $this->authPassword = $data['authPassword'];
        $this->version      = $data['version'];
    }
}
