<?php

namespace App;

// use App\Services\Marketplace\InternelSyncService;
use App\Models\Marketplace\Product;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Cashier\Billable;
use Carbon\Carbon;

class User extends Authenticatable
{
    use Notifiable, Billable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $connection = \App\Enums\Marketplace\DBConnections::DRM_CORE;
    protected $table = 'cms_users';

    protected $fillable = [
        'name', 'email', 'password', 'provider', 'provider_id', 'id_cms_privileges', 'status',
        'term_accept', 'referrer_id', 'ref_id', 'two_factor_code', 'two_factor_expires_at',
        'checked_paywall_term', 'paywall_blacklist', 'marketplace_vendor_id', 'marketplace_api_response', 'mp_terms_conditions_confirmation_time'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    public function drm_imports()
    {
        return $this->hasMany('App\DrmImport');
    }

    public function delivery_companies()
    {
        return $this->hasMany('App\DeliveryCompany');
    }

    public function drm_products()
    {
        return $this->hasManyThrough('App\DrmProduct', 'App\DrmImport');
    }

    public function products()
    {
        return $this->hasMany('App\DrmProduct');
    }

    public function tmp_drm_products()
    {
        return $this->hasManyThrough('App\TmpDrmProduct', 'App\DrmImport');
    }

    public function product_templates()
    {
        return $this->hasManyThrough('App\Models\Product\ProductTemplate', 'App\DrmImport');
    }

    public function user_csv_headers()
    {
        return $this->hasOne('App\UserCsvHeaderValue');
    }

    public function billing_detail()
    {
        return $this->hasOne(BillingDetail::class, 'user_id', 'id');
    }

    public function customer_profile()
    {
        return $this->hasMany(NewCustomer::class, 'cc_user_id', 'id');
    }

    public function cards()
    {
        return $this->hasMany(Card::class, 'user_id', 'id');
    }

    public function banks()
    {
        return $this->hasOne(Bank::class, 'user_id', 'id');
    }

    public function shipcloud()
    {
        return $this->hasOne(ShipcloudToken::class, 'user_id', 'id');
    }

    public function getIsFillBillingAttribute()
    {
        return ($this->billing_detail && $this->billing_detail->is_fill) ? true : false;
    }


    //Old orders
    public function orders()
    {
        return $this->hasMany(DrmOrder::class, 'cms_user_id', 'id');
    }

    public function new_orders()
    {
        return $this->hasMany(NewOrder::class, 'cms_user_id', 'id');
    }

    public function monthly_paywalls()
    {
        return $this->hasMany(MonthlyPaywall::class, 'user_id', 'id');
    }


    /* ==============================
    ==========Paywall invoice========
    ================================*/
    //total_order
    public function getTotalOrderAttribute()
    {
        return $this->new_orders->count();
    }

    //total_order_amount
    public function getTotalOrderAmountAttribute()
    {
        return $this->new_orders->where('test_order', '!=', 1)->sum('eur_total');
    }

    //total_charged_due_order
    public function getTotalChargedDueOrderAttribute()
    {
        return $this->new_orders->where('char_status', '!=', 1)->count();
    }

    //due_orders
    public function getDueOrdersAttribute()
    {
        return $this->new_orders->where('char_status', '!=', 1);
    }

    //total_charged_due_amount
    public function getTotalChargedDueAmountAttribute()
    {
        return $this->new_orders->where('char_status', '!=', 1)->where('test_order', '!=', 1)->sum('eur_total');
    }

    //total_charged_paid_amount
    public function getTotalChargedPaidAmountAttribute()
    {
        return $this->new_orders->where('char_status', 1)->where('test_order', '!=', 1)->sum('eur_total'); //Will be romeve soon
    }

    //total_charged_paid_order
    public function getTotalChargedPaidOrderAttribute()
    {
        return $this->new_orders->where('char_status', 1)->count(); // will be remove soon
    }

    //total_payable_amount
    public function getTotalPayableAmountAttribute()
    {
        return number_format(($this->total_payable), 2, ',', '.');
    }

    //total_payable
    public function getTotalPayableAttribute()
    {
        return (($this->paywall_charge * $this->total_charged_due_amount) / 100 + $this->total_charged_due_order * 0.30);
    }

    //total_due_charge_amount
    public function getTotalDueChargeAmountAttribute()
    {
        return (($this->paywall_charge * $this->total_charged_due_amount) / 100);
    }

    //total_paid_amount
    public function getTotalPaidAmountAttribute()
    {
        return number_format((($this->paywall_charge * $this->total_charged_paid_amount) / 100 + $this->total_charged_paid_order * 0.30), 2, ',', '.');
    }

    public function paywall()
    {
        return $this->hasOne(PaywallCharge::class, 'user_id', 'id');
    }

    public function getPaywallChargeAttribute()
    {
        $import_profit_share_charge = ($this->import_plan_discount) ? $this->import_plan_discount->profit_share : 0;
        $extra_charge = ($this->flat_rate_tarif) ? 3.5 : 0;
        $charge = ($this->paywall) ? $this->paywall->charge : 1.4;
        return $charge + $extra_charge + $import_profit_share_charge;
    }

    public function flat_rate_tarif()
    {
        return $this->hasOne(FlatRateApp::class, 'user_id', 'id')->whereNotNull('flat_rate_status')->whereRaw('CAST(subscription_date_end as datetime) >= \'' . Carbon::today() . '\'');
    }

    public function import_plan_discount()
    {
        $today = Carbon::now()->toDateTimeString();
        return $this->hasOne(ImportPlanGetDiscounts::class, 'user_id', 'id')->where('status', 1)->where(function ($q) use ($today) {
            $q->where('exp_date', '>=', $today)->orWhere('end_date', '>=', $today);
        });
    }

    public function purchased_apps()
    {
        return $this->hasMany('App\Models\AppStore\PurchasedApp', 'cms_user_id', 'id');
    }

    public function assigned_apps()
    {
        return $this->hasMany('App\Models\AppStore\AssignedApp', 'user_id', 'id');
    }

    public function getAllAppsAttribute()
    {
        $assigned = $this->assigned_apps->pluck('app_id')->toArray();
        $fixed_price = $this->purchased_apps->where('type', 'LIKE', '%fixed price%')->pluck('app_id')->toArray();
        $purchased = $this->purchased_apps()->whereDate('subscription_date_end', '>=', \Carbon\Carbon::now())->pluck('app_id')->toArray();
        return array_unique(array_merge($assigned, $fixed_price, $purchased));
    }

    public function getAllWatchedVideos()
    {
        return $this->hasMany('App\VideoWatch', 'user_id');
    }

    public function marketplace_collections()
    {
        return $this->hasMany(\App\Models\Marketplace\Collection::class, 'supplier_id');
    }

    public static function isSupplier ($id)
    {
        return User::find($id)['id_cms_privileges'] == 4;
    }

    public function suppliersProducts ()
    {
        return Product::where('supplier_id', \CRUDBooster::myId())->get();
    }
}
