<?php

namespace App\Console;

use Aws\Command;
use Illuminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{

    protected $commands = [
        // Commands\GenarateSupplierMonthlyReport::class,
        Commands\GenerateProdductsSegments::class,
        Commands\BikeApiStockOutProductSync::class,
        Commands\BikeApiAllOrders::class,
        Commands\BikeApiProductSync::class,
        Commands\BikeApiOrderForNDays::class,
        Commands\BikeApiUpdatedProductSync::class,
        Commands\TrackingCodeSync::class,
        Commands\ProductAttrUpdate::class,
        Commands\B2bUhrenApiBrandSync::class,
        Commands\B2bUhrenApiProductSync::class,
        Commands\BikeApiStockSync::class,
        Commands\BDroppyProductSync::class,
        Commands\BDroppyCatelogSync::class,
        Commands\BDroppyCategorySync::class,
        Commands\BigbuyProductAutoSync::class,
        Commands\BigBuyProductPriceSync::class,
        Commands\BigbuyProductAutoImport::class,
        Commands\BigbuyShippingCostSync::class,
        Commands\MptoDrmProductCompareSync::class,
        Commands\CollectionProductCsvSync::class,
        Commands\EladyProductUpdate::class,
        Commands\AutoTransferProduts::class,
        Commands\UrlImageUploadCloud::class,
        Commands\VidaxlApiStockSync::class,
        Commands\BinoMertensProductSync::class,
        Commands\PlushStockSync::class,
        Commands\PlushTrackingNumberSync::class,

    ];


    protected function schedule(Schedule $schedule)
    {


        if ( env("APP_ENV") == 'local' ) {
            // For local test
            // $schedule->command('BDroppy:CatelogSync')->everyMinute();
            // $schedule->command('BDroppy:ProductSync')->everyMinute();
            // $schedule->command('BDroppy:CategorySync')->everyMinute();
            // $schedule->command('Bigbuy:ProductAutoSync')->everyMinute();
            $schedule->command('Bigbuy:ProductPriceAutoSync')->everyMinute();
            // $schedule->command('product:imageCloudUpload')->hourlyAt(16);
        } else {

            // START :: BDropy API
            // $schedule->command('BDroppy:CatelogSync')->dailyAt('00:10');
            // $schedule->command('BDroppy:ProductSync')->everyTwoHours();
            // END :: BDroppy API

            // START :: B2B
            // $schedule->command('b2buhrenApi:brandSync')->dailyAt('00:20');
            // $schedule->command('b2buhrenApi:productSync')->everyFiveMinutes(15);


            // START :: Bike API
            $schedule->command('bikeApi:stockOutProductSync')->hourly();
            $schedule->command('bikeApi:allSegments')->dailyAt('00:30');
            $schedule->command('bikeApi:productSync')->hourly();
            //    $schedule->command('bikeApi:updatedProductsSync')->everyThirtyMinutes('00:10');
            $schedule->command('bikeApi:stockSync')->everyFifteenMinutes();
            // $schedule->command('bikeApi:productAttrUpdate')->everyThreeHours(15);
            // END :: Bike API

            //Cloud image upload
            $schedule->command('product:imageCloudUpload')->cron('*/7 * * * *');
            //Cloud image upload end

            // END :: B2B


            // Others
            $schedule->command('trackingCode:sync')->hourly();

            // MP to DRM Product Purchase compare
            $schedule->command('mptodrmproductcompare:mptodrmcomparesync')->dailyAt('03:00');

            // Collection Product csv sync
            // $schedule->command('collectionCsv:collectionproductcsvsync')->hourly();
            // $schedule->command('elady:productsync')->hourly();

            // Mp to DRM Products Auto Transfer
            $schedule->command('categorySubscription:autoTransferProducts')->everyThreeHours();
            // $schedule->command('categorySubscription:autoTransferProducts')->everyTenMinutes();

            // $schedule->command('collectionCsv:collectionproductcsvsync')->hourly();

            // BigBuy Product Sync
               $schedule->command('Bigbuy:ProductAutoSync')->everyTenMinutes();
               $schedule->command('Bigbuy:ProductShippingCostSync')->everySixHours();

               $schedule->command('Bigbuy:ProductPriceAutoSync')->everyThirtyMinutes();
            //    $schedule->command('Bigbuy:ProductAutoImport')->daily();
            //    ->withoutOverlapping();

            // Vidaxl product stock and missing product sync
            $schedule->command('vidaxl:stockSync')->hourly();

            // Binomertens

            $schedule->command('binomerten:productSync')->everyThirtyMinutes();

            // plush
            // $schedule->command('plush:stock-sync')->hourly();

            // plush Tracking Number Sync
            // $schedule->command('plush:tracking-number-sync')->daily();

        }

    }
}
