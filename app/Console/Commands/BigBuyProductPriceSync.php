<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BigBuyApiController;
use Log;

class BigBuyProductPriceSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Bigbuy:ProductPriceAutoSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BigBuy Products Price Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        info('Bigbuy Api Product Price Sync Command Run . . .');
        app(\App\Http\Controllers\BigBuy\CountryWiseProductInsert::class)->priceSync();
        app(BigBuyApiController::class)->bigbuyVarientProductPriceSync();

        return 0;
        // app(BigBuyApiController::class)->bigbuyProductPriceSync();

    }
}
