<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BigBuyApiController;
use Log;

class BigbuyShippingCostSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Bigbuy:ProductChShippingCostSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BigBuy Products Ch Shipping cost Update';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Bigbuy ch shipping cost sync command Run . . .');
        app(BigBuyApiController::class)->updateProductChShippingCost();
        return 0;

    }
}
