<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BigBuyApiController;
use Log;

class BigbuyProductAutoImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Bigbuy:ProductAutoImport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BigBuy Products auto Import';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Bigbuy Api Product auto import Command Run . . .');
        app(BigBuyApiController::class)->bigbuyProductInsertNew();
        return 0;
    }
}
