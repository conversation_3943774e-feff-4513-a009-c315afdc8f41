<?php

namespace App\Console\Commands;

use App\Http\Controllers\BigBuy\CountryWiseProductInsert;
use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BigBuyApiController;

class BigbuyProductAutoSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Bigbuy:ProductAutoSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BigBuy Products Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // info('Bigbuy Api Product Sync Command Run . . .');
        // app(BigBuyApiController::class)->bigbuyProductStockSync();
        // return 0;

        info('Bigbuy Api Product Sync Command Run . . .');
        app(CountryWiseProductInsert::class)->stockSync();
        app(BigBuyApiController::class)->bigbuyVarientProductStockSync();
        return 0;
    }
}
