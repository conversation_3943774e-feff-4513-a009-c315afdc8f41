<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Console\Command;
use Log;

class GenerateProdductsSegments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bikeApi:allSegments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hit the Bike api and get the all segments(category) of products, Then the data will be created/updated in Database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $response = app(BikeApiController::class)
            ->productListArguments();
        Log::info('Bike api segment schedule ran just now. Response in below.', $response);
    }
}
