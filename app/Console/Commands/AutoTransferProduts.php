<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Marketplace\AutoTransferSubscription;
use Illuminate\Support\Facades\Log;

class AutoTransferProduts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'categorySubscription:autoTransferProducts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Category subscription wise product auto transfer to DRM';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        app(\App\Http\Controllers\Marketplace\ProductController::class)->autoProductsTransferHitting();
    }
}
