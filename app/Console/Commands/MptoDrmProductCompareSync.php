<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class MptoDrmProductCompareSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mptodrmproductcompare:mptodrmcomparesync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MP to DRM Product Purchases Compare';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info("MP To DRM purchaes compare sync scheduled ran!");

        app(\App\Http\Controllers\Marketplace\ProductController::class)
        ->compareMPPoroductToDrmProduct();
        return 0;
    }
}
