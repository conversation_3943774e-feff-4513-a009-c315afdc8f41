<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use Log;
use App\Jobs\Job;

class BDroppyCatelogSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'BDroppy:CatelogSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BDroppy Api Catelog Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('BDroppy Catelog Sync command run . . .');
        app( \App\Http\Controllers\Marketplace\BDroppyApiController::class )
           ->getCatelogList();
    }
}
