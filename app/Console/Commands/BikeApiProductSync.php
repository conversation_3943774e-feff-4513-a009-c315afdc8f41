<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Console\Command;
use Log;

class BikeApiProductSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bikeApi:productSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info("Product sync scheduled ran!");
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            ->getAllProductsManualProcess();
        // dispatch(new \App\Jobs\Marketplace\BikeApiProductSync());
        // app(BikeApiController::class)
        //     ->buildProductSyncJobs();
        return 0;
    }
}
