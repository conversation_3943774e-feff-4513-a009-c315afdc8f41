<?php

namespace App\Console\Commands;

use Log;
use Illuminate\Console\Command;
class BinoMertensProductSync extends Command
{
    protected $signature = 'binomerten:productSync';

    protected $description = 'Binomerten Products update ' ;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Log::info('Binomerten Product Sync command run . . .');
        app(\App\Http\Controllers\Marketplace\BinoMertensApiController::class)
            ->getDataFromXmlFile();
    }
}
