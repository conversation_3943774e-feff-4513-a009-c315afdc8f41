<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\CollectionController;
use Illuminate\Console\Command;

class CollectionProductCsvSync extends Command
{
    protected $signature = 'collectionCsv:collectionproductcsvsync';

    protected $description = 'Collection Product csv sync';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        app(CollectionController::class)->collectionProductSync();
        return 0;
    }
}
