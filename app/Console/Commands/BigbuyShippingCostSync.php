<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BigBuyApiController;
use Log;

class BigbuyShippingCostSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'Bigbuy:ProductShippingCostSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BigBuy Products Shipping cost Update';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Bigbuy shipping cost Sync Command Run . . .');
        app(BigBuyApiController::class)->updateProductShippingCost();
        return 0;

    }
}
