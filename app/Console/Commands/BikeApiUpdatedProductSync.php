<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Marketplace\BikeApiController;

class BikeApiUpdatedProductSync extends Command
{
    protected $signature = 'bikeApi:updatedProductsSync';

    protected $description = 'Sync the updated products Attributes';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        app(BikeApiController::class)
            ->buildChangesProductsSyncJobs(1);
        return 0;
    }
}
