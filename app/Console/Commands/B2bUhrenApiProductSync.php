<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class B2bUhrenApiProductSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'b2buhrenApi:productSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'B2bUhrenApi Product Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info("B2bUhrenApi Product sync scheduled ran!");
        // dispatch(new \App\Jobs\Marketplace\B2bUhrenApiProductSync());

        app(\App\Http\Controllers\Marketplace\B2bUhrenApiController::class)
        ->buildProductSyncJobs();
        return 0;
    }
}
