<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class PlushTrackingNumberSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plush:tracking-number-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Plush Tracking Number Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        app(\App\Http\Controllers\Marketplace\PlushController::class)
            ->plushTrackingNumberSync();
    }
}
