<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use Log;
use App\Jobs\Job;
use App\Http\Controllers\Marketplace\ProductController;
class BikeApiStockSync extends Command
{
    protected $signature = 'bikeApi:stockSync';

    protected $description = 'Bike Api Products stock update ' ;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Log::info('Bike Api Stock Sync command run . . .');
        app(ProductController::class)
            ->bikeApiStockUpdateFromCsvData();
    }
}
