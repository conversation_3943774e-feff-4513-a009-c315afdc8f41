<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class B2bUhrenApiBrandSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'b2buhrenApi:brandSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'B2bUhrenApi Brand Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info("B2bUhrenApi Brand sync scheduled ran!");

        app(\App\Http\Controllers\Marketplace\B2bUhrenApiController::class)
        ->brandSync();
        return 0;
    }
}
