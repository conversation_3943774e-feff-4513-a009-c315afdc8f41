<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class B2bUhrenApiStockSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'b2buhrenApi:stockSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'b2buhrenApi product stock sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info("B2bUhrenApi Product stock sync scheduled ran!");

    }
}
