<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\ProductController;
use Illuminate\Console\Command;
use Log;

class GenarateSupplierMonthlyReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'supplier:genarateMonthlyReport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hit the supplier mothly report genarate, Then the data will be created/updated in Database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        app(ProductController::class)->createMonthlyReport();
    }
}
