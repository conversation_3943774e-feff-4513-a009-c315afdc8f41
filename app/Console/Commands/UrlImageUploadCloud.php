<?php

namespace App\Console\Commands;

use Log;
use Illuminate\Console\Command;
use App\Models\Marketplace\Product;
use App\Enums\Marketplace\ImageProcess;
use Carbon\Carbon;

class UrlImageUploadCloud extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:imageCloudUpload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        info("Product Image upload scheduled ran!".Carbon::now());
        $mpProducts = Product::with('drmProducts')
                        ->whereNotNull('image')
                        ->where('image','!=' ,'[]')
                        ->where('image','!=' ,'null')
                        ->where('is_image_process',ImageProcess::NO)
                        ->select('id','image','old_images','is_image_process','ean')
                        ->take(250)
                        ->get();
        foreach($mpProducts->chunk(50) as $products){
            dispatch(new \App\Jobs\Marketplace\UrlImageUploadCloud($products));
        }
    }
}
