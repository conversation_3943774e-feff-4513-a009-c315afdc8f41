<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use Log;
use App\Jobs\Job;

class BikeApiStockOutProductSync extends Command
{
    protected $signature = 'bikeApi:stockOutProductSync';

    protected $description = 'Bike Api stock out Products sync ' ;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Log::info('Bike Api Stock out product Sync command run . . .');
        app(\App\Services\Marketplace\BikeApi\BikeApiService::class)->syncStockOutProducts();
    }
}
