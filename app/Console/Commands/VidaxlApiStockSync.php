<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
class VidaxlApiStockSync extends Command
{
    protected $signature = 'vidaxl:stockSync';

    protected $description = 'Vidaxl Api Products stock update ' ;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        info('Vidaxl API Stock Sync command run . . .');
        
        app(\App\Services\Marketplace\Vidaxl\StockSyncProcess::class)->process();

        app(\App\Http\Controllers\Marketplace\VidaXlApiController::class)
            ->stockUpdateFromCsv();
    }
}
