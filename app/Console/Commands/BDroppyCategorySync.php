<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class BDroppyCategorySync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'BDroppy:CategorySync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'BDroppy Category sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Okey');
        dispatch( new \App\Jobs\Marketplace\BDroppyCategorySync() );
    }
}
