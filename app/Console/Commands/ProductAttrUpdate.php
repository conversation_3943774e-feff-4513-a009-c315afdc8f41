<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class ProductAttrUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bikeApi:productAttrUpdate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('Product update schedule ran');
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)->updateProductAttr();
    }
}
