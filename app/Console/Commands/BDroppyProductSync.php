<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;
use Log;
use App\Jobs\Job;

class BDroppyProductSync extends Command
{
    protected $signature = 'BDroppy:ProductSync';

    protected $description = 'BDroppy Product sync command ' ;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        Log::info('BDroppy Product Sync command run . . .');
        app( \App\Http\Controllers\Marketplace\BDroppyApiController::class )->importProducts();
    }
}
