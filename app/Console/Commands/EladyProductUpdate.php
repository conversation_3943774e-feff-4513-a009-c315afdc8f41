<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\CollectionController;
use Illuminate\Console\Command;

class EladyProductUpdate extends Command
{
    protected $signature = 'elady:productsync';

    protected $description = 'Elady Product stock and price update';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        app(CollectionController::class)->eladyProductUpdate();
        return 0;
    }
}
