<?php

use Illuminate\Support\Facades\DB;


/*
	---------------
	Get Customer info of DRM order
	Parameter should be a model of new_order
	The returned format is suitable for BikeApi order
	---------------
*/
function getAndProcessCustomerInfosFromDrmOrderForBikeApiOrder ($drmOrder)
{
    // Getting and processing customer info
    $drmOrderCustomer = DB::connection('drm_core')->table("new_orders")
        ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
        ->join("cms_users", "cms_users.id", "=", "new_orders.cms_user_id")
        ->select("new_orders.id as mp_order_id","new_orders.cms_user_id as order_user_id","new_orders.shipping", "new_customers.phone","new_customers.email","cms_users.contact_number as user_contact_number")
        ->where('new_orders.id', $drmOrder->marketplace_order_ref ?? $drmOrder->id)
        ->first();

    $shippingAddressArr = json_decode($drmOrderCustomer->shipping, 1);

    $coustomer_street = $street = $shippingAddressArr['street'];
    $street_no  = null;
    $street     = null;

    // Find a match and store it in $result.
    if (preg_match('/([^\d]+)\s?(.+)/i', $coustomer_street, $raw_street) && preg_match('/\\d/', $coustomer_street)) {
        $street_no = trim($raw_street[2]);
        $street = trim($raw_street[1]);
    }

    // An array
    $fullName = split_name($shippingAddressArr['name']);

    $country = DB::connection('drm_core')->table('tax_rates')
        ->where('country_code', 'like', $shippingAddressArr['country'])
        ->orWhere('country', 'like', $shippingAddressArr['country'])
        ->orWhere('country_de', 'like', $shippingAddressArr['country'])
        ->orWhere('country_es', 'like', $shippingAddressArr['country'])
        ->first()->country_code;

    $user_billing_phone =  DB::connection('drm_core')->table('billing_details')->where('user_id', $drmOrderCustomer->order_user_id)->value('phone') ?? '';

    $phone = '';
    if(!empty($drmOrderCustomer->phone)){
        $phone = $drmOrderCustomer->phone;
    }else if (!empty($user_billing_phone)){
        $phone = $user_billing_phone;
    }else if(!empty($drmOrderCustomer->user_contact_number)){
        $phone = $drmOrderCustomer->user_contact_number;
    }

    // Bike APi customer info structure array
    $customerInfo = [
        "first_name"    => $fullName['first_name'],
        "last_name"     => $fullName['last_name'],
        "email"         => $drmOrderCustomer->email,
        "street"        => $street,
        "house_no"      => $street_no,
        "zipcode"      => $shippingAddressArr['zip_code'],
        "city"          => $shippingAddressArr['city'],
        "country"       => $country,
        "address"       => $coustomer_street,
        "company"       => $shippingAddressArr['company'],
        "phone"         => (!blank($phone) && strlen($phone) > 3) ? $phone : '34919019948'
    ];


    return $customerInfo;
}

// Split a full name to first_name and last_name
function split_name($name)
{
    $name = trim($name);
    $name = preg_replace('/(_|-)/', ' ', $name);
    $name = trim($name);

    $last_name = (strpos($name, ' ')) ? preg_replace('#.*\s([\w-]*)$#', '$1', substr($name, strpos($name, ' '))) : '';
    $first_name = trim(preg_replace('#' . preg_quote($last_name, '#') . '#', '', $name));

    if(empty($first_name))
    {
        $parts = explode(' ', $last_name);
        $first_name = @$parts[0];
    }
    return ['first_name' => $first_name, 'last_name' => $last_name];
}

function removeCommaFromPrice($amount)
{
    $total = $amount;
    if (strpos($total, ",")) {
        $have = [".", ","];
        $will_be = ["", "."];
        $total = str_replace($have, $will_be, $total);
    }
    return $total;
}

function removeDots($key){
    return str_replace('.', '', $key);
}


function containsOnlyNull($input){
    if ($input != null) {
        return empty(array_filter($input, function ($a) {
            return $a !== null;
        }));
    } else {
        return true;
    }
}

function pathIsUrl($path)
{
    if (filter_var($path, FILTER_VALIDATE_URL)) {
        return true;
    } else {
        return false;
    }
}

function randomEan()
{
    $pool = '0123456789';
    return substr(str_shuffle(str_repeat($pool, 5)), 0, 13);
}

function makeUpdateStatusJson()
{
    $status = [
        'title' => 1,
        'description' => 1,
        'image' => 1,
        'ek_price' => 1,
        'stock' => 1,
        'status' => 1,
        'gender' => 1,
        'item_weight' => 1,
        'item_color' => 1,
        'production_year' => 1,
        'materials' => 1,
        'brand' => 1,
        'item_size' => 1,
        'category' => 1,
        'uvp' => 1,
        'delivery_days' => 1,
        'shipping_cost' => 1
    ];
    return json_encode($status);
}

function isLocal()
{
    if (!app()->environment('production')) {
        return true;
    } else {
        return false;
    }
}

function userWiseVkPriceCalculate($vk_price, $user_id, $discount = 0.0)
{
    if($user_id == 2766 || $user_id == 2698 ){
        $agreement = DB::connection('drm_core')->table('mp_payment_agreements')
                    ->where('user_id', '=', $user_id)
                    ->where('type', '=', 1)
                    ->select('price_markup')
                    ->first();
        $price_markup = $agreement ? $agreement->price_markup : 0.0;
    } else {
        $price_markup = 0.0;
    }
    $vk_price = $vk_price + (($price_markup * $vk_price) / 100);

    return $vk_price - (($discount * $vk_price) / 100);
}
