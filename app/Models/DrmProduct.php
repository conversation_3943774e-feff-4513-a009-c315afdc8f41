<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\DRMProductCategory;
// use Illuminate\Database\Eloquent\SoftDeletes;
use App\Shop;
use App\User;
use Illuminate\Support\Arr;
use App\Models\ChannelProduct;
use App\Models\DrmCategory;
use Carbon\Carbon;

class DrmProduct extends Model
{
    // use SoftDeletes;

    protected $connection   = \App\Enums\Marketplace\DBConnections::DRM_CORE;
    protected $table        = 'drm_products';
    protected $guarded      = [];


    protected $casts = [
        'title' => 'array',
        'description' => 'array'
    ];

    public static function boot()
    {
        parent::boot();
        static::updating(function($item) {
            if ( $item->isDirty('ek_price') ){
                $item->old_ek_price = $item->getOriginal('ek_price');
                $item->ek_price_updated_at = Carbon::now()->toDateTimeString();
            }

            if ( $item->isDirty('uvp') ){
                $item->old_uvp = $item->getOriginal('uvp');
                $item->uvp_updated_at = Carbon::now()->toDateTimeString();
            }

        });

    }

    public function drm_categories()
    {
        return $this->hasMany(DRMProductCategory::class, 'product_id');
    }

    public function suppliers()
    {
        return $this->hasOne(DeliveryCompany::class,'id','delivery_company_id');
    }

    public function getFirstImageAttribute()
    {
        $first_image = Arr::get($this->image, '0');
        if(is_array($first_image)){
            return $first_image['src'] ?? "";
        }
        else{
            return $first_image;
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getImageAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setImageAttribute($value)
    {
        $this->attributes['image'] = json_encode($value);
    }

    public function getNameAttribute()
    {
        return Arr::get($this->title, data_get(request(), 'lang', 'de'), '');
    }

    public function getTransDescriptionAttribute()
    {
        return Arr::get($this->description, data_get(request(), 'lang', 'de'), '');
    }

    public function marketplaceProduct ()
    {
        return $this->hasOne(\App\Models\Marketplace\Product::class, 'api_product_id', 'marketplace_product_id');
    }


}
