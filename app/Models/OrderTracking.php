<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderTracking extends Model
{

    public const MANUAL_TRACKING = 0;
    public const SHIPCLOUD_TRACKING = 1;

    protected $connection = \App\Enums\Marketplace\DBConnections::DRM_CORE;
    protected $table    = 'order_trackings';
    protected $guarded  = [];


    public function order(){
        return $this->belongsTo(\App\Models\Order::class, 'order_id');
    }

    protected $casts = [
        'shipment_data' => 'array',
    ];
}
