<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    protected $connection   = \App\Enums\Marketplace\DBConnections::DRM_CORE;
    protected $table = 'countries';
    
    public function drm_imports()
    {
        return $this->hasMany(DrmImport::class);
    }

    public function marketplaceProducts () 
    {
        return $this->hasMany(App\Models\Product::class, 'country_id');
    }
}
