<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\DrmProduct;
use App\Models\DrmCategory;

class DRMProductCategory extends Model
{
    protected $connection   = \App\Enums\Marketplace\DBConnections::DRM_CORE;

    protected $table = "drm_product_categories";

    protected $fillable = [
        'product_id',
        'category_id' 
    ];

    public $timestamps = false;
    
    public function category()
    {
        return $this->belongsTo(DrmProduct::class, 'product_id');
    }

    public function drm_products()
    {
        return $this->hasMany(DrmProduct::class);
    }

    public function drm_category()
    {
        return $this->belongsTo(DrmCategory::class, 'category_id');
    }
}
