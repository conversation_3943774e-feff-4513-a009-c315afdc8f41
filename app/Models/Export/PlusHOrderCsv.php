<?php

namespace App\Models\Export;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;


class PlusHOrderCsv implements FromCollection, WithHeadings , WithCustomCsvSettings
{
    public $products;
    public $customer_info;
    public $order_info;

    public function __construct($products,$customer_info, $order_info)
    {
        $this->products = $products;
        $this->customer_info = $customer_info;
        $this->order_info = $order_info;
    }

    public function collection()
    {
        $orderProducts = $this->products ?? [];
        $customerInfo = $this->customer_info ?? [];
        $orderInfo = $this->order_info ?? [];
        $result = array();
        foreach ($orderProducts as $product) {
            $result[] = array(
                'beleg_belegnr'               => '50051'.'-'.$orderInfo['order_id'],
                'beleg_position'              => '',
                'beleg_versandart'            => '',
                'beleg_kundennummer'          => '50051',
                'beleg_liefername'            => $customerInfo['first_name'].' '. $customerInfo['last_name'],
                'beleg_lieferadresszusatz'    => '',
                'beleg_lieferansprechpartner' => '',
                'beleg_lieferland'            => $customerInfo['country'],
                'beleg_lieferstrasse'         => $customerInfo['address'],
                'beleg_lieferort'             => $customerInfo['city'],
                'beleg_lieferplz'             => $customerInfo['zipcode'],
                'beleg_email'                 => $customerInfo['email'],
                'beleg_telefon'               => $customerInfo['phone'],
                'beleg_internet'              => '',
                'artikel_nummer'              => $product['ean'],
                'artikel_menge'               => $product['qty']
            );
        }

        return collect($result);
    }

    public function getCsvSettings(): array
    {
        return [
            'Content-Encoding' => 'UTF-8',
            'delimiter' => ';',
            'enclosure' => '',
        ];
    }

    public function headings(): array
    {
        return ['beleg_belegnr', 'beleg_position', 'beleg_versandart', 'beleg_kundennummer', 'beleg_liefername', 'beleg_lieferadresszusatz', 'beleg_lieferansprechpartner', 'beleg_lieferland', 'beleg_lieferstrasse', 'beleg_lieferort', 'beleg_lieferplz', 'beleg_email', 'beleg_telefon', 'beleg_internet', 'artikel_nummer', 'artikel_menge'];
    }
}
