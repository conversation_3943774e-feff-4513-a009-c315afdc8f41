<?php

namespace App\Models\Export;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class MarketplaceProductsExport implements FromCollection, WithHeadings
{
    public $products;
    public $header;

    public function __construct($products, $header)
    {
        $this->products = $products;
        $this->header = $header;
    }
    
    public function collection(){
        $result = array();
        $product_item = [];
        foreach($this->products as $product){
            
            $product = $product->toArray();
            foreach($product as $key => $value){
                $product_item[$key] = $value;
            }
            $result[] = $product_item;
        }

        return collect($result);
    }
    
    public function headings(): array
    {
        return $this->header;
    }

}