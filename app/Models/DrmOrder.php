<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DrmOrder extends Model
{
    protected $connection = \App\Enums\Marketplace\DBConnections::DRM_CORE;

    protected $table = 'new_orders';

    protected $guarded = [];

    //Customer who buy this
    public function customer(){
        return $this->belongsTo(\App\Models\DrmCustomer::class,'drm_customer_id');
    }

    public function getProductsAttribute(){
        return ($this->cart)? json_decode($this->cart) : null;
    }
}
