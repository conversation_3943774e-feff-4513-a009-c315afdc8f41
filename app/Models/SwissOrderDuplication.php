<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SwissOrderDuplication extends Model
{
    protected $table = 'swiss_order_duplications';

    protected $connection = \App\Enums\Marketplace\DBConnections::DRM_CORE;

    protected $fillable = [
        'original_order_id',
        'duplicate_order_id',
        'original_address',
        'mycargogate_address',
        'status'
    ];

    protected $casts = [
        'original_address' => 'array',
        'mycargogate_address' => 'array'
    ];

    public function originalOrder()
    {
        return $this->belongsTo(DrmOrder::class, 'original_order_id');
    }

    public function duplicateOrder()
    {
        return $this->belongsTo(DrmOrder::class, 'duplicate_order_id');
    }

    /**
     * Get MyCargoGate German address structure
     */
    public static function getMyCargoGateAddress()
    {
        return [
            'name' => 'MyCargoGate Germany GmbH',
            'company' => 'MyCargoGate Germany GmbH',
            'address_addition' => 'SmartGateFlex',
            'street' => 'Earl-<PERSON><PERSON><PERSON><PERSON> St<PERSON>',
            'house_no' => '8',
            'zip_code' => '79618',
            'city' => 'Rheinfelden',
            'country' => 'DE'
        ];
    }

    /**
     * Check if an order has been duplicated for Swiss processing
     */
    public static function isDuplicated($orderId)
    {
        return self::where('original_order_id', $orderId)->exists();
    }
}
