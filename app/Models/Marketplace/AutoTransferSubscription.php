<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class AutoTransferSubscription extends Model
{
    protected $table = 'marketplace_auto_transfer_subscriptions';
    protected $guarded = [];

    protected $casts = [
        'billing_info'  => 'array',
        'history'       => 'array',
        'transfered_product_ids' => 'array',
        'brand' => 'array',
        'skip_brand' => 'array',
        'filter_ek' => 'array',
        'filter_uvp' => 'array',
        'filter_profit' => 'array',
        'filter_quantity' => 'array',
    ];
}
