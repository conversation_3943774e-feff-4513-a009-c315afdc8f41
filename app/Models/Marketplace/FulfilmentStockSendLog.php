<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class FulfilmentStockSendLog extends Model
{
    protected $table = 'fulfilment_stock_send_log';

    protected $fillable = [
        'product_id',
        'send_stock',
        'recived_stock',
        'send_more',
        'reserved',
        'is_left',
        'recived_stock_response'
    ];

    protected $casts = [
        'recived_stock_response' => 'array',
        'error_response' => 'array'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
