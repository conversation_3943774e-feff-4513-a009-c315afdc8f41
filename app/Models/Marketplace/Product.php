<?php

namespace App\Models\Marketplace;

use App\User;
use App\Enums\ProfitType;
use App\Models\DrmProduct;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use App\Models\DrmProduct as LuminDrmProduct;
use App\Models\Marketplace\MpSendStockComment;
use App\Models\Marketplace\MpCoreDrmTransferProduct;

class Product extends Model
{
    protected $table = 'marketplace_products';

    protected $guarded = [];

    protected $casts = [
       'misc' => 'array',
       'image' => 'array',
       'category' => 'array',
       'update_status' => 'array',
       'industry_template_data' => 'array',

        // 'stock_info' => 'array',
        // 'images' => 'array',
        // 'product_misc' => 'array',
        // 'internel_api_response' => 'array',
        // 'misc' => 'array',
    ];

    public function collection()
    {
        return $this->belongsTo(Collection::class);
    }

    public function mainCategory()
    {
        return $this->belongsTo(Category::class);
    }

    public function category()
    {
       return $this->belongsTo(Category::class);
        // return $this->belongsToMany(Category::class,'marketplace_product_categories','product_id','category_id')->withPivot('sub_categories');
    }

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    public function parent()
    {
        return $this->belongsTo(Product::class, 'parent_id');
    }

    public function getProfitAmountAttribute()
    {
        if(is_null($this->collection->product_profit_type) || is_null($this->collection->product_profit_price) || empty($this->parent)) {
            return 0;
        }

        return $this->collection->product_profit_type == ProfitType::FIXED ? $this->collection->product_profit_price : ($this->parent->price * $this->collection->product_profit_price) / 100;
    }

    public function listing_prices()
    {
        return $this->morphToMany(ListingPrice::class, 'listable', 'marketplace_listables');
    }

    // public function getFirstImageAttribute()
    // {
    //     return $this->image;
    // }

    // public function setImagesAttribute($value)
    // {
    //     return json_encode($value, true) ?? [];
    // }
    public function getImageAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setImageAttribute($value)
    {
        $this->attributes['image'] = json_encode($value);
    }

    public function getImagesAttribute()
    {
        return $this->image;
        return Arr::get($this->image, '0');
    }

    public static function isInDrmProductList($product, $user_id)
    {
        $drmProduct = new DrmProduct();
        if ( $drmProduct->where('marketplace_product_id', $product->id)
                ->where('user_id', $user_id)->exists() ) {
            return true;
        } else {
            return false;
        }
    }

    public static function isInMpDrmCoreTable($product_id, $user_id)
    {
        $MpCoreToDrm = new MpCoreDrmTransferProduct();
        if ( $MpCoreToDrm->where('marketplace_product_id', $product_id)
                        ->where('user_id', $user_id)->exists() ) {
            return true;
        } else {
            return false;
        }
    }

    public function drmProducts()
    {
        return $this->hasMany(LuminDrmProduct::class,'marketplace_product_id');
    }

    public function drmProductsV2()
    {
        return $this->hasMany(MarketplaceTransferredProductsInDrm::class,'marketplace_product_id');
    }

    public static function boot()
    {
        parent::boot();

        static::saving (function ($model) {
            self::savingOrUpdating($model);
            $status = [
                "name"=>1,
                "description"=>1,
                "image"=>1,
                "ek_price"=>1,
                "vk_price"=>1,
                "stock"=>1,
                "gender"=>1,
                "item_weight"=>1,
                "item_color"=>1,
                "production_year"=>1,
                "materials"=>1,
                "brand"=>1,
                "item_size"=>1,
                "uvp"=>1,
                "vat"=>1,
                "delivery_days"=>1,
                "shipping_cost"=>1,
                "item_number"=>1
            ];
            $model->update_status = $status;
        });

        static::updating (function ($model) {
           self::savingOrUpdating($model);

           if ( $model->isDirty('ek_price') ){
                $model->old_ek_price = $model->getOriginal('ek_price');
                $model->ek_price_updated_at = \Carbon\Carbon::now()->toDateTimeString();
            }

            if ( $model->isDirty('vk_price') ){
                $model->old_vk_price = $model->getOriginal('vk_price');
                $model->vk_price_updated_at = \Carbon\Carbon::now()->toDateTimeString();
            }

        });

    }

    public static function savingOrUpdating ($model) {

        if ( $model->isDirty('stock') ) {
            $model->old_stock        = $model->getOriginal('stock');
            $model->stock_updated_at = \Carbon\Carbon::now();
        }

        if ( $model->isDirty('internel_stock') ) {
            // $model->old_internel_stock          = $model->getOriginal('internel_stock');
            $model->internel_stock_updated_at   = \Carbon\Carbon::now();
        }

    }

    public function additionalInfo(){
        return $this->hasOne(AdditionalInfo::class,'product_id','id');
    }

    public function productBrand()
    {
        return $this->hasOne(ProductBrand::class,'id','brand');
    }

    public function stockSendComment()
    {
        return $this->hasOne(MpSendStockComment::class, 'mp_product_id', 'id');
    }

}
