<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class AdditionalInfo extends Model
{
    
    protected $table = 'mp_product_additional_info';

    protected $fillable = [
        'product_id',
        'manufacturer',
        'manufacturer_link',
        'manufacturer_id',
        'custom_tariff_number',
        'shipping_company_id',
        'region',
        'country_of_origin',
        'min_stock',
        'min_order',
        'gross_weight',
        'net_weight',
        'product_length',
        'product_width',
        'product_height',
        'volume',
        'packaging_length',
        'packaging_width',
        'packaging_height',
        'item_unit',
        'packing_unit',
        'volume_gross',
        'model_number',
        'item_description',
        'item_sub_color',
        'base_materials',
        'quantity_in_ml',
        'area_in_cube',
        'area_in_square',
        'price_labeling_obligation',
        'base_price_reference',
        'base_price',
        'target_group',
        'age_recommendation',
        'electrical_appliance',
        'energy_efficiency_class',
        'energy_label',
        'quantity_in_pieces'
    ];

    protected $casts = [
        'energy_label' => 'array',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}