<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use DRMProductCategory;

class DrmCategory extends Model
{
    protected $connection   = \App\Enums\Marketplace\DBConnections::DRM_CORE;
    protected $table = 'drm_category';

    public $timestamps = false;

    protected $fillable = [
        'id',
        'category_name',
        'category_name_en',
        'category_name_de',
        'category_name_fr',
        'category_name_it',
        'category_name_nl',
        'category_name_es',
        'category_name_sv',
        'category_name_pl',
        'category_name_ru',
        'category_name_bg',
        'category_name_da',
        'category_name_et',
        'category_name_fi',
        'category_name_el',
        'category_name_ga',
        'category_name_hr',
        'category_name_lv',
        'category_name_lt',
        'category_name_lb',
        'category_name_mt',
        'category_name_pt',
        'category_name_ro',
        'category_name_sk',
        'category_name_sl',
        'category_name_cs',
        'category_name_hu',
        'user_id',
        'country_id'
    ];

    public function drm_products()
    {
        return $this->hasMany(DRMProductCategory::class,'category_id','id');
    }
}
