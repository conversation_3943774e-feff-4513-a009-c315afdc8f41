<?php

namespace App\Models;

use App\DrmProduct;
use App\Enums\ProfitType;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class ProductSync extends Model
{
    protected $table = 'marketplace_products';
    //  protected $table = 'marketplace_bigbuy_product';
    // protected $table = 'marketplace_bigbuy_test_product';

    // protected $connection = 'drm_team';

    protected $guarded = [];

    protected $casts = [

     'ek_price' => 'double',

    ];

    public function drmProducts ()
    {
        return $this->hasMany(\App\Models\DrmProduct::class,'marketplace_product_id');
    }
}
