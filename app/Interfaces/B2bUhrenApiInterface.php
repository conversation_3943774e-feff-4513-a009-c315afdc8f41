<?php

namespace App\Interfaces;

interface B2bUhrenApiInterface{
    public function brandSync();
    public function getProductsByBrand($brand);
    public function buildProductSyncJobs();
//    public function productListArguments();
//    public function productsPerSegment($segment);
//    public function v2FetchChangedProductsForNDays($pagesArr, $days);
//    public function fetchStockChangesInTheLastNMinutes($minutes = 5);
//    public function fetchStockPerProductById($id);
//    public function listAllTrackingCodeForUser();
//    public function fetchTrackingCodeById($id);
//    public function listAllOrdersForUser();
//    public function fetchOrderById($id);
//    public function submitAnOrder();
//    public function fetchOrdersForlastNDays($days);
}
