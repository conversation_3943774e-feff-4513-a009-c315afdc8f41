<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Interfaces\OrderInterFace;
use App\Models\Marketplace\ApiOrdersResponse;
use Log;
class OrderController extends Controller
{

    private OrderInterFace $orderRepository;
    public function __construct(OrderInterFace $orderRepository)
    {
        $this->orderRepository = $orderRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function getOrderFromDRM($orderId)
    {
        try{
            $order_informations  = $this->orderRepository->getOrderById($orderId);


            Log::info("Order Information: ".$orderId);
            Log::info($order_informations);
            Log::info("Order Information end");
            
            if(isset($order_informations) && isset($order_informations['products']) && isset($order_informations['order_info'])){
                return $this->orderRepository->sendOrderToApi($order_informations);
            }else{
                return response()->json(['status'=>'error','message' => 'product or customer information not found'], 422);
            }

        }catch (\Exception $e){
            return response()->json([
                'status'=>'error',
                'message'=>$e->getMessage(),
            ]);
        }
    }

    public function getOrderTrackingNumber(){
        Log::info("start order tracking code Process.............");

        // order_id = api order id
        $api_order_responses = ApiOrdersResponse::select('id','api_id','status','drm_order_id','order_id','tracking_codes')
                                                ->where('status',1)
                                                ->whereNull('tracking_codes')
                                                ->orderBy('api_id','asc')
                                                ->get()
                                                ->groupBy('api_id');
        if(count($api_order_responses) > 0){
            $all_api_tracking_address     = \App\Enums\Marketplace\ApiResources::ALL_API_TRACKING_ADDRESS;
            foreach($api_order_responses as $api_order_response){
                if(array_key_exists($api_order_response[0]['api_id'],$all_api_tracking_address)){
                    app(\App\Enums\Marketplace\ApiResources::ALL_API_TRACKING_ADDRESS[$api_order_response[0]['api_id']].''::class)->getApiTracking($api_order_response);
                }
            }
        }else{
            Log::info("No order found for tracking code Process.............");
        }
        
        Log::info("end order tracking code Process.............");
    }
}
