<?php

namespace App\Http\Controllers\Marketplace;

use League\Csv\Writer;
use App\Models\DrmProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use App\Models\Marketplace\AllApiSyncReport;
use App\Services\Marketplace\ProductService;
use App\Models\Marketplace\ApiOrdersResponse;
use App\Services\Marketplace\BigBuyApi\BigBuyApiService;
use App\Enums\Marketplace\ApiResources;
use App\Models\DrmOrder;

class BigBuyApiController extends Controller
{
    public function updateBigBuyVat()
    {
        $variation_url = 'rest/catalog/products';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        if (isset($variation_products) && Redis::connection()) {
            Log::info('Redis get variation product');
            Redis::set('bigbuy_product_veriation', json_encode($variation_products));
        };

        $apiProduct = collect(json_decode(Redis::get('bigbuy_product_veriation'), true));
        $apiProductVat = $apiProduct->pluck('taxRate', 'id')->toArray();
        $apiProductVatId = $apiProduct->pluck('taxId', 'id')->toArray();
        $product = new Product();
        $local_products = $product->where('api_id', 4)
            ->pluck('api_product_id')->toArray();

        foreach (array_chunk($local_products, 1500) as $chunks) {
            foreach ($chunks as $id) {
                $product = new Product();
                $product->where('api_product_id', $id)->update([
                    'vat' => $apiProductVat[$id] ?? null,
                    'tax_type' => $apiProductVatId[$id] ?? 1,
                ]);
                Log::info('Update BigBuy Product Vat -' . $id);
            }
        }
    }


    public function updateDrmTaxType()
    {
        $product = new Product();
        $local_products = $product->where('api_id', 4)
            ->where('tax_type', 3)
            ->pluck('id')->toArray();
        $transfer_product = \App\Models\Marketplace\MpCoreDrmTransferProduct::pluck('marketplace_product_id')->toArray();
        foreach ($local_products as $key) {
            if (in_array($key, $transfer_product)) {
                DrmProduct::where('marketplace_product_id', $key)->update([
                    'tax_type' => 2
                ]);
                Log::info('Update BigBuy Drm Product Vat -' . $key);
            }
        }
    }
    public function updateBrand()
    {
        $productBrand = collect(json_decode(Redis::get('bigbuy_product_brand'), true));
        $apiProduct = collect(json_decode(Redis::get('bigbuy_product_veriation'), true));

        $local_product = Product::where('api_id', 4)
            ->pluck('api_product_id')->toArray();

        foreach (array_chunk($local_product, 1500) as $chunks) {
            $apiProducts = $apiProduct->whereIn('id', $chunks);
            $product_brands = $productBrand->whereIn('id', $apiProducts->pluck('manufacturer')->toArray());
            foreach ($chunks as $id) {
                Product::where('api_product_id', $id)->update([
                    'brand' =>  $product_brands->where(
                        'id',
                        $apiProducts->where('id', $id)->first()['manufacturer'] ?? ""
                    )->first()['name'] ?? '',
                ]);
                Log::info("product brand updated");
            }
        }
        Log::info("finish all products brand updated");
    }

    public function updateProductApiCategoryId()
    {
        Log::info('Update product api_category_id');

        $variation_url = 'rest/catalog/products';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        if (isset($variation_products) && Redis::connection()) {
            Log::info('Redis get variation product');
            Redis::set('bigbuy_product_veriation', json_encode($variation_products));
        };
        $allApiProducts = collect(json_decode(Redis::get('bigbuy_product_veriation'), true));
        // $product = new Product();
        $local_products = collect(Product::where('api_id', 4)
            ->select('api_category_id', 'api_product_id')->get());
        $local_api_category_id = $local_products->pluck('api_category_id', 'api_product_id')->toArray();

        $apiProducts = $allApiProducts->pluck('category', 'id')->toArray();

        foreach ($apiProducts as $key => $category_id) {
            if (array_key_exists($key, $local_api_category_id)) {
                Product::where('api_product_id', $key)
                    ->where('api_id', 4)
                    ->update([
                        'api_category_id' => $category_id,
                    ]);
                Log::info("BigBuy Category Id Updated - " . $key);
            }
        }
        Log::info('Successfully update product api_category_id');
    }

    public function getProductById($product_id)
    {
        if (isset($product_id)) {
            $product = Product::where('api_id', 4)->where('id', $product_id)->select('id', 'api_id', 'api_product_id')->first();
            $url = 'rest/catalog/product/' . $product->api_product_id;
            $response = app(BigBuyApiService::class)->fetchData($url);
            dd($response);
        }
        return "not found";
    }

    public function bigbuyProductStockSync()
    {
        Log::info("scheduler found");
        // $url = 'rest/catalog/productsstock';
        // $product_stocks = app(BigBuyApiService::class)->fetchData($url);
        $product_stocks = $product_stocks =$this->getParentProductStock();

        if (isset($product_stocks)) {
            Log::info("Stock api hit done");
            app(BigBuyApiService::class)->productStockSync($product_stocks);
            Log::info("Finish BigBuy Product Stock Sync");
            $this->bigbuyVarientProductStockSync();
        } else {
            Log::info("Api not hit");
        }
    }

    public function bigbuyProductPriceSync()
    {
        Log::info("price scheduler found");
        $url = 'rest/catalog/products';

        $product_price = app(\App\Services\Marketplace\BigBuyApi\BigBuyApiService::class)->fetchData($url);

        if (isset($product_price)) {
            Log::info("Price api hit done");
            app(BigBuyApiService::class)->productPriceSync($product_price);
            app(BigBuyApiService::class)->productUVPSync($product_price);
            Log::info("Finish BigBuy Product Price Sync");
            $this->bigbuyVarientProductPriceSync();
        } else {
            Log::info("Api not hit");
        }
    }

    public function updateBigbuyProduct()
    {
        dd("not run");
        $variation_url = 'rest/catalog/new-products';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        // if(isset($variation_products) && Redis::connection()){
        //     Log::info('Redis get variation product');
        //     Redis::set('bigbuy_product_veriation',json_encode($variation_products));
        // };

        if (isset($variation_products)) {
            $stock_url = 'rest/catalog/productsstock';
            $product_stocks = app(BigBuyApiService::class)->fetchData($stock_url);
            if (isset($product_stocks) && Redis::connection()) {
                Log::info('Redis get product stock');
                Redis::set('bigbuy_product_stock', json_encode($product_stocks));
            }

            // $brand_url = 'rest/catalog/manufacturers';
            // $product_brands = app(BigBuyApiService::class)->fetchData($brand_url);
            // if(isset($product_brands) && Redis::connection()){
            //     Log::info('Redis get product brand');
            //     Redis::set('bigbuy_product_brand',json_encode($product_brands));
            // }

            $image_url = 'rest/catalog/productsimages';
            $product_image = app(BigBuyApiService::class)->fetchData($image_url);
            $images_arr = [];
            foreach ($product_image as $image) {
                $images_arr[$image['id']] = $image['images'];
            }
            if (!empty($images_arr) && Redis::connection()) {
                Log::info('Redis get product image');
                Redis::set('bigbuy_product_images', json_encode($images_arr));
            }

            $info_url = 'rest/catalog/productsinformation';
            $product_productsinformation = app(BigBuyApiService::class)->fetchData($info_url);
            $product_info = [];
            foreach ($product_productsinformation as $product) {
                $product_info[$product['id']] = $product;
            }
            if (!empty($product_info) && Redis::connection()) {
                Log::info('Redis get product information');
                Redis::set('bigbuy_product_information', json_encode($product_info));
            }


            $shipping_url = 'rest/shipping/lowest-shipping-costs-by-country/de';
            $product_shippingcosts = app(BigBuyApiService::class)->fetchData($shipping_url);
            $product_costinfo = [];
            foreach ($product_shippingcosts as $shippingcost) {
                $product_costinfo[$shippingcost['reference']] = $shippingcost;
            }
            if (!empty($product_costinfo) && Redis::connection()) {
                Log::info('Redis get product shipping cost');
                Redis::set('bigbuy_product_shippingcosts', json_encode($product_costinfo));
            }

            $category_url = 'rest/catalog/productscategories';
            $all_product_category = app(BigBuyApiService::class)->fetchData($category_url);
            $product_category = [];
            foreach ($all_product_category as $product) {
                $product_category[$product['product']] = $product;
            }
            if (!empty($product_category) && Redis::connection()) {
                Log::info('Redis get product categories');
                Redis::set('bigbuy_product_category', json_encode($product_category));
            }


            // $bigbuy_product_variation = Redis::get('bigbuy_product_veriation');
            // $bigbuy_product_variation = json_decode($bigbuy_product_variation);
            // $bigbuy_product_variation = (array)$bigbuy_product_variation;

            Log::info('Redis get bigbuy all information');
            app(BigBuyApiService::class)->insertProductsToMarketplace($variation_products);
            Log::info('Successfully Updated BigBuy All Products');
        } else {
            Log::info('Failed To BigBuy Product Update');
        }
    }

    public function orderBigBuyProduct($drmOrder, $customerInfo)
    {

        $customerInfo = json_decode($customerInfo);
        $products = array();
        $order = json_decode($drmOrder);
        // dd($order);
        // $orderCustomerInfo = json_decode($order->customer_info);

        // $carriers = DB::table('user_parcel_services')
        //            ->select('parcel_name')
        //            ->where('id', $order->parcel_id)->get();
        // $carriers_name = strtolower($carriers[0]->parcel_name);

        foreach (json_decode($drmOrder->cart) as $cart) {

            if (isset($cart->marketplace_product_id)) {

                $marketplace_product_info = Product::where([
                    'id' => $cart->marketplace_product_id,
                    'api_id' => \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID
                ])->first();

                if (isset($marketplace_product_info->api_product_id)) {
                    $carriers_name = strtolower($marketplace_product_info->misc);
                    $products[] = [
                        'reference' => $marketplace_product_info->item_number,
                        'quantity' => $cart->qty,
                        'internalReference' => $cart->item_number
                    ];
                }
            }
        }

        if (isset($order->id) && count($products) > 0) {
            $productInfo = array(
                'order' => [
                    // 'internalReference' => $order->marketplace_order_ref.'re',
                    'internalReference' => $order->id ?? '',
                    'language' => "de",
                    'paymentMethod' =>  $order->payment_type ?? 'moneybox',
                    "carriers" => [
                        [
                            "name" => "postal service"
                        ],
                        [
                            "name" => $carriers_name
                        ]
                    ],
                    'shippingAddress' => [
                        'firstName'     => $customerInfo->first_name ?? '',
                        'lastName'      => $customerInfo->last_name ?? '',
                        'country'       => $customerInfo->country ?? '',
                        'postcode'      => $customerInfo->zipcode,
                        'town'          => $customerInfo->city ?? '',
                        'address'       => $customerInfo->address ?? '',
                        'phone'         => $customerInfo->phone,
                        'email'         => $customerInfo->email ?? '',
                        'comment'       => "",
                        //   'firstName' => 'Jennifer Black',
                        //   'lastName'=> 'Forest',
                        //   'country'=> 'GB',
                        //   //'country'=> $customerInfo->country ?? '',
                        //   'postcode' => '78126',
                        //   'town' => 'Königsfeld In the Black Forest',
                        //   'address' => $orderCustomerInfo->address ?? '',
                        //   'phone' => "664869570",
                        //   'email' => $customerInfo->email ?? '',
                        //   'comment' => ""
                    ],
                    'products'         => $products ?? '',
                ]
            );

            $data = json_encode($productInfo);

            if (!isset($_GET['send']))
                dd($data);


            $url = 'rest/order/create';
            $response = app(BigBuyApiService::class)->fetchData($url, 'POST', 'de', $data);

            if (isset($response)) {
                $orderAttributes = [
                    'drm_order_id'  => $order->id ?? 0,
                    'api_id'        => \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID,
                    'order_id'      => $response['order_id'] ?? 0,
                    'invoice_number' => $order->invoice_number ?? 0,
                    'shipping_cost' => $order->shipping_cost ?? 0,
                    'total'         => $order->total ?? 0,
                    'customer_infos' => [
                        'name'     => $customerInfo->first_name . $customerInfo->last_name ?? '',
                        'street'   => $customerInfo->street ?? '',
                        'houseno'  => $customerInfo->house_no ?? '',
                        'postcode' => $customerInfo->zipcode ?? '',
                        'city'     => $customerInfo->city ?? '',
                        'country'  => $customerInfo->country ?? '',
                    ],
                    'product_infos' => $products ?? '',
                    'misc'          => $response ?? '',
                    'status'        => 1,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order->id,
                    'message'       => 'Order placed to marketplace API Successfully!',
                    'parcel_number' => '',
                    'parcel_service' => '',
                    'status'        => 'transfer',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(ProductService::class)->sendOrderTrackingToDRM($drm_data);
            } else {
                Log::info("BigBuy Order Error");
            }
        }
    }

    public function checkTotalProduct()
    {
        $productBrand = collect(json_decode(Redis::get('bigbuy_product_brand'), true));
        dd($productBrand);
    }

    public function categoryMappingWithMpProduct()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        if (isset($_GET['api_id']) && !empty($_GET['api_id'])) {
            $api_id = $_GET['api_id'];

            $mapped_categories = DB::table('api_category_mapping')->where('api_id', $api_id)
                ->where('is_complete', 1)
                ->select('api_category_id', 'mp_category_id')
                ->get()->groupBy('mp_category_id')->toArray();

            $category_array = [];
            foreach ($mapped_categories as $mapped_category) {
                if (!in_array($category_array, $mapped_category)) {
                    $category_array[$mapped_category[0]->mp_category_id] = array_column($mapped_category, 'api_category_id');
                }
            }

            foreach ($category_array as $key => $value) {

                Product::where('api_id', $api_id)->whereIn('api_category_id', $value)->where('category_id', '!=', $key)->update(['category_id' => $key]);
            }
        } else {
            dd('api_id not found');
        }
    }

    public function sendOrder($products, $customer_info, $order_info)
    {
        sleep(1);
        $exist_order_check = DrmOrder::where('id', $order_info['order_id'])->whereNotNull('mp_api_id')->exists();
        if($exist_order_check) return true;

        $required_field = ["first_name", "last_name", "zipcode", "city", "country", "address"];
        $missing        = [];
        foreach ($customer_info as $key => $customerInfo) {
            if (in_array($key, $required_field) &&  empty($customerInfo)) {
                $missing[] = $key;
            }
        }

        if (!empty($missing)) {
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, BIGBUY API required data ' . implode(" ,", $missing) . ' is missing',
                'parcel_number' => '',
                'parcel_service' => '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'], 422);
        }

        $products_array = [];

        foreach ($products as $product) {

            $carriers_name = strtolower($product['misc']);
            $products_array[] = [
                'reference'         => $product['item_number'],
                'quantity'          => $product['qty'],
                'internalReference' => $product['item_number']
            ];
        }
                        
        $carriers_name_for_order = [
            [ "name" => $carriers_name ],
            [ "name" => "postal service" ],
            [ "name" => "Dachser" ],
            [ "name" => "Smart Shipment" ],
            [ "name" => "Standard Shipment" ],
            [ "name" => "SEUR" ],
            [ "name" => "TNT" ],
            [ "name" => "DHL Freight" ],
            [ "name" => "DB Schenker" ],
        ];

        if(DrmOrder::where('id', $order_info['marketplace_order_ref'])->where('insert_type', 6)->exists()){
            
            $drm_product_carrer = DrmProduct::whereIn('drm_products.id', array_column($products->toArray(), 'drm_product_id'))
                        ->join('drm_shipping_methods', 'drm_products.shipping_method_id', '=', 'drm_shipping_methods.id')
                        ->whereNotNull('shipping_method_id')
                        ->where('shipping_method_id','<>', 2)
                        ->select('drm_shipping_methods.shipping_method')
                        ->first()
                        ->shipping_method ?? null;
                        
            if(isset($drm_product_carrer)){
                $carriers_name_for_order = [[ "name" => $drm_product_carrer]];
            }
        }

        $orderShippingAddress = $this->getOrderShippingAddress($customer_info, $order_info);

        if (count($products_array) > 0) {
            $productInfo = array(
                'order' => [
                    'internalReference' => (string)$order_info['order_id'] ?? '',
                    'language'          => "de",
                    'paymentMethod'     =>  $order_info['payment_type'] ?? 'moneybox',
                    "carriers"          =>  $carriers_name_for_order ?? '',
                    'shippingAddress'   => $orderShippingAddress,
                    'products'          => $products_array ?? '',
                ]
            );

            if (!isLocal()) {
                $url      = 'rest/order/create';
                $response = app(BigBuyApiService::class)->newBuildRequest($url, 'POST', 'de', json_encode($productInfo), $order_info['cms_user_id']);

                // if money not available payment method
                if(isset($response) && $response['status_code'] != 201 && ($response['code'] == 'ER009' || $response['code'] == 'ER005')) {

                    $productInfo['order']['paymentMethod'] = 'paypal';
                    $response = app(BigBuyApiService::class)->newBuildRequest($url, 'POST', 'de', json_encode($productInfo), $order_info['cms_user_id']);
                }
                
                // neet to optimization
                if (isset($response) && $response['status_code'] == 201) {
                    app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], ($response['order_id'] ?? 0));
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order placed to marketplace API Successfully!',
                        'parcel_number' => '',
                        'parcel_service' => '',
                        'status'        => 'transfer',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $orderAttributes = [
                        'drm_order_id'  => $order_info['order_id'] ?? 0,
                        'api_id'        => \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID,
                        'order_id'      => $response['order_id'] ?? 0,
                        'invoice_number' => $order_info['invoice_number'] ?? 0,
                        'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                        'total'         => $order_info['total'] ?? 0,
                        'customer_infos' => $orderShippingAddress,
                        'product_infos' => $products_array ?? '',
                        'misc'          => $response ?? '',
                        'status'        => 1,
                        'order_date'    => \Carbon\Carbon::now(),
                    ];

                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    if(isset($customer_info['country']) && (strtoupper($customer_info['country']) == 'CH' || strtoupper($customer_info['country']) == 'CHE' || strtoupper($customer_info['country']) == 'SCHWEIZ' || strtoupper($customer_info['country']) == 'SWITZERLAND')){
                        app(\App\Http\Controllers\Marketplace\SwissPostController::class)->sendOrder($products, $customer_info, $order_info);
                    }
                    
                    return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'], 200);
                } else if (isset($response) && $response['status_code'] != 201) {

                    $orderAttributes = [
                        'drm_order_id'  => $order_info['order_id'] ?? 0,
                        'api_id'        => \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID,
                        'order_id'      => $response['order_id'] ?? 0,
                        'invoice_number' => $order_info['invoice_number'] ?? 0,
                        'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                        'total'         => $order_info['total'] ?? 0,
                        'customer_infos' => $orderShippingAddress,
                        'product_infos' => $products_array ?? '',
                        'misc'          => $response ?? '',
                        'status'        => 2,
                        'order_date'    => \Carbon\Carbon::now(),
                    ];
                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    if ($response['code'] == 'ER003') {
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API Products have no stock  - ' . $product['ean'] ?? '',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'error_level'   => 'STOCK',
                            'status'        => 'exception',
                            'date'          => Carbon::now()
                        ];
                    }else if ($response['code'] == 'ER009') {
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API You do not have enough funds in your Money Box for this payment method. - ' . $product['ean'] ?? '',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'status'        => 'exception_delivery',
                            'date'          => Carbon::now()
                        ];
                    }else if ($response['status_code'] == 404) {
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API No carriers/products has been found. - ' . $product['ean'] ?? '',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'status'        => 'exception_delivery',
                            'date'          => Carbon::now()
                        ];
                    }else if($response['status_code'] == 400){

                        if (isset($response['errors']['firstName'])) {
                            $error_mess = "First name " . $response['errors']['firstName']['errors'][0];
                        } else if (isset($response['errors']['lastName'])) {
                            $error_mess = "Last name " . $response['errors']['lastName']['errors'][0];
                        } else if (isset($response['errors']['country'])) {
                            $error_mess = "Country name " . $response['errors']['country']['errors'][0];
                        } else if (isset($response['errors']['companyName'])) {
                            $error_mess = "Company name " . $response['errors']['companyName']['errors'][0];
                        } else if (isset($response['errors']['address'])) {
                            $error_mess = "Address " . $response['errors']['address']['errors'][0];
                        } else {
                            $error_mess = '';
                        }

                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API ' . $error_mess ?? '' . ' - ' . $product['ean'] ?? '',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'status'        => 'exception_delivery',
                            'date'          => Carbon::now()
                        ];

                    }else if ($response['code'] == 'ER001') {
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API Products have no stock  - ' . $product['ean'] ?? '',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'error_level'   => 'STOCK',
                            'status'        => 'exception',
                            'date'          => Carbon::now()
                        ];
                    } else if ($response['code'] == 'ER008') {
                        return true;
                    } else {
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BIGBUY API',
                            'parcel_number' => '',
                            'parcel_service' => '',
                            'status'        => 'exception_delivery',
                            'date'          => Carbon::now()
                        ];
                    }

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    return response()->json(['status' => 'error', 'message' => 'Order Transfer Failed'], 200);
                }
            }
        }
    }

    private function getOrderShippingAddress($customer_info)
    {
        if(isset($customer_info['country']) && (strtoupper($customer_info['country']) == 'CH' || strtoupper($customer_info['country']) == 'CHE' || strtoupper($customer_info['country']) == 'SCHWEIZ' || strtoupper($customer_info['country']) == 'SWITZERLAND')){
            return [
                'firstName'     => 'MyCargoGate',
                'lastName'      => 'Germany GmbH',
                'country'       => 'de',
                'postcode'      => '79618',
                'town'          => 'Rheinfelden',
                'address'       => 'Earl-H.-Wood Strasse 8',
                'phone'         => '49030303662236',
                'email'         => '<EMAIL>',
                'companyName'   => 'SmartGateFlex',
                'comment'       => "",
            ];
        }else{
            return [
                'firstName'     => $customer_info['first_name'] ?? '',
                'lastName'      => $customer_info['last_name'] ?? '',
                'country'       => $customer_info['country'] ?? '',
                'postcode'      => $customer_info['zipcode'],
                'town'          => $customer_info['city'] ?? '',
                'address'       => $customer_info['address'] ?? '',
                'phone'         => !blank($customer_info['phone']) && strlen($customer_info['phone']) > 3 ? $customer_info['phone'] : '49030303662236',
                'email'         => $customer_info['email'] ?? '',
                'companyName'   => $customer_info['company'] ?? '',
                'comment'       => "",
            ];
        }
    }

    public function syncTracking()
    {

        $order_id = $_GET['order_id'];
        if (isset($order_id) && !empty($order_id)) {
            $status_array = ['ORDER_COMPLETED' => 'shipped', 'ORDER_CANCELLED' => 'cancel'];

            $url = 'https://drm.software/testcquxebwnxnllvem2wtv/farhad/check-internel-order-status?order_id=' . $order_id;
            $internel_order = file_get_contents($url);
            $internel_order = json_decode($internel_order, true);

            if ($order_id == "*********") {
                $orderId = substr($internel_order['Order']['OrderID'], 4);
            } else {
                $orderId = substr($internel_order['Order']['OrderID'], 3);
            }

            $order_status = end($internel_order['Order']['OrderStatus']);

            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $orderId,
                'message'       => '',
                'parcel_number' => $internel_order['Order']['TrackingNumber'] ?? '',
                'parcel_service' => $internel_order['Order']['ShipmentProvider'] ?? '',
                'status'        => $status_array[$order_status['Status']]  ?? '',
                'date'          => $order_status['Date']
            ];
            $drm_data = json_encode($drm_data);

            if (!isset($_GET['send']))
                dd(json_decode($drm_data));

            $res = app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

            dd($res);
        } else {
            dd('order_id not found');
        }
    }

    public function updateProductShippingCost()
    {
        $startTime                  = microtime(true);
        $shipping_url               = 'rest/shipping/lowest-shipping-costs-by-country/de';
        $api_id                     = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        $api_product_shippingcosts  = app(BigBuyApiService::class)->fetchData($shipping_url);
        $calculation                = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        $local_category_im          = $this->getCategoryIdWithIMHandel();
        $enterpriceOrTrialUserList = app(\App\Services\Marketplace\ProductService::class)->enterpriceOrTrialUserList() ?? [];
        Log::info('bigbuy shipping cost load......');
        if (count($api_product_shippingcosts) > 1) {

            $api_shipping_array = [];
            foreach ($api_product_shippingcosts as $api_product_shippingcost) {
                if (!array_key_exists($api_product_shippingcost['reference'], $api_shipping_array) && isset($api_product_shippingcost['cost'])) {

                    $api_shipping_array[$api_product_shippingcost['reference']] = number_format($api_product_shippingcost['cost'], 2, '.', '')  ?? 0.00;
                    // $shipping_cost_new = ($api_product_shippingcost['cost'] > 0) ? $api_product_shippingcost['cost']: 5.20;
                    // if ("DACHSER" == strtoupper($api_product_shippingcost['carrierName']) || "PALLET DELIVERY" == strtoupper($api_product_shippingcost['carrierName'])) {
                    //     $shipping_cost_new = $shipping_cost_new + (($shipping_cost_new * 25) / 100);
                    // }
                    // $api_shipping_array[$api_product_shippingcost['reference']] = number_format(($shipping_cost_new * 1.10), 2, '.', '');
                }
            }

            $local_product      = Product::where('api_id', $api_id)->where('country_id', 1)->pluck('real_shipping_cost', 'item_number')->toArray();
            $new_shipping_costs = array_diff_assoc($api_shipping_array, $local_product);
            
            $array_item_number      = [];
            foreach ($new_shipping_costs as $item_number => $shipping_cost) {
                if (array_key_exists($item_number, $local_product)) {
                    $array_item_number[] = $item_number;
                }
            }
           
            info('Bigbuy shipping cost Product Found...................' . count($array_item_number));
            $count = 0;
            foreach (array_chunk($array_item_number, 3000) as $i_number) {
                $local_products = Product::with('drmProducts')
                    ->select('id','ean','uvp','api_id','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','im_handel','category_id','item_number','shipping_cost','update_status','real_shipping_cost','misc')
                    ->where('api_id', $api_id)
                    ->where('country_id', 1)
                    ->whereIn('item_number', $i_number)
                    ->get();

                foreach ($local_products as $product) {
                    if (isset($new_shipping_costs[$product->item_number]) && $product->real_shipping_cost !=  $new_shipping_costs[$product->item_number]) {
                        $update_status          = $product->update_status;

                        if (isset($update_status['real_shipping_cost']) && $update_status['real_shipping_cost'] == 0) {
                            info("Bigbuy shipping status is 0 -" . $product->item_number);
                        } else {
                            
                            // $bigbuy_new_shipping_cost = $new_shipping_costs[$product->item_number];
                            // if($bigbuy_new_shipping_cost > 35){
                            app(\App\Services\Marketplace\ProductService::class)
                                ->mpNewPriceCalculation($product,$product->ek_price,$calculation,$local_category_im,$new_shipping_costs[$product->item_number],false,$enterpriceOrTrialUserList);
                               
                            // }else{
                            //     $product->shipping_cost      = $bigbuy_new_shipping_cost;
                            //     $product->real_shipping_cost = $bigbuy_new_shipping_cost;
                            //     $drm_products                = $product->drmProducts;

                            //     if (count($drm_products) > 0) {
                            //         $data['shipping_cost'] = round($bigbuy_new_shipping_cost, 2);
                            //         app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                            //         info("Bigbuy shipping DRM Price sync-" . $product->item_number);
                            //     }
                            //     $product->update();
                            //     info("Bigbuy shipping cost sync-" . $product->item_number);
                            // }
                        }

                        $count++;
                    } else {
                        info("Bigbuy shipping not update-" . $product->item_number);
                    }
                    info("Bigbuy shipping cost update-" . $count);
                }
            }

            $ex_time =  (microtime(true) - $startTime) . " seconds";

            info($ex_time . "</br>" . "Bigbuy shipping cost fixed on: " . $count . " Products");
        } else {
            info('Bigbuy shipping cost Product Not Found...................');
        }
    }

    public function getApiTracking($api_order_response)
    {

        info("Bigbuy tracking start..........");
        $carriers_url    = 'rest/tracking/carriers';
        $all_carriers    = app(BigBuyApiService::class)->fetchData($carriers_url);
        $carrriers_array = collect($all_carriers)->pluck('name', 'id')->toArray();

        foreach ($api_order_response as $key => $order) {

            if ($key > 0) {
                sleep(1);
            }

            if ($order->order_id > 0) {

                $cms_user_id = DrmOrder::where('id', $order->drm_order_id)->value('cms_user_id');
                $url            = 'rest/tracking/order/' . $order->order_id;
                $tracking_codes = app(BigBuyApiService::class)->fetchData($url, 'GET', 'de', [], $cms_user_id);

                if (isset($tracking_codes['code']) && $tracking_codes['code'] == 404) continue;

                foreach ($tracking_codes as $tracking_code) {
                    if (count($tracking_code['trackings']) > 0) {
                        foreach ($tracking_code['trackings'] as $tracking) {
                            if (array_key_exists($tracking['carrier']['id'], $carrriers_array) && !empty($tracking['trackingNumber'])) {

                                if ($tracking['carrier']['id'] == "26") {
                                    $carrier_name = "Hermes";
                                } else {
                                    $carrier_name = $carrriers_array[$tracking['carrier']['id']] ?? '';
                                }


                                $drm_data = [
                                    'token'         => "Zd6tQv8Cvd",
                                    'order_id'      => $order->drm_order_id,
                                    'message'       => 'order shipped',
                                    'parcel_number' => $tracking['trackingNumber'] ?? '',
                                    'parcel_service' => $carrier_name ?? '',
                                    'status'        => 'shipped',
                                    'date'          => Carbon::now()
                                ];

                                $drm_data = json_encode($drm_data);
                                app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                                $order->tracking_codes = $tracking['trackingNumber'];
                                $order->status = 3;
                                $order->update();
                            }
                        }
                    }
                }
            }
        }
        info('bigbuy tracking done........');
    }

    /**
     * Get Bigbuy Product Information for insert new products
     *
     */
    public function bigbuyProductInsertNew()
    {
        return;
        $api_manufacture      = $this->getApiManufacture();
        $product_stock_array  = $this->getParentProductStock();
        $images_arr           = $this->getApiImage();
        $product_info         = $this->getApiProductInformation();
        $api_product_costinfo = $this->getApiShippingCost();
        $api_product_category = $this->getApiCategory();
        $local_category_im   = $this->getCategoryIdWithIMHandel();

        $variation_url = 'rest/catalog/products';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        if (blank($variation_products)) dd('Bigbuy Product Not Found...................');

        if (
            count($variation_products) > 0
            && count($product_stock_array) > 0
            && count($images_arr) > 0
            && count($product_info) > 0
            && count($api_product_costinfo) > 0
            && count($api_product_category) > 0
            && count($api_manufacture) > 0
        ) {
            $this->insertProductNew(
                $variation_products,
                $product_stock_array,
                $images_arr,
                $product_info,
                $api_product_costinfo,
                $api_product_category,
                $api_manufacture,
                $local_category_im
            );
        } else {
            dd("api data not found");
        }
    }

    /**
     * BigBuy Product Insert New method
     * @required $api_products, $api_product_stock_array, $api_image_array, $api_product_info, $api_product_costinfo, $api_product_category, $api_product_brands
     */
    private function insertProductNew(
        $api_products,
        $api_product_stock_array,
        $api_image_array,
        $api_product_info,
        $api_product_costinfo,
        $api_product_category,
        $api_product_brands,
        $local_category_im
    ) {

        $api_id            = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        $deliveryCompanyId = \App\Enums\Marketplace\ApiResources::BIGBUY_DELIVERY_COMPANY_ID;
        $shippingMethod    = \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING;

        $maping_categories  = DB::table('api_category_mapping')
            ->where('api_id', $api_id)
            ->where('is_complete', 1)
            ->select('api_category_id', 'mp_category_id')
            ->get();

        $categories = [];
        foreach ($maping_categories as $m_category) {
            $categories[$m_category->api_category_id] = $m_category->mp_category_id;
        }

        $api_brands     = array_unique(array_column($api_product_brands, 'name'));
        // $local_brands   = $this->getBrandInsertAndUpdateForTeamServer($api_brands);
        $local_brands   = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);
        $product_brands = collect($api_product_brands)->pluck('name', 'id')->toArray();

        if (isset($api_products)) {
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
            
            foreach(array_chunk($api_products,1000) as $api_chunk_products){
               
                $exist_product = Product::where('api_id', $api_id)
                                        ->where('country_id', 1)
                                        ->whereIn('ean', array_column($api_chunk_products,'ean13'))
                                        ->select('id', 'api_id', 'ean')
                                        ->get();

                foreach ($api_chunk_products as $product) {

                    if(empty($product['ean13'])) continue;
                    
                    $old_product = $exist_product->where('ean', $product['ean13'])->first();
                    if (isset($old_product)) {
                        continue;
                    }

                    $images_arr        = [];
                    if (isset($product['id']) && array_key_exists($product['id'], $api_image_array)) {
                        foreach ($api_image_array[$product['id']] as $image) {
                            $images_arr[] = $image['url'];
                        }
                    }

                    $product_name = '';
                    $discriptions = '';
                    if (isset($product['id']) && array_key_exists($product['id'], $api_product_info)) {
                        $product_name  = $api_product_info[$product['id']]['name'];
                        $discriptions  = $api_product_info[$product['id']]['description'];
                    }

                    $product_stock          = 0;
                    $product_handling_day   = 2;
                    if (isset($product['id']) && array_key_exists($product['id'], $api_product_stock_array)) {
                        $product_stock        = $api_product_stock_array[$product['id']]['quantity'] ?? 0;
                        $product_handling_day = $api_product_stock_array[$product['id']]['maxHandlingDays'] ?? 2;
                    }

                    if (isset($product['sku']) && array_key_exists($product['sku'], $api_product_costinfo)) {
                        $product_shipping_cost = $api_product_costinfo[$product['sku']]['cost'] ?? 0.00;
                        $product_carrier_name = $api_product_costinfo[$product['sku']]['carrierName'] ?? 'GLS';
                    } else {
                        $product_shipping_cost = 0.00;
                        $product_carrier_name  = 'GLS';
                    }

                    $product_brand = 0;
                    if (isset($product['manufacturer']) && isset($product_brands[$product['manufacturer']])) {
                        $product_brand = $local_brands[strtoupper($product_brands[$product['manufacturer']])] ?? 0;
                    }

                    if ($product['condition'] == 'NEW' && !empty($product['ean13']) && !empty($product_name) && !empty($images_arr) && $product_stock > 0) {

                        $ean                = $product['ean13'];
                        $api_product_id     = $product['id'];
                        $item_number        = $product['sku'];
                        $ek_price           = $product['wholesalePrice'];
                        $uvp                = $product['retailPrice'] + ($product['retailPrice'] * 0.10);
                        $vat                = $product['taxRate'];
                        $tax_type           = $product['taxId'];

                        $new_shipping_with_percentage = $product_shipping_cost > 0 ? $product_shipping_cost * 1.10 : 5.20;

                        if($new_shipping_with_percentage > 35){
                            $vkPrice  = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, 35);
                            $vkPrice += $new_shipping_with_percentage - 35;
                            $new_shipping_with_percentage = 35;
                        }else{
                            $vkPrice            = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, $new_shipping_with_percentage);
                        }

                        
                        $product_category   = 68;
                        $product_api_category_id ='';
                        if (array_key_exists($product['id'], $api_product_category)) {
                            $product_category = !empty($categories[$api_product_category[$product['id']]]) ? $categories[$api_product_category[$product['id']]] : 68;
                            $product_api_category_id = $api_product_category[$product['id']] ?? '';
                        }
                        $im_handel = 0;
                        if(isset($local_category_im[$product_category]) && $local_category_im[$product_category] > 0){
                            $im_handel =  $vkPrice * (1 + $local_category_im[$product_category] / 100) ?? 0;
                        }

                        $status_set_attributes = [
                            'item_number'   => $item_number, 
                            'brand'         => $product_brand, 
                            'ek_price'      => $ek_price, 
                            'uvp'           => $uvp, 
                            'delivery_days' => $product_handling_day, 
                            'vat'           => $vat
                        ];

                        $attributes = [
                            'api_id'             => $api_id,
                            'api_product_id'     => $api_product_id,
                            'name'               => $product_name ?? '',
                            'brand'              => $product_brand ?? '',
                            'ean'                => $ean ?? '',
                            'ek_price'           => $ek_price ?? '',
                            'vk_price'           => $vkPrice ?? '',
                            'uvp'                => $uvp ?? '',
                            'description'        => $discriptions ?? '',
                            'category_id'        => $product_category ?? '',
                            'api_category_id'    => $product_api_category_id ?? '',
                            'image'              => $images_arr ?? '',
                            'item_color'         => '',
                            'status'             => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name, $discriptions, $images_arr, $product_stock, $status_set_attributes),
                            'item_number'        => $item_number ?? '',
                            'shipping_method'    => $shippingMethod,
                            'shipping_cost'      => $new_shipping_with_percentage,
                            'real_shipping_cost' => $product_shipping_cost,
                            'stock'              => $product_stock ?? 0,
                            'item_weight'        => '',
                            'item_size'          => '',
                            'production_year'    => '',
                            'materials'          => '',
                            'gender'             => '',
                            'vat'                => $vat,
                            'is_top_product'     => 1,
                            'delivery_days'      => isset($product_handling_day) ? ($product_handling_day + 1) : 2,
                            'collection_id'      => 0,
                            'internel_stock'     => 0,
                            'delivery_company_id' => $deliveryCompanyId,
                            'misc'               => $product_carrier_name,
                            'tax_type'           => $tax_type,
                            'im_handel'          => $im_handel ?? 0,
                        ];

                        Product::create($attributes);
                        
                        Log::info("BigBuy Inserted Ean- " . $ean);
                        
                    }
                    // break;
                }
                Log::info("BigBuy All New Products Update successfully");
            }
        }
    }

    /**
     * Get Bigbuy parent product stock Information
     *
     * @return array
     */
    public function getParentProductStock()
    {
        $stock_url = 'rest/catalog/productsstockbyhandlingdays';
        $parent_product_stocks = app(BigBuyApiService::class)->fetchData($stock_url);

        if (blank($parent_product_stocks)) dd('Bigbuy parent Product Stock Not Found...................');

        $new_stock_arr = [];

        foreach ($parent_product_stocks as $parent_product_stock) {
            $stocks = collect($parent_product_stock['stocks']);
            
            $maxStock = $stocks->max('quantity');
            
            $new_stock_coll = $stocks->where('quantity', $maxStock)->first();

            $new_stock_arr[$parent_product_stock['id']] = [
                "maxHandlingDays" => $new_stock_coll['maxHandlingDays'],
                "quantity"        => $new_stock_coll['quantity'],
            ];
        }

        $parent_product_stocks = [];
        $maxStock = null;
        $new_stock_coll = null;

        return $new_stock_arr ?? [];
    }

    /**
     * Insert Bigbuy Variant Product
     *
     * @return void
     */
    public function bigbuyVariantProductInsertNew()
    {
        $api_product_atributes         = $this->getApiAttributes();
        $api_product_atribute_groupe   = $this->getApiAttributeGroupe();
        $api_product_variations        = $this->getApiVariation();
        $product_stock_array           = $this->getApiVariationStock();
        $images_arr                    = $this->getApiImage();
        $product_info                  = $this->getApiProductInformation();
        $api_product_costinfo          = $this->getApiShippingCost();
        $api_product_category = $this->getApiCategory();

        $variation_url = 'rest/catalog/productsvariations';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        if (blank($variation_products)) dd('Bigbuy Product Variation Not Found...................');

        $this->insertVariantProductNew(
            $variation_products,
            $product_stock_array,
            $images_arr,
            $product_info,
            $api_product_costinfo,
            $api_product_category,
            $api_product_variations,
            $api_product_atributes,
            $api_product_atribute_groupe,
        );
    }

    private function insertVariantProductNew(
        $api_products,
        $api_product_stock_array,
        $api_image_array,
        $api_product_info,
        $api_product_costinfo,
        $api_product_category,
        $api_product_variations,
        $api_product_atributes,
        $api_product_atribute_groupe
    ) {

        $api_id            = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        $deliveryCompanyId = \App\Enums\Marketplace\ApiResources::BIGBUY_DELIVERY_COMPANY_ID;
        $shippingMethod    = \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING;

        $brand_url = 'rest/catalog/manufacturers';
        $product_brands = app(BigBuyApiService::class)->fetchData($brand_url);

        $parentProductWithBrand = $this->getProductIdWithBrandId();

        $api_brands = array_unique(array_column($product_brands, 'name'));
        $local_brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);
        // $local_brands   = $this->getBrandInsertAndUpdateForTeamServer($api_brands);
        $product_brands = collect($product_brands)->pluck('name', 'id')->toArray();

        if (isset($api_products)) {
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
            $vareant_defaullt_category = DB::table('marketplace_categories')->where('id',2696)->first();

            foreach ($api_products as $product) {

                $images_arr        = [];
                if (isset($product['product']) && array_key_exists($product['product'], $api_image_array)) {
                    foreach ($api_image_array[$product['product']] as $image) {
                        $images_arr[] = $image['url'];
                    }
                }
                $product_name = '';
                $discriptions = '';
                if (isset($product['product']) && array_key_exists($product['product'], $api_product_info)) {
                    $product_name  = $api_product_info[$product['product']]['name'];
                    $discriptions  = $api_product_info[$product['product']]['description'];
                }

                $product_stock = 0;
                $product_handling_day = 0;
                if (isset($product['id']) && array_key_exists($product['id'], $api_product_stock_array)) {
                    $product_stock = $api_product_stock_array[$product['id']]['quantity'] ?? 0;
                    $product_handling_day = $api_product_stock_array[$product['id']]['maxHandlingDays'] ?? 2;
                }

                if (isset($product['sku']) && array_key_exists($product['sku'], $api_product_costinfo)) {
                    $product_shipping_cost = $api_product_costinfo[$product['sku']]['cost'] ?? 0.00;
                    $product_carrier_name = $api_product_costinfo[$product['sku']]['carrierName'] ?? 'GLS';
                } else {
                    $product_shipping_cost = 0.00;
                    $product_carrier_name = 'GLS';
                }

                $product_shipping_cost = $product_shipping_cost * 1.10;
                
                $product_brand = 0;
                $brand_name = '';
                if (isset($product['product']) && isset($parentProductWithBrand[$product['product']])) {
                    $product_brand_id = $parentProductWithBrand[$product['product']];
                    if (isset($product_brand_id) && isset($product_brands[$product_brand_id])) {
                        $brand_name = $product_brands[$product_brand_id];
                        $product_brand = $local_brands[strtoupper($product_brands[$product_brand_id])] ?? '';
                    }
                }

                if (!empty($product['ean13']) && $product_stock > 0) {

                    $ean                = $product['ean13'];
                    $api_product_id     = $product['id'];
                    $item_number        = $product['sku'];
                    $ek_price           = $product['wholesalePrice'];
                    $uvp                = $product['retailPrice'] + ($product['retailPrice'] * 0.10);
                    $vat                = 19;
                    $tax_type           = 1;

                    $new_shipping_with_percentage = $product_shipping_cost > 0 ? $product_shipping_cost * 1.10 : 5.20;

                    if($new_shipping_with_percentage > 35){
                        $vkPrice  = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, 35);
                        $vkPrice += $new_shipping_with_percentage - 35;
                        $new_shipping_with_percentage = 35;
                    }else{
                        $vkPrice = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($ek_price, $calculation, $uvp, $new_shipping_with_percentage);
                    }

                    if (array_key_exists($product['product'], $api_product_category)) {
                        $product_api_category_id = $api_product_category[$product['product']] ?? '';
                    }

                    $old_product = Product::where('api_id', $api_id)
                        ->where('ean', $ean)
                        ->select('id', 'api_id', 'ean')
                        ->first();

                    if (isset($old_product)) {
                        info('bigbuy product already exist' . $old_product->ean);
                    } else {

                        $product_length = $product['depth'] ?? 0.0;
                        $product_width  = $product['width'] ?? 0.0;
                        $product_height = $product['height'] ?? 0.0;
                        $item_weight    = $product['extraWeight'] ?? 0.0;
                        $item_size      = '';
                        $item_color     = '';
                        $gender         = '';

                        if (isset($api_product_id) && array_key_exists($api_product_id, $api_product_variations)) {
                            foreach ($api_product_variations[$api_product_id] as $variation_loo) {
                                if (isset($variation_loo)) {
                                    foreach ($variation_loo as $variation_id) {
                                        if (isset($variation_id) && array_key_exists($variation_id, $api_product_atributes)) {
                                            $variable_name = $this->getAttributesName($api_product_atribute_groupe[$api_product_atributes[$variation_id]['attributeGroup']]);
                                            ${$variable_name} = $api_product_atributes[$variation_id]['name'];
                                        }
                                    }
                                }
                            }
                        }

                        $im_handel = 0;
                        if(isset($vareant_defaullt_category) && $vareant_defaullt_category->im_handel > 0){
                            $im_handel =  $vkPrice * (1 + $vareant_defaullt_category->im_handel / 100) ?? 0;
                        }

                        $product_name = $product_name . ' ' . $item_color . ' ' . $item_size . ' ' . $brand_name;
                        $status_set_attributes = [
                            'item_number'   => $item_number, 
                            'brand'         => $product_brand, 
                            'ek_price'      => $ek_price, 
                            'uvp'           => $uvp, 
                            'delivery_days' => $product_handling_day, 
                            'vat'           => $vat
                        ];

                        $attributes = [
                            'api_id'             => $api_id,
                            'api_product_id'     => $api_product_id,
                            'name'               => $product_name ?? '',
                            'brand'              => $product_brand ?? 0,
                            'ean'                => $ean ?? '',
                            'ek_price'           => $ek_price ?? 0,
                            'vk_price'           => $vkPrice ?? 0,
                            'uvp'                => $uvp ?? 0,
                            'description'        => $discriptions ?? '',
                            'category_id'        => 2696,
                            'api_category_id'    => $product_api_category_id ?? '',
                            'image'              => $images_arr ?? '',
                            'item_color'         => $item_color ?? '',
                            'status'             => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name, $discriptions, $images_arr, $product_stock, $status_set_attributes),
                            'item_number'        => $item_number ?? '',
                            'shipping_method'    => $shippingMethod,
                            'shipping_cost'      => $new_shipping_with_percentage,
                            'real_shipping_cost' => $product_shipping_cost,
                            'stock'              => $product_stock ?? 0,
                            'item_weight'        => $item_weight ?? '',
                            'item_size'          => $item_size ?? '',
                            'production_year'    => '',
                            'materials'          => '',
                            'gender'             => $gender ?? '',
                            'vat'                => $vat,
                            'is_top_product'     => 1,
                            'delivery_days'         => isset($product_handling_day) ? ($product_handling_day + 1) : 2,
                            'collection_id'      => 0,
                            'internel_stock'     => 0,
                            'delivery_company_id' => $deliveryCompanyId,
                            'misc'               => $product_carrier_name,
                            'tax_type'           => $tax_type,
                            'is_varient'         => 1,
                            'im_handel'          => $im_handel ?? 0,
                        ];

                        $product_insert_id = Product::create($attributes);

                        if ($product_insert_id->id) {
                            $volume = (($product_length * $product_width * $product_height) / 1000000);

                            $additionalInfo['product_length'] =  $product_length ?? 0.0;
                            $additionalInfo['product_width']  =  $product_width ?? 0.0;
                            $additionalInfo['product_height'] =  $product_height ?? 0.0;
                            $additionalInfo['item_unit']      =  'Centimeter';
                            $additionalInfo['volume'] = number_format($volume, 6);
                            $additionalInfo['product_id'] =  $product_insert_id->id;
                            $additionalInfo['custom_tariff_number'] = $product['manufacturer'] ?? '';
                            $additionalInfo['manufacturer_id'] = $product['partNumber'] ?? '';

                            DB::table('mp_product_additional_info')->insert($additionalInfo);
                        }

                        info("BigBuy Inserted Ean - " . $ean);
                    }
                }
                // break;
            }
            Log::info("BigBuy All New Products Update successfully");
        }
    }

    private function getAttributesName($attriuteName)
    {
        $attrituteGroup = [];
        $attrituteGroup["Farbe"] = "item_color";
        $attrituteGroup["Größe"] = "item_size";
        $attrituteGroup["Fußgröße"] = "item_size";
        $attrituteGroup["Sex"] = "gender";
        $attrituteGroup["Gewicht"] = "item_weight";
        $attrituteGroup["Länge"] = "product_length";
        return $attrituteGroup[$attriuteName] ?? '';
    }

    private function getApiAttributes()
    {

        $attributes_url = 'rest/catalog/attributes';
        $all_product_attributes = app(BigBuyApiService::class)->fetchData($attributes_url);
        if (blank($all_product_attributes)) dd('Bigbuy Product Attributes Not Found...................');
        $api_product_atributes = [];
        foreach ($all_product_attributes as $attribute) {
            $api_product_atributes[$attribute['id']] = [
                "attributeGroup" => $attribute['attributeGroup'],
                'name'            => $attribute['name'],
            ];
        }
        $all_product_attributes = [];
        return $api_product_atributes;
    }

    private function getApiAttributeGroupe()
    {

        $attribute_groupe_url = 'rest/catalog/attributegroups';
        $all_product_attribute_groupes = app(BigBuyApiService::class)->fetchData($attribute_groupe_url);
        if (blank($all_product_attribute_groupes)) dd('Bigbuy Product attributegroups Not Found...................');
        $api_product_atribute_groupe = [];
        foreach ($all_product_attribute_groupes as $attribute_groupe) {
            $api_product_atribute_groupe[$attribute_groupe['id']] = $attribute_groupe['name'];
        }
        $all_product_attribute_groupes = [];
        return $api_product_atribute_groupe;
    }

    private function getApiVariation()
    {
        $variations = 'rest/catalog/variations';
        $all_product_variations = app(BigBuyApiService::class)->fetchData($variations);
        if (blank($all_product_variations)) dd('Bigbuy Product variations Not Found...................');
        $api_product_variations = [];
        foreach ($all_product_variations as $product) {
            $api_product_variations[$product['id']] = $product['attributes'];
        }
        $all_product_variations = [];

        return $api_product_variations;
    }

    private function getApiVariationStock()
    {
        $variant_stock_url = 'rest/catalog/productsvariationsstockbyhandlingdays';
        $variant_product_stocks = app(BigBuyApiService::class)->fetchData($variant_stock_url);

        if (blank($variant_product_stocks)) dd('Bigbuy parent Product Stock Not Found...................');

        $new_variant_stock_arr = [];

        foreach ($variant_product_stocks as $variant_product_stock) {
            $stocks = collect($variant_product_stock['stocks']);
            
            $maxStock = $stocks->max('quantity');
            
            $new_stock_coll = $stocks->where('quantity', $maxStock)->first();

            $new_variant_stock_arr[$variant_product_stock['id']] = [
                "maxHandlingDays" => $new_stock_coll['maxHandlingDays'],
                "quantity"        => $new_stock_coll['quantity'],
            ];
        }

        $variant_product_stocks = [];
        $maxStock = null;
        $new_stock_coll = null;

        return $new_variant_stock_arr ?? [];

        // $variation_stock_url = 'rest/catalog/productsvariationsstock';
        // $product_variation_stocks = app(BigBuyApiService::class)->fetchData($variation_stock_url);
        // if (blank($product_variation_stocks)) dd('Bigbuy Product Stock Not Found...................');
        // $product_variation_stock_array = [];
        // foreach ($product_variation_stocks as $product_variation_stock) {
        //     $product_variation_stock_array[$product_variation_stock['id']] = $product_variation_stock;
        // }
        // $product_variation_stocks = [];
        // return $product_variation_stock_array;
    }

    private function getApiImage()
    {
        $image_url = 'rest/catalog/productsimages';
        $product_image = app(BigBuyApiService::class)->fetchData($image_url);
        if (blank($product_image)) dd('Bigbuy Product Image Not Found...................');
        $images_arr = [];
        foreach ($product_image as $image) {
            $images_arr[$image['id']] = $image['images'];
        }
        $product_image = [];
        return $images_arr;
    }

    private function getApiProductInformation()
    {
        $info_url = 'rest/catalog/productsinformation';
        $api_productsinformation = app(BigBuyApiService::class)->fetchData($info_url);
        if (blank($api_productsinformation)) dd('Bigbuy Product Information Not Found...................');
        $product_info = [];
        foreach ($api_productsinformation as $product) {
            $product_info[$product['id']] = $product;
        }
        $api_productsinformation = [];
        return $product_info;
    }

    private function getApiShippingCost()
    {
        $shipping_url = 'rest/shipping/lowest-shipping-costs-by-country/de';
        $product_shippingcosts = app(BigBuyApiService::class)->fetchData($shipping_url);
        if (blank($product_shippingcosts)) dd('Bigbuy Product Shipping Cost Not Found...................');
        $api_product_costinfo = [];
        foreach ($product_shippingcosts as $shippingcost) {
            $api_product_costinfo[$shippingcost['reference']] = $shippingcost;
        }
        $product_shippingcosts = [];
        return $api_product_costinfo;
    }

    private function getProductIdWithBrandId()
    {
        $product_url = 'rest/catalog/products';
        $parent_products = app(BigBuyApiService::class)->fetchData($product_url);
        if (blank($parent_products)) dd('Bigbuy parent Product Not Found...................');
        $api_product_costinfo = collect($parent_products)->pluck('manufacturer', 'id')->toArray();
        $parent_products = [];
        return $api_product_costinfo;
    }

    /**
     * get api product manufacture
     */
    private function getApiManufacture()
    {
        $brand_url = 'rest/catalog/manufacturers';
        $product_brands = app(BigBuyApiService::class)->fetchData($brand_url);
        
        if (blank($product_brands)) dd('Bigbuy manufacture Not Found...................');
        return $product_brands ?? [];
    }

    private function getApiCategory()
    {
        $category_url = 'rest/catalog/productscategories';
        $all_product_category = app(BigBuyApiService::class)->fetchData($category_url);
        if (blank($all_product_category)) dd('Bigbuy Product Category Not Found...................');
        $api_product_category = [];
        foreach ($all_product_category as $product) {
            $api_product_category[$product['product']] = $product['category'];
        }
        $all_product_category = [];
        return $api_product_category;
    }

    // brand insert team server for test
    private function getBrandInsertAndUpdateForTeamServer(array $api_brands): array
    {
        if (!blank($api_brands)) {
            $local_brands = DB::connection('drm_team')->table('marketplace_product_brand')
                ->pluck('brand_name')
                ->toArray();

            $api_brand_arr   = array_map('strtoupper', $api_brands);
            $local_brand_arr = array_map('strtoupper', $local_brands);

            $brands = array_diff($api_brand_arr, $local_brand_arr);

            if (count($brands) > 0) {
                $new_brand = [];
                foreach ($brands as $key => $brand) {
                    if (!blank($brand)) {
                        $new_brand[] = [
                            'brand_name' => $brand,
                            'user_id'    => 2455,
                            'brand_logo' => null,
                        ];
                    }
                }
                DB::connection('drm_team')->table('marketplace_product_brand')->insert($new_brand);
            }
        }

        $local_all_brands =  DB::connection('drm_team')->table('marketplace_product_brand')->pluck('id', 'brand_name')->toArray();
        return array_change_key_case($local_all_brands, CASE_UPPER);
    }

    public function bigbuyVarientProductStockSync()
    {
        $product_stocks = $this->getApiVariationStock();

        if (blank($product_stocks)) dd('Bigbuy Product Variation Not Found...................');

        if (isset($product_stocks)) {
            info("bigbuy varient Stock api hit done");
            app(BigBuyApiService::class)->varientProductStockSync($product_stocks);
            Log::info("Finish BigBuy varient Product Stock Sync");
        } else {
            Log::info("Api not hit");
        }
    }

    public function bigbuyVarientProductPriceSync()
    {
        $variation_url      = 'rest/catalog/productsvariations';
        $variation_products = app(BigBuyApiService::class)->fetchData($variation_url);
        if (blank($variation_products)) dd('Bigbuy Product Variation Not Found...................');

        app(BigBuyApiService::class)->variantProductPriceUpdate($variation_products);
        info("Finish BigBuy varient Product price Sync");
    }

    public function checkOrderStatus()
    {

        $api_order_responses = ApiOrdersResponse::select('id','api_id','status','drm_order_id','order_id','tracking_codes','created_at')
                                                ->where('status',1)
                                                ->whereNull('tracking_codes')
                                                ->where('api_id',4)
                                                ->get();

        if (blank($api_order_responses)) dd('Bigbuy Order Not Found...................');

        foreach($api_order_responses as $order){

            $api_res = app(BigBuyApiService::class)->fetchData(('rest/order/'.$order->order_id));

            if(isset($api_res['status']) && $api_res['status'] == 'Cancelado'){
                dd($api_res);
            }
            info('Bigbuy order status check '.  $order->order_id);
            sleep(1);
        }

        dd("done");

    }

    public function updateProductChShippingCost()
    {

        $startTime                  = microtime(true);
        $shipping_url               = 'rest/shipping/lowest-shipping-costs-by-country/ch';
        $api_product_shippingcosts  = app(BigBuyApiService::class)->fetchData($shipping_url);

        Log::info('bigbuy ch shipping cost load......');
        if (count($api_product_shippingcosts) > 1) {

            $api_shipping_array = [];
            foreach ($api_product_shippingcosts as $api_product_shippingcost) {
                if (!array_key_exists($api_product_shippingcost['reference'], $api_shipping_array) && isset($api_product_shippingcost['cost'])) {
                    $shipping_cost_new = ($api_product_shippingcost['cost'] > 0) ? $api_product_shippingcost['cost']: 5.20;
                    
                    if ("DACHSER" == strtoupper($api_product_shippingcost['carrierName']) || "PALLET DELIVERY" == strtoupper($api_product_shippingcost['carrierName'])) {
                        $shipping_cost_new = $shipping_cost_new + (($shipping_cost_new * 25) / 100);
                    }

                    $api_shipping_array[$api_product_shippingcost['reference']] = number_format(($shipping_cost_new * 1.10), 2, '.', '');
                }
            }

            $drm_products = DrmProduct::where('user_id', 3420)->where('marketplace_delivery_company_id', ApiResources::BIGBUY_DELIVERY_COMPANY_ID)->get();

            $local_product = $drm_products->pluck('shipping_cost', 'item_number')->toArray();
            $new_shipping_costs = array_diff_assoc($api_shipping_array, $local_product);
            
            $array_item_number      = [];
            foreach ($new_shipping_costs as $item_number => $shipping_cost) {
                if (array_key_exists($item_number, $local_product)) {
                    $array_item_number[] = $item_number;
                }
            }
            
            $count = 0;
            foreach (array_chunk($array_item_number, 3000) as $i_numbers) {
                $update_products = $drm_products->whereIn('item_number', $i_numbers);
                foreach ($update_products as $drm_product) {
                    if (isset($drm_product) && isset($new_shipping_costs[$drm_product->item_number])) {
                        $update_shipping_cost = round($new_shipping_costs[$drm_product->item_number], 2);
                        if($update_shipping_cost != $drm_product->shipping_cost){
                            $data['ch_shipping_cost'] = $update_shipping_cost;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct(collect([$drm_product]), $data);
                            Log::info("Bigbuy ch shipping DRM Price sync- " . $drm_product->ean);
                            $count++;
                        }
                    }
                }
            }

            $ex_time =  (microtime(true) - $startTime) . " seconds";
            Log::info($ex_time . "</br>" . "Bigbuy ch shipping cost fixed on: " . $count . " Products");
        } else {
            Log::info('Bigbuy ch shipping cost Product Not Found...................');
        }
    }

    private function getCategoryIdWithIMHandel(){
        return DB::table('marketplace_categories')->pluck('im_handel','id')->toArray();
    }


    public function insertApiScalePrice()
    {
        dd("stop");
        $variation_url = 'rest/catalog/productprices.json?includePriceLargeQuantities=true';
        $api_response = app(BigBuyApiService::class)->fetchData($variation_url);

        if (blank($api_response)) {
            dd('Bigbuy Product Not Found...................');
        }

        foreach (array_chunk($api_response, 1000) as $api_chunk_products) {
            $insertData = [];

            $existingRecords = DB::table('mp_api_scaled_prices')
                ->whereIn('api_product_id', array_column($api_chunk_products, 'id'))
                ->get();

            $existingRecordsMap = $existingRecords->mapWithKeys(function ($item) {
                return [$item->api_product_id . '-' . $item->stock => $item];
            });

            foreach ($api_chunk_products as $product) {
                if (empty($product['sku']) || empty($product['id']) || empty($product['priceLargeQuantities'])) {
                    continue;
                }

                foreach ($product['priceLargeQuantities'] as $price) {
                    $uniqueKey = $product['id'] . '-' . $price['quantity'];
                    if(isset($existingRecordsMap[$uniqueKey])) continue;
                    $insertData[] = [
                        'api_id' => 4,
                        'item_number' => $product['sku'],
                        'stock' => (int)$price['quantity'],
                        'price' => $price['price'],
                        'api_product_id' => $product['id'],
                        'api_unique_id' => $price['id'],
                    ];
                }
            }

            if (!empty($insertData)) {
                DB::table('mp_api_scaled_prices')->insert($insertData);
                info("BigBuy Inserted Scaled Price");
            }
        }

        dd("done");
    }

    public function updateNameDescription(){
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $api_id  = \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID;
        info('api product info loade start ---------------------------');
        $api_product_info = $this->getApiProductInformation();
        info('api product info loade end ---------------------------');
        $exist_product = Product::with('drmProducts')
                                ->where('api_id', $api_id)
                                ->select('id', 'api_id','api_product_id', 'ean','name','description')
                                ->offset(0)
					            ->take(1000)
                                ->get();

        foreach($exist_product as $product){
            $product_name = '';
            $discriptions = '';
            if (isset($product->api_product_id) && array_key_exists($product->api_product_id, $api_product_info)) {
                $product_name  = $api_product_info[$product->api_product_id]['name'];
                $discriptions  = $api_product_info[$product->api_product_id]['description'];

                if(!empty($product_name) && $product_name != $product->name){
                    $product->name = $product_name;
                    $product->description = $discriptions;

                    $data = [];
                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['title'] = $product_name;
                        $data['description'] = $discriptions;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        info("Bigbuy drm name and description updated --- ". $product->ean);
                    }
                    $product->update();
                    info("Bigbuy name and description updated --- ". $product->ean);
                }else{
                    info('not updated -----------');
                }
            }else{
                info('not found array');
            }

        }
        
        dd("done");
    }



}
