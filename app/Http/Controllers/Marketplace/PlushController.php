<?php

namespace App\Http\Controllers\Marketplace;

use DateTime;
use Carbon\Carbon;
use App\Models\DrmOrder;
use App\Services\FTPService;
use App\Models\DeliveryCompany;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Models\Export\PlusHOrderCsv;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Marketplace\ApiOrdersResponse;
use App\Models\Marketplace\FulfilmentStockSendLog;

class PlushController extends Controller
{
    private $FtpService;
    public function __construct()
    {
        $isLocal = isLocal();
        $config = [
            'host' => !$isLocal ? env('FTP_HOST') : 'mirdostogir.royalwebhosting.net', // required
            'root' => '/', // required
            'username' => !$isLocal ? env('FTP_USERNAME') : '4244359_mirdostogir', // required
            'password' => !$isLocal ? env('FTP_PASSWORD') : 'iO_z:Lj.7OaBTM/x', // required
            'port' => 21,
        ];
        $this->FtpService = new FTPService($config);
    }
    public function insertProduct()
    {
        $path        = 'Products';
        $fileName    = 'artikel_ek_dropmatix.csv';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter   = ';';
        $product_arr = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);
        $products    = collect($product_arr)->unique('EAN')->toArray();

        $api_brands = array_unique(array_column($products, 'MarkenName'));
        $brands     = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();
        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();
        info('plush product insert start......................');
        $this->priceSync($products, $local_category_im, $calculation);

        foreach (array_chunk($products, 500) as $chumn_products) {
            app(\App\Services\Marketplace\Plush\PlushService::class)->insertProduct($chumn_products, $brands, $local_category_im);
        }

        dd("done");
    }

    public function updateProduct()
    {
        $delivery_company_id = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;

        $path        = 'Products';
        $fileName    = 'ArtikelShopLAGER.csv';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter   = ';';
        $product_arr = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);

        $this->stockSync($product_arr, $delivery_company_id);
    }

    private function stockSync($rows, $delivery_company_id)
    {
        $api_products_ean_stock   = array_map('intval', collect($rows)->pluck('Lager', 'EAN')->toArray());
        $local_products_ean_stock = Product::where('delivery_company_id', $delivery_company_id)->pluck('stock', 'ean')->toArray();
        $new_ean_stock            = array_diff_assoc($api_products_ean_stock, $local_products_ean_stock);

        $array_ean = [];
        foreach ($new_ean_stock as $ean => $stock) {
            if (array_key_exists($ean, $local_products_ean_stock)) {
                $array_ean[] = $ean;
            }
        }

        if (count($array_ean) > 0) {
            foreach (array_chunk($array_ean, 1500) as $ean) {
                $local_mp_products = Product::with('drmProducts')
                    ->select('stock', 'ean', 'id', 'old_stock', 'stock_updated_at', 'delivery_company_id', 'ek_price')
                    ->where('delivery_company_id', $delivery_company_id)
                    ->whereIn('ean', $ean)
                    ->get();

                $salesTrac = [];
                foreach ($local_mp_products as $product) {
                    if ($product->stock !=  $new_ean_stock[$product->ean]) {

                        $old_stock = $product->stock;

                        $product->stock             = $new_ean_stock[$product->ean];
                        $product->old_stock         = $old_stock;
                        $product->stock_updated_at  = \Carbon\Carbon::now();

                        if ($old_stock > $new_ean_stock[$product->ean]) {
                            $discres_stock = $old_stock - $new_ean_stock[$product->ean];
                            $salesTrac[] = [
                                'marketplace_product_id'    => $product->id,
                                'sales_stock'               => $discres_stock,
                                'sales_amount'              => $discres_stock * $product->ek_price,
                                'created_at'                => \Carbon\Carbon::now(),
                            ];
                        }

                        $drm_products = $product->drmProducts;
                        if (count($drm_products) > 0) {
                            $data['stock'] = $new_ean_stock[$product->ean];
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                            info("plush API DRM product sync-" . $product->ean);
                        }

                        $product->update();
                        info("plush API product sync-" . $product->ean);
                    }
                }
                if (count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
            }
        } else {
            info('plush product not foun for stock sync');
        }
    }

    public function sendOrder($products, $customer_info, $order_info)
    {

        if(isset($customer_info['country']) && (strtoupper($customer_info['country']) == 'CH' || strtoupper($customer_info['country']) == 'CHE' || strtoupper($customer_info['country']) == 'SCHWEIZ' || strtoupper($customer_info['country']) == 'SWITZERLAND')){
            app(\App\Http\Controllers\Marketplace\SwissPostController::class)->sendOrder($products, $customer_info, $order_info);
        }
        
        $order = DrmOrder::where('id', $order_info['order_id'])->first();
        collect($order->products)->whereNotNull('mp_supplier_id')
        ->groupBy('mp_supplier_id')
        ->each(function($carts, $supplier_id) use($order) {
            $status = 'order_placed';
            $supplier = DeliveryCompany::find($supplier_id);
            
            if(blank($supplier)) return;
            
            $order->update(['supplier_id' => $supplier_id, 'supplier_time' => \Carbon\Carbon::now(), 'supplier' => 'checked', 'status' => $status, 'mp_api_id' => 0]);

            if ($order->marketplace_order_ref) {
                
                $referencedOrder = DrmOrder::find($order->marketplace_order_ref);

                $data = [
                    'name' => 'Dropmatix Systema SL',
                    'address' => 'C/ HORTS, 33',
                    'zip' => '07200',
                    'state' => 'FELANITX',
                    'country_id' => 8,
                    'contact_name' => 'Dropmatix Systema SL',
                    'note' => 'Marketplace Supplier',
                    'is_marketplace_supplier' => true,
                ];
        
                $supplier_detail = DeliveryCompany::firstOrCreate(
                    [
                        'user_id' => $referencedOrder->cms_user_id,
                        'email' => '<EMAIL>'
                    ],
                    $data
                );

                $referencedOrder->update(['status' => $status, 'supplier_id' => $supplier_detail->id, 'supplier_time' => \Carbon\Carbon::now()]);
            }
            
            return true;
        });


        return true;
        dd("disable this by Patrick");
        foreach ($products as $o_product) {
            $local_p = Product::where('id', $o_product['product_id'])
                ->where('internel_stock', '>=', (int)$o_product['qty'])
                ->select('id', 'internel_stock','delivery_company_id')
                ->first();

            if (blank($local_p)) {
                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, Products have no stock - ' . $o_product['ean'],
                    'parcel_number' => '',
                    'parcel_service' => '',
                    'error_level'   => 'STOCK',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status' => 'error', 'message' => 'Validation error'], 422);
            }
        }

        $required_field = ["first_name", "zipcode", "city", "country", "address", "phone"];
        $missing        = [];
        foreach ($customer_info as $key => $customerInfo) {
            if (in_array($key, $required_field) &&  empty($customerInfo)) {
                $missing[] = $key;
            }
        }

        if (!empty($missing)) {
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, required data ' . implode(" ,", $missing) . ' is missing',
                'parcel_number' => '',
                'parcel_service' => '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'], 422);
        }

        $orderUploadResponse = $this->orderUploadToTheFtp($products, $customer_info, $order_info);

        if ($orderUploadResponse) {

            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order placed to Fulfillment PlusH Successfully!',
                'parcel_number' => '',
                'parcel_service' => '',
                'status'        => 'transfer',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            $order_products = [];
            foreach ($products as $product) {
                $order_products[] = [
                    'product_id'     => $product['product_id'],
                    'qty'            => $product['qty'],
                ];
            }

            $orderAttributes = [
                'drm_order_id'  => $order_info['order_id'],
                'api_id'        => \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID,
                'order_id'      => 0,
                'invoice_number' => $order_info['invoice_number'] ?? 0,
                'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                'total'         => $order_info['total'] ?? 0,
                'customer_infos' => [
                    'name'     => '',
                    'street'   => $customer_info['street'] ?? '',
                    'houseno'  => $customer_info['house_no'] ?? '',
                    'postcode' => $customer_info['zipcode'] ?? '',
                    'city'     => $customer_info['city'] ?? '',
                    'country'  => $customer_info['country'] ?? '',
                ],
                'product_infos' => $order_products ?? [],
                'misc'          => $orderUploadResponse ?? '',
                'status'        => 2,
                'order_date'    => \Carbon\Carbon::now(),
            ];
            \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

            $drm_order_info = DrmOrder::where('id',$order_info['order_id'])->select('id','mp_api_id','supplier_id')->first();
            $drm_order_info->mp_api_id = 0;
            
            if(!empty($local_p->delivery_company_id) && $drm_order_info->supplier_id != $local_p->delivery_company_id){
                $drm_order_info->supplier_id = $local_p->delivery_company_id;
            }
            $drm_order_info->update();

            return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => "order not transfer"], 422);
        }
    }

    private function priceSync($rows, $local_category_im, $calculation)
    {
        $delivery_company_id    = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;
        $api_products_ean_price = collect($rows)->pluck('VKPreis1', 'EAN')->toArray();

        $api_ean_price          = [];
        foreach ($api_products_ean_price as $ean => $price) {
            $api_ean_price[$ean] = removeCommaFromPrice($price);
        }
        $api_ean_price = array_map('floatval', $api_ean_price);

        $local_products_ean_stock = Product::where('delivery_company_id', $delivery_company_id)->pluck('ek_price', 'ean')->toArray();
        $local_products_ean_stock = array_map('floatval', $local_products_ean_stock);
        $new_ean_price            = array_diff_assoc($api_ean_price, $local_products_ean_stock);

        $array_ean    = array_keys(array_intersect_key($local_products_ean_stock, $new_ean_price));

        if (count($array_ean) > 0) {
            foreach (array_chunk($array_ean, 1500) as $ean) {
                $local_mp_products = Product::with('drmProducts')
                    ->select('id', 'ean', 'uvp', 'api_id', 'ek_price', 'old_ek_price', 'ek_price_updated_at', 'vk_price', 'old_vk_price', 'vk_price_updated_at', 'im_handel', 'category_id', 'item_number', 'shipping_cost', 'update_status', 'real_shipping_cost', 'delivery_company_id')
                    ->where('delivery_company_id', $delivery_company_id)
                    ->whereIn('ean', $ean)
                    ->get();

                foreach ($local_mp_products as $product) {
                    $new_ek_price = $new_ean_price[$product->ean];
                    if ($product->ek_price != $new_ek_price) {

                        app(\App\Services\Marketplace\ProductService::class)
                            ->mpNewPriceCalculation($product, $new_ek_price, $calculation, $local_category_im, $product->real_shipping_cost, false);
                        //                         $product->old_ek_price         = $product->ek_price;
                        //                         $product->ek_price             = $new_ek_price;
                        //                         $product->ek_price_updated_at  = \Carbon\Carbon::now();

                        //                         $new_vk_price = app(\App\Http\Controllers\Marketplace\CollectionController::class)->calculatePrice($new_ek_price, $calculation, $product->uvp, $product->shipping_cost);
                        // //                        $new_vk_price = ($new_ek_price + ($new_ek_price * 0.05 ));

                        //                         $product->old_vk_price         = $product->vk_price;
                        //                         $product->vk_price             = $new_vk_price;
                        //                         $product->vk_price_updated_at  = \Carbon\Carbon::now();

                        //                         $drm_products = $product->drmProducts;
                        //                         if(count($drm_products) > 0){
                        //                             $data['vk_price'] = round($new_vk_price,2);
                        //                             app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        //                             info("Plush API DRM product price sync-".$product->ean);
                        //                         }

                        //                         $product->update();
                        //                         info("Plush product price sync - ".$product->ean);
                    }
                }
            }
        } else {
            info('plush product not foun for stock sync');
        }
    }

    public function orderUploadToTheFtp($products, $customer_info, $order_info)
    {

        try {
            $fileName = '50051' . '_' . Carbon::now()->format('dmY_hhmmss') . '.csv';
            $content = Excel::raw(new PlusHOrderCsv($products, $customer_info, $order_info), \Maatwebsite\Excel\Excel::CSV);

            return $this->FtpService->storeFileToFtp('Orders', $fileName, $content);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function plushSendProductUpdate()
    {

        // dd("done");
        $path        = 'Products';
        $fileName    = 'ArtikelShopLAGER_50051.csv'; // need to change
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter   = ';';
        $product_arr = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);

        $csv_ean_stock = collect($product_arr)->pluck('Lager', 'EAN')->toArray();
        $csv_ean_stock = array_map('intval', $csv_ean_stock);
        //dd($csv_ean_stock);
        foreach (array_chunk(array_keys($csv_ean_stock), 1500) as $ean) {

            $local_mp_products = Product::with('drmProducts', 'additionalInfo:product_id,product_length,product_width,product_height')
                ->select('internel_stock', 'ean', 'id', 'old_internel_stock', 'internel_stock_updated_at', 'shipping_method', 'cubic_meters', 'atw', 'marketplace_product_id')
                ->whereIn('ean', $ean)
                ->where('shipping_method', 2)
                ->get();

            foreach ($local_mp_products as $product) {

                if (isset($csv_ean_stock[$product->ean]) && $product->internel_stock !=  $csv_ean_stock[$product->ean]) {

                    $old_stock = $product->internel_stock ? $product->internel_stock : 0;
                    $new_stock = $csv_ean_stock[$product->ean];

                    $product->internel_stock             = $new_stock;
                    $product->old_internel_stock         = $old_stock;
                    $product->internel_stock_updated_at  = \Carbon\Carbon::now();
                    $product->stock                      = 0;
                    $product->ta                         = 0;
                    $product->atw                        = 0;
                    $product->marketplace_product_id     = $product->id;

                    $drm_products = $product->drmProducts;
                    if (count($drm_products) > 0) {
                        $data['stock'] =  $new_stock;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                        info("plush API DRM product sync-" . $product->ean);
                    }
                    if ($product->additionalInfo) {
                        $volume = (($product->additionalInfo->product_length ?? 0) * ($product->additionalInfo->product_width ?? 0) * ($product->additionalInfo->product_height ?? 0)) / 1000000;
                        $newCubicMeter = $volume * $new_stock;
                        $product->cubic_meters = $newCubicMeter;
                    }
                    $product->update();
                    info("plush API product sync-" . $product->ean);
                    $this->fulfilmentStockCalculation($product->id, $csv_ean_stock[$product->ean]);
                }
            }
        }
        // Defect stock sync from this file ArtikelShopLAGER_50051_defekte_Artikel
        $this->plushDefectProductSync();
    }

    public function plushDefectProductSync()
    {
        $path = 'Products';
        $fileName = 'ArtikelShopLAGER_50051_defekte_Artikel.csv';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter = ';';
        $product_arr = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);

        $csv_ean_stock = collect($product_arr)->pluck('Lager', 'EAN')->map(function ($value) {
            return intval($value);
        });

        $defected_ean_Chunks = array_chunk($csv_ean_stock->keys()->toArray(), 1500);
        foreach ($defected_ean_Chunks as $defected_ean_Chunk) {
            $local_mp_products = Product::select('defect_stock', 'ean', 'id', 'internel_stock', 'atw')
                ->whereIn('ean', $defected_ean_Chunk)
                ->where('shipping_method', 2)
                ->get();

            foreach ($local_mp_products as $product) {
                if (isset($csv_ean_stock[$product->ean]) && $csv_ean_stock[$product->ean] > 0) {
                    $product->defect_stock = $csv_ean_stock[$product->ean];
                    if ($product->atw == $csv_ean_stock[$product->ean]) {
                        $product->atw = 0;
                    }

                    $product->update();
                    info("plush product defect stock sync- " . $product->ean);
                }
            }
        }
        dd("end");
    }

    public function plushProductAdditionalInfoUpdate($delivery_company_id, $csvAllProducts)
    {
        dd('this function not work now');
        $csv_products = [];
        foreach ($csvAllProducts as $csvP) {
            $csv_products[$csvP['EAN']] = $csvP;
        }
        ini_set('max_execution_time', '0');
        $i = 0;
        foreach (array_chunk($csvAllProducts, 1500) as $csvProduct) {
            $products = \App\Models\Marketplace\Product::with('additionalInfo', 'drmProducts')->where('delivery_company_id', $delivery_company_id)->whereIn('ean', array_column($csvProduct, 'EAN'))->select('ean', 'id', 'item_weight')->get();
            foreach ($products as $product) {
                try {
                    if (isset($csv_products[$product->ean])) {
                        $csv_match_product = $csv_products[$product->ean];
                        $Verkaufseinheit = explode(" ", $csv_match_product['Verkaufseinheit']);
                        $item_unit = $csv_match_product['Basis'];
                        $item_weight = $Verkaufseinheit[0];
                        // Update the item_weight of $product
                        $product->item_weight = $item_weight;
                        $product->save();
                        if ($product->additionalInfo) {
                            // Update the item_unit column of the related additionalInfo object
                            $product->additionalInfo->item_unit = $item_unit;
                            $product->additionalInfo->save();
                        } else {
                            \App\Models\Marketplace\AdditionalInfo::create(['product_id' => $product->id, 'item_unit' => $item_unit]);
                        }
                        $drm_products = $product->drmProducts;
                        if (count($drm_products) > 0) {
                            $data['item_unit'] = $item_unit;
                            $data['item_weight'] = $item_weight;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                            info("plush API DRM product sync-" . $product->ean);
                        }
                        info("Total Update Product-" . $i++);
                    }
                } catch (\Exception $e) {
                    info(['product not update ' . $e->getMessage(), $product->id . ' ' . $e->getLine()]);
                }
            }
        }
        dd("done");
    }

    public function productBrandUpdate()
    {
        $path        = 'Products';
        $fileName    = 'artikel_ek_dropmatix.csv';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter   = ';';
        $product_arr = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);
        $products    = collect($product_arr)->unique('EAN')->toArray();

        $api_brands = array_unique(array_column($products, 'MarkenName'));

        $brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);

        foreach ($products as $product) {
            $brand_id = $brands[strtoupper($product['MarkenName'])] ?? '';

            if (!blank($brand_id)) {

                $local_products = Product::select('id', 'brand', 'ean')
                    ->where('delivery_company_id', 14398)
                    ->where('ean', $product['EAN'])
                    ->first();

                if ($local_products && $local_products->brand != $brand_id) {
                    $local_products->brand = $brand_id;
                    // dd($local_products->brand);
                    $local_products->update();
                    info("plush API product brand sync-" . $local_products->ean);
                }
            }
        }

        dd($api_brands);
    }

    public function productDescriptionUpdate()
    {  
        $path        = 'Products';
        $fileName    = 'artikel_ek_dropmatix.csv';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
        $delimiter   = ';';
        $rows = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);


        $delivery_company_id    = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;
        $api_products_ean_description = collect($rows)->pluck('Produktbeschreibung', 'EAN')->toArray();

        $local_products_ean_stock = Product::where('delivery_company_id', $delivery_company_id)->pluck('description', 'ean')->toArray();

        $new_ean_des = [];
        foreach ($api_products_ean_description as $ean => $description) {
            if (array_key_exists($ean, $local_products_ean_stock) && $local_products_ean_stock[$ean] != $description) {
                $new_ean_des[$ean] = $description;
            }
        }

        if (count(array_keys($new_ean_des)) > 0) {
            foreach (array_chunk(array_keys($new_ean_des), 1500) as $ean) {
                $local_mp_products = Product::with('drmProducts')
                    ->where('delivery_company_id', $delivery_company_id)
                    ->whereIn('ean', $ean)
                    ->select('id', 'description', 'ean', 'delivery_company_id')
                    ->get();

                foreach ($local_mp_products as $product) {
                    $new_desc = $new_ean_des[$product->ean];

                    if (!blank($new_desc)) {
                        $product->description         = $new_desc;

                        $drm_products = $product->drmProducts;
                        if (count($drm_products) > 0) {
                            $data['description'] = $new_desc;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $data);
                            info("Plush API DRM product description sync-" . $product->ean);
                        }

                        $product->update();
                        info("Plush product description sync - " . $product->ean);
                    }
                }
            }
        } else {
            info('plush product not foun for stock sync');
        }
    }

    public function plushTrackingNumberSync()
    {

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $delivery_company_id    = \App\Enums\Marketplace\ApiResources::PLUSH_DELIVERY_COMPANY_ID;
        $local_datas            = ApiOrdersResponse::where('api_id', $delivery_company_id)->where('status', 2)->select('id', 'drm_order_id', 'api_id', 'status', 'product_infos', 'tracking_codes')->get();

        if (!blank($local_datas)) {
            $date = new DateTime('now');
            $date->modify('last day');
            $path        = 'Tracking';
            $fileName    = '50051_tracking_' . $date->format('dmY') . '.csv';
            // $fileName = '50051_tracking_18052023.csv';
            $delimiter   = ',';
            $fileContent = $this->FtpService->getFileToFtp($path, $fileName);

            if (gettype($fileContent) != 'object') {
                $rows = app(\App\Services\Marketplace\ProductService::class)->generateStringToArray($fileContent, $delimiter);
                $csv_datas = collect($rows)->groupBy('ReferenzNr');
                if (!blank($csv_datas)) {
                    foreach ($csv_datas as $key => $csv_data) {
                        if (!blank($csv_data[0]['Trackingcode'])) {

                            $local_data = $local_datas->where('drm_order_id', $key)->first();
                            if (!blank($local_data)) {

                                foreach ($csv_data as $csv) {

                                    $drm_data = [
                                        'token'         => "Zd6tQv8Cvd",
                                        'order_id'      => $key,
                                        'message'       => 'order shipped',
                                        'parcel_number' => $csv['Trackingcode'] ?? '',
                                        'parcel_service' => 'DHL',
                                        'status'        => 'shipped',
                                        'date'          => Carbon::now()
                                    ];

                                    $drm_data = json_encode($drm_data);
                                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);
                                }
                                foreach ($local_data->product_infos as $product_info) {
                                    app(\App\Services\Marketplace\ProductService::class)->fulfilProductStockDecreaseByIdAndStock($product_info['product_id'], $product_info['qty']);
                                }
                                $local_data->status = 3;
                                $local_data->update();
                            } else {
                                info("local order data not found");
                            }
                        }
                    }
                } else {
                    info("file is empty");
                }
            } else {
                info("file not found");
            }
        } else {
            info("no data found");
        }
    }

    public function fulfilmentStockCalculation($product_id, $recived_stock)
    {
        try {
            $fulfilmentStockSend = FulfilmentStockSendLog::where('product_id', $product_id)->first();
            if ($fulfilmentStockSend == null) return;
            if ($recived_stock != 0) {
                $oldIsLeft  = $fulfilmentStockSend->is_left ?? 0;
                if ($oldIsLeft != 0) {
                    $isLeftStock =  ($oldIsLeft == $recived_stock) ? 0 : ($oldIsLeft - $recived_stock);
                } else {
                    $isLeftStock =  $oldIsLeft + (($fulfilmentStockSend->send_stock ?? 0) - $recived_stock);
                }
            } else {
                $isLeftStock = 0;
            }
            $isLeftStock = $isLeftStock > 0 ? $isLeftStock : null;
            $data['recived_stock'] =  $recived_stock;
            $data['is_left'] = $isLeftStock;
            $fulfilmentStockSend->update($data);
        } catch (\Exception $e) {
            info('!ops stock recived not update' . $e->getMessage());
        }
    }

    // this all function only for test route
    public function plushDuplicateProductsProcess($product, $eans)
    {
        
        if ($product->shipping_method == 1) {
            $this->craeteDuplicateDropshippingProduct($product);
            dump('fulfilment-create');
        } else {
            // $product->old_internel_stock = $product->internel_stock;
            // $product->internel_stock = (int)$eans[$product->ean];
            // $product->internel_stock_updated_at = Carbon::now();
            // $product->save();
        }
    }

    private function craeteDuplicateDropshippingProduct($product)
    {
        $newProduct = new Product; // Create a new instance of the Product model
        $this->setNewProductAttributes($product, $newProduct);
        // Save the new product
        $newProduct->save();
        $this->cloneRelationship($product, $newProduct);
    }

    public function setNewProductAttributes($product, Product $newProduct)
    {
        $attributes = $product->getAttributes();
        // Set new values for specific attributes
        $attributes['id'] = null; // This ensures a new ID is assigned
        $attributes['atw'] = 0;
        $attributes['stock'] = 0;
        $attributes['shipping_method'] = 2;
        $attributes['shipping_cost'] = 5.20;
        $attributes['status'] = 0;
        $attributes['internel_sync_status'] = 0;
        $attributes['internel_send_date'] = Carbon::now();
        // Set the attributes on the new model
        $newProduct->setRawAttributes($attributes);
    }

    public function cloneRelationship($product, Product $newProduct)
    {
        // Clone the "additionalInfo" relationship (one-to-one)
        if ($product->relationLoaded('additionalInfo') && $product->additionalInfo) {
            $newProduct->additionalInfo()->create($product->additionalInfo->toArray());
        }
        // Clone the "stockSendComment" relationship (one-to-one)
        if ($product->relationLoaded('stockSendComment') && $product->stockSendComment) {
            $newProduct->stockSendComment()->create($product->stockSendComment->toArray());
        }
    }
}
