<?php

namespace App\Http\Controllers\Marketplace;

use League\Csv\Reader;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Collection;
use Illuminate\Support\LazyCollection;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Services\Marketplace\ProductService;

class CollectionController extends Controller
{
    public function collectionProductSync(){

        $collection = Collection::where(['source_type'=>2,'sync_status'=>0])
                                    ->where('shipping_method', 1)
                                    ->where('source_url','!=','')
                                    ->where('column_mapping','!=','')
                                    ->where('category_id','>',0)
                                    ->orderBy('id')->first();

        // $collection =   $collection->setConnection('drm_team')->where(['source_type'=>2,'sync_status'=>0])
        //                 ->where('source_url','!=','')
        //                 ->where('column_mapping','!=','')
        //                 ->orderBy('id')
        //                 ->first();

        if(!empty($collection)){
            $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();
            $type = pathinfo($collection->source_url, PATHINFO_EXTENSION);
            if(empty($type)) $type = 'csv'; 
            $rows = $this->csvToArray($collection->source_url, $type, 'auto', false);
            
            if(count($rows) > 0){
                foreach(array_chunk($rows,2500) as $row){
                    // dispatch(new \App\Jobs\Marketplace\CollectionProductSync($collection,$row));
                    $this->productSync($collection,$row,$local_category_im);
                    Log::info("csv sync job create");
                }

                $collection->update(['sync_status'=>1]);
                Log::info("collection cync done");

                $products = Product::where('collection_id',$collection->id)->get();
                // $products = new Product();
                // $products = $products->setConnection('drm_team')->where('collection_id',$collection->id)->get();
                $col_map = $collection->column_mapping;
                $csvData = array();
                foreach($rows as $row){
                    $csvData[$row[$col_map['ean']]] = $row;
                }
                foreach($products as $product){
                    if(!isset($csvData[$product->ean])){
                        $product->update(['stock'=>0]);
                    }
                }

            }else{
                $collection->update(['sync_status'=>1]);
                Log::info("product not found in csv");
            }
        }else{
            // $collections2 = new Collection();
            // $collections2->setConnection('drm_team')->where(['source_type'=>2,'sync_status'=>1])->update(['sync_status'=>0]);
            Log::info("collection not found all sync status 0");
            Collection::where(['source_type'=>2,'sync_status'=>1])->update(['sync_status'=>0]);
        }
    }

    public function productSync($collection,$row,$local_category_im){
        $col_map = $collection->column_mapping;
        $local_product = Product::with('drmProducts')
                                ->where('collection_id',$collection->id)
                                ->whereIn('ean',array_column($row,$col_map['ean']))
                                ->get();

        if(!empty($col_map['brand'])){
           $api_brands = array_unique(array_column($row,$col_map['brand']));
           $brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands); 
        }
        
        if($collection->id == 78){
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',26)->first();
        }else{
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        }
        
        $product_array = [];
        $salesTrac = [];
        foreach($row as $data){
            $old_product = $local_product->where('collection_id',$collection->id)->where('ean',$data[$col_map['ean']])->first();

            $product_all_image = [];
            $product_info      = [];

            if(isset($data[$col_map['ean']])){
                $product_info['ean'] = $data[$col_map['ean']];
            }else{
                continue;
            }

            $shipping_cost_with_calculation = 0.00;

            if(!empty($col_map['shipping_cost']) || !empty($col_map['shipping_cost_manual'])){
                if(!empty($col_map['shipping_cost'])){
                    $product_info['real_shipping_cost'] = floatval($data[$col_map['shipping_cost']]);
                }else if(!empty($col_map['shipping_cost_manual'])){
                    $product_info['real_shipping_cost'] = floatval($col_map['shipping_cost_manual']);
                }

                $shipping_cost_with_calculation = isset($product_info['real_shipping_cost']) && $product_info['real_shipping_cost'] > 0 ? $product_info['real_shipping_cost'] * 1.10 : 0.00;
            }

            if(!empty($col_map['uvp'])){
                if (strpos($data[$col_map['uvp']], '.') !== false) {
                    $product_info['uvp'] = floatval(str_replace(',', '', $data[$col_map['uvp']]));
                }else{
                    $product_info['uvp'] = floatval(removeCommaFromPrice($data[$col_map['uvp']]));
                }
            }else if(!empty($col_map['manual_uvp'])){
                if (strpos($data[$col_map['ek_price']], '.') !== false) {
                    $product_info['uvp'] = $this->uvpPercentangeCalculation(floatval(str_replace(',', '', $data[$col_map['ek_price']])),floatval($col_map['manual_uvp']));
                }else{
                    $product_info['uvp'] = $this->uvpPercentangeCalculation(floatval(removeCommaFromPrice($data[$col_map['ek_price']])),floatval($col_map['manual_uvp']));
                }
            }

            if(!empty($col_map['ek_price'])){
                if (strpos($data[$col_map['ek_price']], '.') !== false) {
                    $product_info['ek_price'] = floatval(str_replace(',', '', $data[$col_map['ek_price']]));
                }else{
                    $product_info['ek_price'] = floatval(removeCommaFromPrice($data[$col_map['ek_price']]));
                }
            }else{
                continue;
            }

            if($shipping_cost_with_calculation > 35){
                $new_vk_price = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($product_info['ek_price'], $calculation, $product_info['uvp'], 35);
                $new_vk_price += $shipping_cost_with_calculation - 35;
                $product_info['vk_price'] = $new_vk_price;
                $product_info['shipping_cost'] = 35;
            }else{
                $new_vk_price = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($product_info['ek_price'], $calculation, $product_info['uvp'], $shipping_cost_with_calculation);
                $product_info['vk_price'] = $new_vk_price;
                $product_info['shipping_cost'] = $shipping_cost_with_calculation;
            }

            if(!empty($old_product)){

                if($collection->id == 86) continue;
                $drm_update_data = [];
                $old_stock = $old_product->stock;
              
                if($old_product->shipping_method == 1 && isset($data[$col_map['stock']]) && $old_product->stock != $data[$col_map['stock']]){
                    $old_product->stock             =  $data[$col_map['stock']] > 0 ? $data[$col_map['stock']] : 0;
                    $old_product->old_stock         =  $old_stock;
                    $old_product->stock_updated_at  = \Carbon\Carbon::now();
                    $drm_update_data['stock']       = $data[$col_map['stock']] > 0 ? $data[$col_map['stock']] : 0;

                    if($old_stock > $data[$col_map['stock']]){
                        $discres_stock = $old_stock - $data[$col_map['stock']];
                        $salesTrac[] = [
                            'marketplace_product_id'    => $old_product->id,
                            'sales_stock'               => $discres_stock,
                            'sales_amount'              => $discres_stock * $old_product->ek_price,
                            'created_at'                => \Carbon\Carbon::now(),
                        ];
                    }
                }

                if($old_product->ek_price != number_format($product_info['ek_price'], 2)  && $product_info['ek_price'] > 0){
                    $old_product->ek_price = $product_info['ek_price'];
                }

                if($old_product->vk_price != number_format($new_vk_price, 2)){
                    $old_product->vk_price = $new_vk_price;
                    $drm_update_data['ek_price'] = $new_vk_price;
                }

                if($old_product->uvp != number_format($product_info['uvp'],2)){
                    $old_product->uvp = $product_info['uvp'];
                    $drm_update_data['uvp'] = $product_info['uvp'];
                }

                if($old_product->real_shipping_cost != number_format($product_info['real_shipping_cost'],2)){
                    $old_product->real_shipping_cost = $product_info['real_shipping_cost'];
                }

                if($old_product->shipping_cost != number_format($shipping_cost_with_calculation,2)){
                    $old_product->shipping_cost = $shipping_cost_with_calculation;
                    $drm_update_data['shipping_cost'] = $shipping_cost_with_calculation;
                }
                $new_im_handel = 0;
                if(isset($local_category_im[$old_product->category_id]) && $local_category_im[$old_product->category_id] > 0){
                    $new_im_handel =  $new_vk_price + (($new_vk_price * $local_category_im[$old_product->category_id]) / 100);
                }

                if($old_product->im_handel != number_format($new_im_handel, 2)){
                    $old_product->im_handel = $new_im_handel;
                    $drm_update_data['im_handel'] = $new_im_handel;
                }

                $drm_products = $old_product->drmProducts;
                if(count($drm_products) > 0 && count($drm_update_data) > 0){
                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$drm_update_data);
                    Log::info("Collection drm Product Sync: ".$old_product->ean);
                }
                $old_product->update();

            }else{

                if(empty($collection->category_id) || $collection->category_id == 0) continue;

                !empty($col_map['name']) ? $product_info['name'] = $data[$col_map['name']]:'';
                !empty($col_map['item_number']) ? $product_info['item_number'] = $data[$col_map['item_number']]:'';
                !empty($col_map['description']) ? $product_info['description'] = $data[$col_map['description']] :'';
                !empty($col_map['item_size']) ? $product_info['item_size'] = $data[$col_map['item_size']] :'';
                !empty($col_map['production_year']) ? $product_info['production_year'] = $data[$col_map['production_year']]:'';
                !empty($col_map['materials']) ? $product_info['materials'] = $data[$col_map['materials']] : '';
                !empty($col_map['tags']) ? $product_info['tags'] = $data[$col_map['tags']] : '';
               
                !empty($col_map['gender']) ? $product_info['gender'] = $data[$col_map['gender']] : '';
                if(!empty($col_map['brand']) && isset($brands)){
                    $product_info['brand'] = $brands[strtoupper($data[$col_map['brand']])] ?? '';
                }

                if(!empty($col_map['image'])){
                    if(is_array($col_map['image'])){
                        foreach($col_map['image'] as $img){
                            $product_all_image[] = $data[$img];
                        }
                    }
                }

                if(!empty($col_map['vat'])){
                    $product_info['vat'] = floatval(removeCommaFromPrice($data[$col_map['vat']]));
                }else{
                    $product_info['vat'] = floatval($col_map['manual_vat']);
                }

                if(!empty($col_map['stock']) && $data[$col_map['stock']] > 0){
                    $product_info['stock'] = $data[$col_map['stock']];
                }else{
                    $product_info['stock'] = 0;
                }

                if(!empty($col_map['delivery_days'])){
                    $product_info['delivery_days'] = intval($data[$col_map['delivery_days']]);
                }else{
                    $product_info['delivery_days'] = intval($col_map['handling_time']);
                }

                if(count($product_all_image) > 0){
                    $iamges =  $this->image_processing($product_all_image,$col_map['image_prefix'],$col_map['image_suffix'],$col_map['image_separator']);
                    $new_images = (!empty($iamges)) ? array_merge([], $iamges) : null;
                    $product_info['image'] = json_encode($new_images);
                }

                if(isset($local_category_im[$collection->category_id]) && $local_category_im[$collection->category_id] > 0){
                    $product_info['im_handel'] =  $product_info['vk_price'] + (($product_info['vk_price'] * $local_category_im[$collection->category_id]) / 100);
                }

                $product_info['collection_id'] = $collection->id;
                $product_info['shipping_method'] = $collection->shipping_method;
                $product_info['supplier_id'] = $collection->supplier_id;
                $product_info['delivery_company_id'] = $collection->delivery_company_id;
                $product_info['category_id'] = $collection->category_id;
                $product_info['country_id'] = $col_map['country_id'] ?? 1;
                $product_info['internel_stock'] = 0;
                $product_info['created_at'] = \Carbon\Carbon::now();;

                if($collection->id == 85){
                    $product_info['delivery_days'] = 7;
                }

                $valid_ean = app(ProductService::class)->validateEAN($product_info['ean']);

                if( $valid_ean == true && isset($product_info['ek_price']) && ($product_info['ek_price'] > 0) && !empty($product_info['name']) && strlen($product_info['name']) < 190 && $product_info['stock'] > 0){

                    $product_info['status']   = app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_info['name'],$product_info['description'],$new_images,$product_info['stock']);
                    
                    // if($collection->id == 86 && $calculation){
                        
                    //     $calculation_vk_price = $this->calculatePrice($product_info['ek_price'], $calculation, $product_info['uvp'], $product_info['shipping_cost']);
                        
                    //     if ($calculation_vk_price > 0) {
                    //         $product_info['vk_price'] = $calculation_vk_price;
                    //         $product_info['status'] = ProductStatus::ACTIVE;
                    //     }
                    // }

                    $product_array[] = $product_info;
                }
            }
        }

        if(count($product_array) > 0){
            Product::insert($product_array );
            Log::info("Inserted Collection Product");
        }else{
            Log::info("Product not found for insert");
        }
        if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);

    }

    public function csvToArray($path,$type,$delimiter,$deleteFile = true){
        ini_set('max_execution_time', '0');
        ini_set('memory_limit',-1);
        $key=null;
        $key_count=0;
        $array = array();
        $rand=Str::random(40);

            file_put_contents($rand . '.' . $type, fopen($path, 'r'));
            $localpath = $rand . '.' . $type;

            if($type =='csv'|| $type == 'txt'){
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if($delimiter!='auto'){
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            }
            else{
                $spreadsheet = IOFactory::load($localpath);
            }
            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
            $collection = LazyCollection::make($spreadsheet);

            if($key==null){
                $key = array_map('trim', $collection->first());
                $key_count=count($key);
            }
            $key = array_map('removeDots',$key);
            $collection = $collection->except(0);
            foreach($collection as $row){
                if(count($row)==$key_count && !containsOnlyNull($row)){
                    $array[] = array_combine($key, $row);
                }
            }

            if(!pathIsUrl($path) && $deleteFile){
                unlink($localpath);
            }else{
                unlink($localpath);
            }


        return $array;
    }

    public function generateArray($path){

        $reader = Reader::createFromString($path);
        $reader->setHeaderOffset(0);
        return $reader->getRecords();
    }

    public function storeInStorage($api_id,$path){

        $csv_string = file_get_contents($path);

        if(empty($csv_string)){
            sleep(1000);
            return app(\App\Http\Controllers\Marketplace\ProductController::class)->bikeApiStockUpdateFromCsvData();
        }

        $csv_array = 'api_stocks/bikeapi/'. Carbon::now()->toDateString() .'/'.Carbon::now()->toTimeString().'.csv';

        Storage::disk('space')->put($csv_array, $csv_string,'public');
        // $file_url = Storage::disk('space')->url($csv_array);
        $data['csv_string'] = $csv_string;
        $data['file_url']   = $csv_array;
        return $data;
    }

    function containsOnlyNull($input)
    {
        if ($input != null) {
            return empty(array_filter($input, function ($a) {
                return $a !== null;
            }));
        } else {
            return true;
        }
    }

    public function image_processing($product_all_image,$image_prefix,$image_suffix,$image_separator){
        $images = [];
        foreach($product_all_image as $image){
            if(!empty($image_prefix) && !empty($image_suffix) && !empty($image_separator)){
                if(substr($image_prefix,-1) != '/'){
                    $image_prefix = $image_prefix.'/';
                }
                if(substr($image_suffix,0,1) != '.'){
                    $image_suffix = '.'.$image_suffix;
                }
                $image_arrs = explode($image_separator,$image);
                foreach($image_arrs as $image_arr){
                    if(!empty($image_arr)){
                        $img_url = $image_prefix.$image_arr.$image_suffix;
                        array_push($images, $img_url);
                    }
                }

            }else if(empty($image_prefix) && !empty($image_suffix) && !empty($image_separator)){
                if(substr($image_suffix,0,1) != '.'){
                    $image_suffix = '.'.$image_suffix;
                }
                $image_arrs = explode($image_separator,$image);

                foreach($image_arrs as $image_arr){
                    if(!empty($image_arr)){
                        $img_url = $image_arr.$image_suffix;
                        array_push($images, $img_url);
                    }
                }
            }else if(!empty($image_prefix) && empty($image_suffix) && !empty($image_separator)){
                if(substr($image_prefix,-1) != '/'){
                    $image_prefix = $image_prefix.'/';
                }
                $image_arrs = explode($image_separator,$image);

                foreach($image_arrs as $image_arr){
                    if(!empty($image_arr)){
                        $img_url = $image_prefix.$image_arr;
                        array_push($images, $img_url);
                    }
                }
            }else{
                $semicolon = explode(';', $image);
                $coma = explode(',', $image);
                $pipe = explode('|', $image);
                $space = explode(' ', $image);
                if (is_array($semicolon) && count($semicolon) > 1) {
                    foreach ($semicolon as $url) array_push($images, $url);
                } elseif (is_array($coma) && count($coma) > 1) {
                    foreach ($coma as $url) array_push($images, $url);
                } elseif (is_array($pipe) && count($pipe) > 1) {
                    foreach ($pipe as $url) array_push($images, $url);
                } elseif (is_array($space) && count($space) > 1) {
                    foreach ($space as $url) array_push($images, $url);
                } else array_push($images, $image);
            }
        }
        return $images;
    }

    public function uvpPercentangeCalculation($ek_price,$uvp_percentange){
        if(is_int($ek_price) || is_float($ek_price)){
           return ( $ek_price + ($ek_price * $uvp_percentange / 100) );
        }else{
            return 0;
        }
    }

    public function cornotProductCheck(){
        ini_set('max_execution_time', '0');
        ini_set('memory_limit',-1);

        $products = Product::select('id','collection_id','calculation_id','ek_price','vk_price','uvp','shipping_cost','ean')->where('collection_id',85)->whereNull('calculation_id')->get();

        foreach($products->chunk(200) as $chunk_products){
           foreach($chunk_products as $product){
                $drm_products = $product->drmProducts()->get();
                if(count($drm_products) > 0){
                    $vk_price = app(\App\Services\Marketplace\ProductService::class)->updateAssignCalc(16,$product->ek_price,$product->uvp,$product->shipping_cost);
                    Log::info($vk_price);
                    if($vk_price){
                        $product->calculation_id = 16;
                        $product->vk_price = $vk_price;
                        $product->status = 1;
                        $product->update();

                        $data['vk_price'] = $vk_price;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info($product->ean);
                    }
                }
           }
        }
    }

    public function eladyProductUpdate(){
        // elady all service desabled ( Mattias )
        return true;
        $url = "http://**************/elady?columns=ean,stock,price";
        $file_content = file_get_contents($url);

        $reader = Reader::createFromString($file_content);
        $reader->setHeaderOffset(0);
        $csv_array = $reader->getRecords();
        $this->eladyStockSync($csv_array);
        $this->eladyPriceSnc($csv_array);

    }

    private function eladyStockSync($csv_array){
        $csv_ean_stock = collect($csv_array)->pluck('stock','ean')->toArray();
        $csv_ean_stock = array_map('intval', $csv_ean_stock);
        $local_product = Product::where('delivery_company_id', 14379)->pluck('stock','ean')->toArray();
        
        $new_stock    = array_diff_assoc($csv_ean_stock,$local_product);
        $array_ean    = array_keys(array_intersect_key($local_product, $new_stock));
        
        if(count($array_ean) > 0){
            foreach(array_chunk($array_ean ,1500) as $ean){

                $products = Product::with('drmProducts')
                            ->select('id','api_id','stock','ean','old_stock','stock_updated_at','ek_price')
                            ->where('delivery_company_id', 14379)
                            ->whereIn('ean',$ean)
                            ->get();
                $salesTrac = [];
                foreach($products as $product){
                    $api_new_stock = $new_stock[$product->ean];
                    if($product->stock != $api_new_stock){
                        $old_stock = $product->stock;
                        $product->stock             = $api_new_stock;
                        $product->old_stock         = $old_stock;
                        $product->stock_updated_at  = \Carbon\Carbon::now();

                        if($old_stock > $api_new_stock){
                            $discres_stock = $old_stock - $api_new_stock;
                            $salesTrac[] = [
                                'marketplace_product_id'    => $product->id,
                                'sales_stock'               => $discres_stock,
                                'sales_amount'              => $discres_stock * $product->ek_price,
                                'created_at'                => \Carbon\Carbon::now(),
                            ];
                        }
    
                        $drm_products = $product->drmProducts;
                        if(count($drm_products) > 0){
                            $data['stock'] = $api_new_stock;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                            info("Elady product Stock sync drm - " .$product->ean);
                        }
    
                        $product->update();
                        info("Elady product stock sync - ".$product->ean);
                    }
                }
                if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
            }
    
            info("Elady product stock sync  done ..............");
        }else{
            info("elady stock sync product not found");
        }
    }

    private function eladyPriceSnc($csv_array){
        $csv_ean_price = collect($csv_array)->pluck('price','ean')->toArray();
        $csv_ean_price = array_map('floatval', $csv_ean_price);
        
        $local_products = Product::where('delivery_company_id', 14379)->pluck('ek_price','ean')->toArray();
        $local_products = array_map('floatval', $local_products);
        
        $new_price    = array_diff_assoc($csv_ean_price,$local_products);
        $array_eanprice    = array_keys(array_intersect_key($local_products, $new_price));
        
        if(count($array_eanprice) > 0){
            $calculations = DB::connection('drm_core')->table('marketplace_profit_calculations')->get();
            foreach(array_chunk($array_eanprice ,500) as $ean){

                $products = Product::with('drmProducts')
                            ->select('id','ean','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','calculation_id','uvp','shipping_cost')
                            ->where('delivery_company_id', 14379)
                            ->whereIn('ean',$ean)
                            ->get();
    
                foreach($products as $product){
                    $api_new_price = $new_price[$product->ean];
                    if($product->ek_price != $api_new_price){
                        $old_ek_price = $product->ek_price;
                        $product->ek_price             = $api_new_price;
                        $product->old_ek_price         = $old_ek_price;
                        $product->ek_price_updated_at  = \Carbon\Carbon::now();

                        if(!empty($product->calculation_id)){
                            $new_vk_price = $this->updateAssignCalc($api_new_price,$product,$calculations);
                            if($new_vk_price){
                                $product->old_vk_price         = $product->vk_price;
                                $product->vk_price             = $new_vk_price;
                                $product->vk_price_updated_at  = \Carbon\Carbon::now();
                                $drm_products = $product->drmProducts;
                                if(count($drm_products) > 0){
                                    $data['vk_price'] = round($new_vk_price,2);
                                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                                    Log::info("Elady drm product price sync-".$product->ean);
                                }
                            }
                        }

    
                        $product->update();
                        info("Elady product stock sync - ".$product->ean);
                    }
                }
            }
    
            info("Elady product stock sync  done ..............");
        }else{
            info("elady price sync product not found");
        }
    }

    public function updateAssignCalc($api_new_price,$product,$calculations)
    {
        
        $calculation = $calculations->where('id',$product->calculation_id)->first();
        $price = $this->calculatePrice($api_new_price, $calculation, $product->uvp, $product->shipping_cost);
        if ($price > 0) {
            return $price;
        }
        return false;
        
    }

    public function calculatePrice($price, $calculation, $uvp = 0, $shipping_cost = 0)
    {
        if ($calculation->dynamic_shipping_cost && $shipping_cost > 0) {
            $calculation_shipping_cost = $shipping_cost;
        } else {
            $calculation_shipping_cost = $calculation->shipping_cost;
        }
        
        if ($calculation->uvp) {
            $price = $uvp;
        } else {
            $price = $price + $price
                * ($calculation->profit_percent / 100)
                + $calculation_shipping_cost
                + $calculation->additional_charge;

            if ($calculation->round_scale != null) {
                $prices = explode('.', $price);
                if ($prices[1] != 0) {
                    $price = $prices[0] + $calculation->round_scale;
                }
            }
        }
        return (float)str_replace(',', '', number_format($price, 2));
        
    }

    public function manualCsvProductInsert(){
        $url = "https://drm-file.fra1.digitaloceanspaces.com/marketplace-collections/71/b09f3cb4711cdffd37304d47bcc7b1fe.csv";
        $csv_string = file_get_contents($url);
        $csv_data =$this->generateArray($csv_string);
        
        foreach($csv_data as $data){
            $old_product = Product::where('delivery_company_id', 50131)->where('ean',$data['EAN'])->first();
            
            if($old_product){
                info("old Product");
                continue;
            }else{

                $valid_ean = app(\App\Services\Marketplace\ProductService::class)->validateEAN($data['EAN']);

                if(!$valid_ean) continue;

                $images = [];
                if(!empty($data['BILD 1'])) $images[] = $data['BILD 1'];
                if(!empty($data['Bild 2'])) $images[] = $data['Bild 2'];
                if(!empty($data['Bild 3'])) $images[] = $data['Bild 3'];
                if(!empty($data['Bild 4'])) $images[] = $data['Bild 4'];
                if(!empty($data['Bild 5'])) $images[] = $data['Bild 5'];

                $symbols     = array('$', '€', '£');
                $ek_price         = isset($data['NK'])? str_replace($symbols, '', $data['NK']): 0;
                $vk_price         = isset($data['VK'])? str_replace($symbols, '', $data['VK']): 0;

                if($ek_price == 0 || $vk_price == 0) continue;

                $data_array = [
                    'name' => $data['Titel'],
                    'ean' => $data['EAN'],
                    'item_number' => $data['Artikelnr.'],
                    'description' => $data['Beschreibung'],
                    'ek_price' => floatval($ek_price),
                    'vk_price' => floatval($vk_price),
                    'brand' => $data['Marke'],
                    'category_id' => 1915,
                    'image' => $images,
                    'shipping_cost' => 5.20,
                    'stock' => $data['Menge'],
                    'delivery_company_id' => 50131,
                    'supplier_id' => 3315,
                    'shipping_method' => 2,
                    'collection_id' => 0,
                    'internel_stock' => 0,
                    'created_at' => \Carbon\Carbon::now(),
                    'updated_at' => \Carbon\Carbon::now(),
                    'status'    => 5,
                    'internel_sync_status'  =>5,
                ];
                
                Product::create($data_array);
            }
            
        }
        
        dd("done");
    }


}
