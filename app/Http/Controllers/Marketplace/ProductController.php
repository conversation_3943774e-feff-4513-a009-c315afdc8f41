<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\DrmProduct;
use App\Models\Marketplace\CreditNote;
use App\Models\Marketplace\Product;
use App\Models\MarketplaceAllApiStock;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;

use App\Models\Marketplace\AutoTransferSubscription;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\Category;
use App\Models\DrmCategory;
use App\Models\DRMProductCategory;
use App\Models\DeliveryCompany;
use App\Models\UserSetting;
use App\Models\Country;
use App\Models\DrmOrder;
use App\Models\Marketplace\AllApiSyncReport;
use App\Models\Marketplace\ProductSyncHistory;
use Illuminate\Support\Carbon;
use DB;
use Log;
use SebastianBergmann\Diff\Chunk;
use Illuminate\Support\Facades\Http;
use App\Models\Marketplace\AllApiUpdateSchedule;
use App\Models\Marketplace\MarketplaceApiCsv;
use App\Models\Marketplace\Order;
use App\Models\DropmatixProductBrand;

class ProductController extends Controller
{
    public function compareMPPoroductToDrmProduct(){
        $product_status = \App\Enums\Marketplace\ProductStatus::ACTIVE;

        $drm_products = DrmProduct::where('status',$product_status)->select('id','ek_price','ean','marketplace_product_id')
                        ->whereNull('mp_offer')
                        ->whereNull('marketplace_product_id')
                        ->get()
                        ->toArray();
        if(!empty($drm_products)){
            foreach(array_chunk($drm_products,1000) as $drm_chunk_product){
                Log::info("MP To DRM purchaes compare job dispatch");
                dispatch(new \App\Jobs\Marketplace\CompareMpToDrmProductJob($drm_chunk_product));
            }
        }

    }

    public function processMpToDrmProductCompare($drm_products){
        $data = [];
        Log::info("MP To DRM purchaes compare job run");

        foreach($drm_products as $drm_product){
            $drm_product['ek_price'] = round($drm_product['ek_price'],2);
            $product_collection= Product::where([
                                    'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                                    'ean'   => $drm_product['ean']
                                ])->where('vk_price','<',$drm_product['ek_price'])->exists();

            if($product_collection) $data[]= $drm_product['id'];
        }

        if(!empty($data)){
            Log::info("Compare done");
            DrmProduct::whereIn('id',$data)->update(['mp_offer'=>1]);
        }
        Log::info("Successfully Run Complete");

    }

    public function checkBikeApiData(){
        $products = Product::where(['api_id'=>1,'name'=>''])->get('id');

        $drm_products = DrmProduct::whereIn('marketplace_product_id',$products)->get(['marketplace_product_id','ean'])->toArray();

        dd($products->toArray(),$drm_products);
    }

    public function createMonthlyReport(){
        $users = DB::connection('drm_core')->table('cms_users')->where('id_cms_privileges',4)->get();
        // dd(date('Y-m', strtotime('last month')));
        // dd(date('Y-m', strtotime(-2 .'month')),date('m', strtotime(-2 . '  month')));
        // $month = strtotime(-2 . 'month');
        $month = strtotime('last month');
        foreach($users as $user){
            $orders = DrmOrder::where('cms_user_id',$user->id)
                ->whereYear('order_date', date('Y', strtotime('last month')))
                ->whereMonth('order_date', date('m', $month))
                ->where('new_orders.invoice_number', '>', 0)
                ->get();
    
                CreditNote::updateOrCreate(['supplier_id'=>$user->id,'month'=>date('Y-m', $month)],['amount'=>$orders->sum('total')]);
                Log::info("monthly report created");
        }
    }

    public function bikeApiCategoryCheck(){
        $products = Product::where('api_id',1)->whereNotNull('misc')->get();

        $uniqu_categories = [];

        // foreach($products as $product){
        //     foreach($product->misc['categories'] as $category){
        //        if(!in_array($category['name_de'],$uniqu_categories)){
        //             $uniqu_categories[$category['name_de']] = $category['id'];
        //         }
        //     }
        // }

        foreach($products as $product){
               if(!in_array($product->misc['categories'][0]['name_de'],$uniqu_categories)){
                    $uniqu_categories[$product->misc['categories'][0]['name_de']] = $product->misc['categories'][0]['id'];
                }
        }

        dd($uniqu_categories);

    }


    public function bikeApiStockUpdateFromCsvData(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $startTime  = microtime(true);

        $api_id         = \App\Enums\Marketplace\ApiResources::BIKE_API_ID;
        $update_time    = \Carbon\Carbon::now()->addMinutes(15);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);
        
        $url            = \App\Enums\Marketplace\ApiResources::BIKE_API_STOCK_CSV_URL;
        $csv_string     = app(\App\Http\Controllers\Marketplace\CollectionController::class)->storeInStorage($api_id,$url);
        $csv_array      = app(\App\Http\Controllers\Marketplace\CollectionController::class)->generateArray($csv_string['csv_string']);
        $api_stock      = collect($csv_array)->pluck('stock','eancode')->toArray();
        
        $local_product = Product::where('api_id', $api_id)->pluck('stock','ean')->toArray();

        $new_stocks    = array_diff_assoc($api_stock,$local_product);

        $api_sync_report = new AllApiSyncReport();
        $api_sync_report->api_id    = $api_id;
        $api_sync_report->file_name = "Bike API - ".Carbon::now()->toDateTimeString();
        $api_sync_report->csv_url   = $csv_string['file_url'];
        $api_sync_report->save();

        $array_ean = [];
        foreach($new_stocks as $ean => $stock){
            if(array_key_exists($ean,$local_product)){
                $array_ean[] = $ean;
            }
        }

        $count = 0;
        foreach(array_chunk($array_ean,1500) as $ean){
            $local_products = Product::with('drmProducts')
                                        ->select('stock','ean','id','old_stock','stock_updated_at','ek_price')
                                        ->where('api_id', $api_id)
                                        ->whereIn('ean',$ean)
                                        ->get();
            $product_sync_datas = [];
            $salesTrac = [];
            foreach($local_products as $product){
                if($product->stock !=  $new_stocks[$product->ean] ){

                    $old_stock = $product->stock;

                    $product->stock             = $new_stocks[$product->ean];
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if($old_stock > $new_stocks[$product->ean]){
                        $discres_stock = $old_stock - $new_stocks[$product->ean];
                        $salesTrac[] = [
                            'marketplace_product_id'    => $product->id,
                            'sales_stock'               => $discres_stock,
                            'sales_amount'              => $discres_stock * $product->ek_price,
                            'created_at'                => \Carbon\Carbon::now(),
                        ];
                    }

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['stock'] = $new_stocks[$product->ean];
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("Bike API DRM product sync-".$product->ean);
                    }

                    $product->update();

                    $product_sync_datas[] =[
                        'report_id'              => $api_sync_report->id,
                        'marketplace_product_id' => $product->id,
                        'event'                  => 'update',
                        'tries'                  => 0,
                        'status'                 => 1,
                        'metadata'               => json_encode([
                            'stock'            => $new_stocks[$product->ean],
                            'old_stock'        => $old_stock,
                            'stock_updated_at' => \Carbon\Carbon::now(),
                        ]),
                        'synced_at'              => \Carbon\Carbon::now(),
                        'created_at'             => \Carbon\Carbon::now(),
                    ];

                    Log::info("Bike API product sync-".$product->ean);
                    $count += 1;
                }
            }

            if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);

            if(!empty($product_sync_datas)){
                ProductSyncHistory::insert($product_sync_datas);
            }
        }

        $ex_time =  (microtime(true) - $startTime) ." seconds";

        Log::info($ex_time. "</br>" ."Bike Api issue fixed on: " .$count. " Products");
        // return $ex_time. "</br>" ."Bike Api issue fixed on: " .$count. " Products";


    }

    public function checkApiProductAvailableOnSystem(){
        $bike_api_stocks = MarketplaceAllApiStock::where('api_id',1)->pluck('ean')->toArray();

        foreach(array_chunk($bike_api_stocks,300) as $bike_api_stock_chunk){

            Product::where('api_id',1)->whereIn('ean',$bike_api_stock_chunk)->update([
                'is_api_available' => 1
            ]);

            Log::info("is_api_available updated");
        }
        Log::info('Done');
    }

    public function checkAMissingApiProduct(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $products_ean = MarketplaceAllApiStock::whereDoesntHave('product')->pluck('ean')->toArray();

        dd($products_ean);
    }

    public function bikeApiProductThatHasNotApi(){
        $products = Product::where('api_id',1)->where('is_api_available',0)->pluck('ean')->toArray();

        dd($products);
    }


    public function genarateBikeApiReport(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $startTime = microtime(true);

        $url = 'https://www.twm-bv.com/api-v1/stock';
        $type = 'csv';

        $rows    = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);

        // $rows = Redis::get('bike_api');
        // $rows = json_decode($rows);

        MarketplaceAllApiStock::truncate();
        foreach(array_chunk($rows,2000) as $chunk_rows){
            $data = [];
            foreach($chunk_rows as $row){
                if(!empty($row['eancode'])){
                    $data[] =[
                        'ean'           => $row['eancode'],
                        'stock'         => $row['stock'],
                        'sku'           => $row['sku'],
                        'api_id'        => \App\Enums\Marketplace\ApiResources::BIKE_API_ID,
                        'created_at'    => \Carbon\Carbon::now(),
                    ];
                }
            }
            if(!empty($data)){
                MarketplaceAllApiStock::insert($data);
            }
        }

        $ex_time =  (microtime(true) - $startTime) ." seconds";

        return $ex_time. "</br>" ."Bike Api Report Generated";
    }

    public function showReport(){
        $startTime = microtime(true);

        $api_stock =  MarketplaceAllApiStock::pluck('stock','ean')->toArray();

        $local_product = Product::where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)->pluck('stock','ean')->toArray();

        $new_stocks = array_diff_assoc($api_stock,$local_product);
        $count = 0;
        foreach($new_stocks as $ean => $stock){
            if(array_key_exists($ean,$local_product)){
                $count += 1;
            }
        }
        $ex_time =  (microtime(true) - $startTime) ." seconds";
        return $ex_time. "</br>" . "Sync issue found on: " .$count. " Products";
    }

    public function stockSyncBikeApi(){

        $startTime = microtime(true);

        $api_stock      =  MarketplaceAllApiStock::pluck('stock','ean')->toArray();
        $local_product  = Product::where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)->pluck('stock','ean')->toArray();
        $new_stocks     = array_diff_assoc($api_stock,$local_product);

        $array_ean = [];
        foreach($new_stocks as $ean => $stock){
            if(array_key_exists($ean,$local_product)){
                $array_ean[] = $ean;
            }
        }

        $count = 0;
        foreach(array_chunk($array_ean,1500) as $ean){
            $local_products = Product::with('drmProducts')
                                        ->select('stock','ean','id','old_stock','stock_updated_at')
                                        ->where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)
                                        ->whereIn('ean',$ean)
                                        ->get();

            foreach($local_products as $product){
                if($product->stock !=  $new_stocks[$product->ean] ){

                    $old_stock = $product->stock;

                    $product->stock             = $new_stocks[$product->ean];
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['stock'] = $new_stocks[$product->ean];
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("Bike API DRM product sync-".$product->ean);
                    }

                    $product->update();
                    Log::info("Bike API product sync-".$product->ean);
                    $count += 1;
                }
            }
        }

        $ex_time =  (microtime(true) - $startTime) ." seconds";

        return $ex_time. "</br>" ."Bike Api issue fixed on: " .$count. " Products";
     }

    public function bikeApiStockZeroMissingProduct(){

        $local_product  = Product::where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)->where('stock','!=',0)->pluck('stock','ean')->toArray();
        $api_stock      =  MarketplaceAllApiStock::pluck('stock','ean')->toArray();

        $new_stocks     = array_diff_assoc($local_product,$api_stock);

        $array_ean = [];
        foreach($new_stocks as $ean => $stock){
            if(!array_key_exists($ean,$api_stock)){
                $array_ean[] = $ean;
            }
        }

        if(!isset($_GET['updated']))
        dd($array_ean);

        foreach(array_chunk($array_ean ,1500) as $ean){

            $products = Product::with('drmProducts')
                                        ->select('api_id','stock','ean','id','old_stock','stock_updated_at','ek_price')
                                        ->where('api_id', \App\Enums\Marketplace\ApiResources::BIKE_API_ID)
                                        ->whereIn('ean',$ean)
                                        ->get();
            $stock_out_sales_trac = [];
            foreach($products as $product){

                if($product->stock !=0 ){
                    $old_stock = $product->stock;
                    $product->stock             = 0;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    $stock_out_sales_trac[] = [
                        'marketplace_product_id'=> $product->id,
                        'sales_stock'           => $old_stock,
                        'sales_amount'          => $old_stock * $product->ek_price,
                        'created_at'            => \Carbon\Carbon::now(),
                    ];

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['stock'] = 0;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("Bike API DRM product sync-".$product->ean);
                    }
                    $product->update();
                    Log::info("Bike API product sync-".$product->ean);
                }

            }

            if(count($stock_out_sales_trac) > 0) DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);
        }

    }

    public function transferProductsInstantly($categorySubscription = null)
    {
        if($categorySubscription){
            $userId = $categorySubscription->user_id;
            $categoryId = $categorySubscription->category_id;
            $products = $this->getCountAllFilterProducts($categorySubscription);
            $productIds = $products->pluck('id')->toArray();
            Log::info("Get and total transfered MP products ids ".Count($productIds));
            $productIds = implode(",",$productIds) ?? null;
            $autoTransferUrl = "https://drm.software/api/mp-to-drm-product-auto-transfer/$productIds/$userId/$categoryId"; //Live url
            $autoTransferProducts = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($autoTransferUrl);
            Log::info($autoTransferProducts);
        }
    }

    public function transferProductToDrm ($productId = null, $user_id, $attributes = [])
    {
        DB::beginTransaction();
        try {
            if ($productId != null) {
                $pId = $productId;
            } else {
                $pId = request()->product_id;
            }
            Log::info("Product transfer function ".$pId);

            $product = new Product();
            $product = $product->where('id', $pId)->first();
            // Log::info("Product data ".$product);

            $drmProduct = new DrmProduct();
            $mpCoreToDrm = new MpCoreDrmTransferProduct();
            if ( Product::isInDrmProductList($product, $user_id) ) {
                if ( !(Product::isInMpDrmCoreTable($product->id, $user_id)) ) {
                    $inDrm = $drmProduct->select('id','marketplace_product_id','user_id','created_at','updated_at')->where('marketplace_product_id', $product->id)->get();

                    $mpCoreToDrm->insert([
                        'drm_product_id' => $inDrm->id,
                        'marketplace_product_id' => $inDrm->marketplace_product_id,
                        'user_id' => $inDrm->user_id,
                        'created_at' => $inDrm->created_at,
                        'updated_at' => $inDrm->created_at,
                    ]);

                    return null;
                }
            }


            $country_id = $this->getProductCountry($product->supplier_id);
            $lang = $this->getProductLanguage($country_id);

            $mpCategory = new Category();
            $drmCategories = new DrmCategory();

            if ( $product->category_id ) {
                $categoryName = $mpCategory->where('id',$product->category_id)->first()->name;
                if ( $drmCategories->where('category_name_'.$lang, $categoryName)->where('user_id', $user_id)->exists() ) {
                    $drmCategory = $drmCategories->where('category_name_'.$lang, $categoryName)->where('user_id', $user_id)->first();
                } else {
                    $drmCategory = $drmCategories->create([
                        'category_name' => $categoryName,
                        'category_name_'.$lang => $categoryName,
                        // 'category_name_en' => $categoryName,
                        // 'category_name_es' => $categoryName,
                        'user_id' => $user_id,
                        'country_id' => $country_id,
                    ]);

                }
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];

            $deliveryCompanies = new DeliveryCompany();
            $deliveryCompany = $deliveryCompanies->where('user_id', $user_id)
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();

            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id;
                $deliveryCompany = $deliveryCompanies->create($deliveryCompanyRow);
            }

            $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

            if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                $shippingCost = 0.0;
            } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                $shippingCost = $product->shipping_cost;
            }

            if ( $product->brand ) {
                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper($product->brand) . '%')->where('user_id', $user_id)->first();
                if ( $drmBrand ) {
                    $drmBrand = $drmBrand;
                } else {
                    $drmBrand = DropmatixProductBrand::create([
                        'brand_name' => $product->brand,
                        'user_id' => $user_id,
                    ]);
                }
            }

            $drmProductInfo             = [
                'user_id'               => $user_id,
                'country_id'            => $country_id,
                'language_id'           => null,
                'name'                  => $product->brand .' '. $product->name,
                'item_number'           => $product->item_number,
                'ean'                   => $product->ean,
                'image'                 => $product->image,
                'ek_price'              => $product->vk_price,
                'vk_price'              => 0.00,
                'vat'                   => NULL,
                'stock'                 => (empty($stock)) ? 0 : $stock,
                'category'              => $drmCategory->id ?? null,
                'ean_field'             => 1,
                'item_weight'           => $product->item_weight ?? null,
                'item_size'             => $product->item_size ?? null,
                'item_color'            => $product->item_color ?? null,
                'note'                  => $product->note ?? null,
                'production_year'       => $product->production_year ?? null,
                'brand'                 => $drmBrand ? $drmBrand->id : null,
                'materials'             => $product->materials ?? null,
                'tags'                  => $product->tags ?? null,
                'update_enabled'        => $product->update_enabled ?? null,
                'status'                => $product->status ?? null,
                'gender'                => $product->gender ?? null,
                'uvp'                   => $product->uvp ?? 0.00,
                'title'                 => [
                                             $lang => $product->name ?? null,
                                           ],
                'update_status'         => makeUpdateStatusJson(),
                'description' => [
                    $lang => $product->description ?? null,
                ],
                'delivery_company_id'     => $deliveryCompany->id,
                'marketplace_supplier_id' => $product->supplier_id ?? '',
                'marketplace_product_id'  => $product->id,
                'marketplace_shipping_method' => $product->shipping_method,
                'shipping_cost'               => $shippingCost ?? 0,
                'delivery_days'               => $product->delivery_days,
                'industry_template_data'      => json_encode($product->industry_template_data),
            ];

            $drmProductInfo = array_merge($drmProductInfo,$attributes);

            $drmProduct = $drmProduct->create($drmProductInfo);
            Log::info("Product create on drm ".$drmProduct);

            $drm_product_categories = new DRMProductCategory();

            $drm_product_categories->insert([
                'product_id' => $drmProduct->id,
                'category_id' => $drmCategory->id,
            ]);

            $mpCoreToDrm->insert([
                'drm_product_id' => $drmProduct->id,
                'marketplace_product_id' => $drmProduct->marketplace_product_id,
                'user_id' => $drmProduct->user_id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            DB::commit();
            // app(TransferProduct::class)->transferSynchronizedProductsToAnalysis(CRUDBooster::myId());
            return $drmProduct ?? null;

        } catch (\Exception $e) {
            DB::rollback();
        }
    }

    public function convertRangeBy ($rangeBy)
    {
      if ($rangeBy == 'ek_price') {
        return 'vk_price';
      } else if ($rangeBy == 'vk_price') {
        return 'uvp';
      } else if($rangeBy == 'profit'){
        return '(uvp - vk_price)/vk_price*100';
      } else if($rangeBy == 'stock'){
        return 'stock';
      } else {
        return $rangeBy;
      }
    }

    public function getProductCountry($user_id): int
    {
        $userSetting = new UserSetting();
        return $userSetting->where('user_id',$user_id)->value('product_country') ?? 1;
    }
    public function getProductLanguage($country_id): string
    {
        $country = new Country();
        return $country->where(['id' => $country_id])->value('language_shortcode') ?? 'de';
    }

    public function transferProductToDrmManually ($user_id, $category_id)
    {
        $this->transferProductsInstantly($user_id, $category_id);

        Log::info("Transfer product successfully");

    }

    public function transferAllFilteredProductsToDrm ($productId = null, $userId, $categoryId)
    {
        info("product transfer run ----------");
        DB::beginTransaction();
        try {
            $attributes = [];
            $autoTranferIds = [];
            $totalTransfered = 0;
            $productIds = (request()->product_id) ? request()->product_id : $productId;
            $user_id = $userId;
            $products = new Product();
            // $products = $products->setConnection('drm_team');
            $mpCategory = new Category();
            // $mpCategory = $mpCategory->setConnection('drm_team');
            $drmProducts = new DrmProduct();
            // $drmProducts = $drmProducts->setConnection('drm_core_team');
            $drmCategories = new DrmCategory();
            // $drmCategories = $drmCategories->setConnection('drm_core_team');
            $deliveryCompanies = new DeliveryCompany();
            // $deliveryCompanies = $deliveryCompanies->setConnection('drm_core_team');
            $mpCoreToDrm = new MpCoreDrmTransferProduct();
            // $mpCoreToDrm = $mpCoreToDrm->setConnection('drm_team');
            $drm_product_categories = new DRMProductCategory();
            // $drm_product_categories = $drm_product_categories->setConnection('drm_core_team');
            $autoTransferSubscriptions = new AutoTransferSubscription();
            // $autoTransferSubscriptions = $autoTransferSubscriptions->setConnection('drm_team');
            $drmBrands = new DropmatixProductBrand();
            // $drmBrands = $drmBrands->setConnection('drm_core_team');

            $id_exists = $drmProducts->whereIntegerInRaw('marketplace_product_id', $productIds)
                    ->where('user_id', $user_id)->pluck('marketplace_product_id')->toArray();

            $tranferable_ids = array_diff($productIds, $id_exists);
            if(empty($tranferable_ids)){
                Log::info("Products already transfered to drm. Please select new products.");

                return response()->json([
                    'status'      => true,
                    'message'     => 'Products already transfered to drm. Please select new products.',
                ]);
            }

            if($categoryId){
                $autoTransferSubscription = $autoTransferSubscriptions->where(['user_id'=>$user_id,'category_id'=>$categoryId])->first();
                $autoTranferIds = $autoTransferSubscription->transfered_product_ids ?? [];
                $totalTransfered = $autoTransferSubscription->transfered_products_count ?? 0;
                info("Total transfered ----------".$totalTransfered);
            }

            // $url = "https://drm.team/api/check_drm/import_plan/$user_id";
            $url = "https://drm.software/api/check_drm/import_plan/$user_id"; //live url
            $importProduct = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($url);

            $transferLimit = $importProduct['product_amount'] ?? 0;
            $transferPlan = $importProduct['plan'] ?? null;
            $transferPlanLimit = $importProduct['limit'] ?? null;
            Log::info("DRM Tarif Limit ".$transferLimit);

            $count = 0;
            $trial_checked = 0;
            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                foreach($tranferable_ids as $productId){
                    $count++;
                    $product = $products->where('id', $productId)->with('additionalInfo','productBrand')->first();
                    $ean_exist = $drmProducts->where('ean', $product->ean)->where('user_id', $userId)->first();
                    if(!$ean_exist){
                        // Additional columns check
                        $manufacturer = null;
                        $manufacturer_link = null;
                        $manufacturer_id = null;
                        $custom_tariff_number = null;
                        $shipping_company_id = null;
                        $region = null;
                        $country_of_origin = null;
                        $min_stock = null;
                        $min_order = null;
                        $gross_weight = null;
                        $net_weight = null;
                        $product_length = null;
                        $product_width = null;
                        $product_height = null;
                        $volume = null;
                        $packaging_length = null;
                        $packaging_width = null;
                        $packaging_height = null;
                        $item_unit = null;
                        $packing_unit = null;
                        $volume_gross = null;

                        if( $product->additionalInfo ){
                            $manufacturer = $product->additionalInfo->manufacturer;
                            $manufacturer_link = $product->additionalInfo->manufacturer_link;
                            $manufacturer_id = $product->additionalInfo->manufacturer_id;
                            $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                            $shipping_company_id = $product->additionalInfo->shipping_company_id;
                            $region = $product->additionalInfo->region;
                            $country_of_origin = $product->additionalInfo->country_of_origin;
                            $min_stock = $product->additionalInfo->min_stock;
                            $min_order = $product->additionalInfo->min_order;
                            $gross_weight = $product->additionalInfo->gross_weight;
                            $net_weight = $product->additionalInfo->net_weight;
                            $product_length = $product->additionalInfo->product_length;
                            $product_width = $product->additionalInfo->product_width;
                            $product_height = $product->additionalInfo->product_height;
                            $volume = $product->additionalInfo->volume;
                            $packaging_length = $product->additionalInfo->packaging_length;
                            $packaging_width = $product->additionalInfo->packaging_width;
                            $packaging_height = $product->additionalInfo->packaging_height;
                            $item_unit = $product->additionalInfo->item_unit;
                            $packing_unit = $product->additionalInfo->packaging_unit;
                            $volume_gross = $product->additionalInfo->volume_gross;
                        }
                        $country_id = $this->getProductCountry($product->supplier_id);
                        $lang = $this->getProductLanguage($country_id);

                        $discount = 0.0;
                        if ( $product->category_id ) {
                            $category = $mpCategory->where('id',$product->category_id)->first();
                            $drmCategory = $drmCategories->where('category_name_'.$lang, $category->name)->where('user_id',$userId)->first();
                            if( $category->start_date <= Carbon::now() && $category->end_date >= Carbon::now() ){
                                $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                            }
                            if ( !$drmCategory ) {
                                $drmCategory = $drmCategories->create([
                                    'category_name_'.$lang => $category->name,
                                    'user_id' => $user_id,
                                    'country_id' => $country_id,
                                ]);
                            }
                        }

                        $deliveryCompanyRow = [
                            'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                            'name'          => 'Dropmatix Systema SL',
                            'contact_name'  => 'Dropmatix Systema SL',
                            'zip'           => '07200',
                            'state'         => 'FELANITX',
                            'country_id'    => 8,
                            'email'         => '<EMAIL>',
                            'address'       => 'C/ HORTS, 33',
                            'note'          => 'Marketplace Supplier',
                            'is_marketplace_supplier' => 1,
                        ];

                        $deliveryCompany = $deliveryCompanies->where('user_id', $user_id)
                            ->where('name', $deliveryCompanyRow['name'])
                            ->where('email', '<EMAIL>')
                            ->first();

                        if ( !$deliveryCompany ) {
                            $deliveryCompanyRow['user_id'] = $user_id;
                            $deliveryCompany = $deliveryCompanies->create($deliveryCompanyRow);
                        }

                        $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                        if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                            $shippingCost = 0.0;
                        } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                            $shippingCost = $product->shipping_cost;
                        }

                        $drmBrand = null;
                        if ( $product->brand ) {
                            $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                            $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                            if ( $drmBrand ) {
                                $drmBrand = $drmBrand;
                            } else {
                                if(!empty($mpBrandName)){
                                    $drmBrand = DropmatixProductBrand::create([
                                        'brand_name' =>  $mpBrandName,
                                        'user_id' => $user_id,
                                        'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                    ]);
                                }
                            }
                        }

                        if( $product->offer_start_date <= Carbon::now() && date($product->offer_end_date) >= Carbon::now() && $product->is_offer_active == 1){
                            $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                        }

                        $drmProductInfo             = [
                            'user_id'               => $user_id,
                            'country_id'            => $country_id,
                            'language_id'           => null,
                            'name'                  => $product->brand .' '. $product->name,
                            'item_number'           => $product->item_number,
                            'ean'                   => $product->ean,
                            'additional_eans'       => json_encode($product->additional_eans),
                            'image'                 => $product->image,
                            'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, $discount),
                            'vk_price'              => 0.00,
                            'vat'                   => NULL,
                            'stock'                 => (empty($stock)) ? 0 : $stock,
                            'category'              => $drmCategory->id ?? null,
                            'ean_field'             => 1,
                            'item_weight'           => $product->item_weight ?? null,
                            'item_size'             => $product->item_size ?? null,
                            'item_color'            => $product->item_color ?? null,
                            'note'                  => $product->note ?? null,
                            'production_year'       => $product->production_year ?? null,
                            'brand'                 => $drmBrand ? $drmBrand->id : null,
                            'materials'             => $product->materials ?? null,
                            'tags'                  => $product->tags ?? null,
                            'update_enabled'        => $product->update_enabled ?? null,
                            'status'                => $product->status ?? null,
                            'gender'                => $product->gender ?? null,
                            'uvp'                   => $product->uvp ?? 0.00,
                            'title'                 => [
                                                        $lang => $product->name ?? null,
                                                    ],
                            'update_status'         => makeUpdateStatusJson(),
                            'description' => [
                                $lang => $product->description ?? null,
                            ],
                            'delivery_company_id'     => $deliveryCompany->id,
                            'marketplace_supplier_id' => $product->supplier_id ?? '',
                            'marketplace_product_id'  => $product->id,
                            'marketplace_shipping_method' => $product->shipping_method,
                            'shipping_cost'               => $shippingCost ?? 0,
                            'delivery_days'               => $product->delivery_days,
                            'industry_template_data'      => json_encode($product->industry_template_data),
                            'product_type'                => 1,
                            'mp_category_offer'           => $discount,
                            // Additional columns
                            'manufacturer'                => $manufacturer,
                            'manufacturer_link'           => $manufacturer_link,
                            'manufacturer_id'             => $manufacturer_id,
                            'custom_tariff_number'        => $custom_tariff_number,
                            'shipping_company_id'         => $shipping_company_id ?? 8,
                            'region'                      => $region,
                            'country_of_origin'           => $country_of_origin,
                            'min_stock'                   => $min_stock,
                            'min_order'                   => $min_order,
                            'gross_weight'                => $gross_weight,
                            'net_weight'                  => $net_weight,
                            'product_length'              => $product_length,
                            'product_width'               => $product_width,
                            'product_height'              => $product_height,
                            // 'volume'                      => $volume,
                            'packaging_length'            => $packaging_length,
                            'packaging_width'             => $packaging_width,
                            'packaging_height'            => $packaging_height,
                            'item_unit'                   => $item_unit,
                            'packaging_unit'              => $packing_unit,
                            // 'volume_gross'                => $volume_gross,
                        ];

                        $drmProductInfo = array_merge($drmProductInfo,$attributes);
                        $drmProduct = $drmProducts->create($drmProductInfo);
                        Log::info("Product create on drm ean ".$drmProduct->ean);
                        $transferLimit--;

                        if ($trial_checked == 0) {
                            // app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial(\CRUDBooster::myId());
                            $check_trial = DB::connection('drm_core')->table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
                            $check_product = DB::connection('drm_core')->table('drm_products')->where('user_id', $user_id)->first();
                            if (!$check_trial && $check_product != null) {
                                DB::connection('drm_core')->table('app_trials')->insert([
                                    'user_id' => $user_id,
                                    'app_id' => 0,
                                    'trial_days' => 14,
                                    'start_date' => date("Y-m-d"),
                                ]);
                            }
                            $trial_checked = 1;
                        }

                        $autoTranferIds[$productId] = $drmProduct->id;

                        $drm_product_categories->insert([
                            'product_id' => $drmProduct->id,
                            'category_id' => $drmCategory->id,
                            'country_id' => $drmCategory->country_id,
                        ]);

                        $mpCoreToDrm->insert([
                            'drm_product_id' => $drmProduct->id,
                            'marketplace_product_id' => $drmProduct->marketplace_product_id,
                            'user_id' => $drmProduct->user_id,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now(),
                        ]);
                    }
                }
            } else if( $transferLimit > $count) {
                foreach($tranferable_ids as $productId){
                    if($transferLimit > $count) {
                        $count++;
                        $product = $products->where('id', $productId)->with('additionalInfo','productBrand')->first();
                        $ean_exist = $drmProducts->where('ean', $product->ean)->where('user_id', $userId)->first();
                        if(!$ean_exist){
                            // Additional columns check
                            $manufacturer = null;
                            $manufacturer_link = null;
                            $manufacturer_id = null;
                            $custom_tariff_number = null;
                            $shipping_company_id = null;
                            $region = null;
                            $country_of_origin = null;
                            $min_stock = null;
                            $min_order = null;
                            $gross_weight = null;
                            $net_weight = null;
                            $product_length = null;
                            $product_width = null;
                            $product_height = null;
                            $volume = null;
                            $packaging_length = null;
                            $packaging_width = null;
                            $packaging_height = null;
                            $item_unit = null;
                            $packing_unit = null;
                            $volume_gross = null;

                            if( $product->additionalInfo ){
                                $manufacturer = $product->additionalInfo->manufacturer;
                                $manufacturer_link = $product->additionalInfo->manufacturer_link;
                                $manufacturer_id = $product->additionalInfo->manufacturer_id;
                                $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                                $shipping_company_id = $product->additionalInfo->shipping_company_id;
                                $region = $product->additionalInfo->region;
                                $country_of_origin = $product->additionalInfo->country_of_origin;
                                $min_stock = $product->additionalInfo->min_stock;
                                $min_order = $product->additionalInfo->min_order;
                                $gross_weight = $product->additionalInfo->gross_weight;
                                $net_weight = $product->additionalInfo->net_weight;
                                $product_length = $product->additionalInfo->product_length;
                                $product_width = $product->additionalInfo->product_width;
                                $product_height = $product->additionalInfo->product_height;
                                $volume = $product->additionalInfo->volume;
                                $packaging_length = $product->additionalInfo->packaging_length;
                                $packaging_width = $product->additionalInfo->packaging_width;
                                $packaging_height = $product->additionalInfo->packaging_height;
                                $item_unit = $product->additionalInfo->item_unit;
                                $packing_unit = $product->additionalInfo->packing_unit;
                                $volume_gross = $product->additionalInfo->volume_gross;
                            }
                            $country_id = $this->getProductCountry($product->supplier_id);
                            $lang = $this->getProductLanguage($country_id);
                            $discount = 0.0;
                            if ( $product->category_id ) {
                                $category = $mpCategory->where('id',$product->category_id)->first();
                                $drmCategory = $drmCategories->where('category_name_'.$lang, $category->name)->where('user_id',$userId)->first();
                                if( $category->start_date <= Carbon::now() && $category->end_date >= Carbon::now() ){
                                    $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                                }
                                if ( !$drmCategory ) {
                                    $drmCategory = $drmCategories->create([
                                        'category_name_'.$lang => $category->name,
                                        'user_id' => $user_id,
                                        'country_id' => $country_id,
                                    ]);
                                }
                            }

                            $deliveryCompanyRow = [
                                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                                'name'          => 'Dropmatix Systema SL',
                                'contact_name'  => 'Dropmatix Systema SL',
                                'zip'           => '07200',
                                'state'         => 'FELANITX',
                                'country_id'    => 8,
                                'email'         => '<EMAIL>',
                                'address'       => 'C/ HORTS, 33',
                                'note'          => 'Marketplace Supplier',
                                'is_marketplace_supplier' => 1,
                            ];

                            $deliveryCompany = $deliveryCompanies->where('user_id', $user_id)
                                ->where('name', $deliveryCompanyRow['name'])
                                ->where('email', '<EMAIL>')
                                ->first();

                            if ( !$deliveryCompany ) {
                                $deliveryCompanyRow['user_id'] = $user_id;
                                $deliveryCompany = $deliveryCompanies->create($deliveryCompanyRow);
                            }

                            $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                            if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                                $shippingCost = 0.0;
                            } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                                $shippingCost = $product->shipping_cost;
                            }

                            $drmBrand = null;
                            if ( $product->brand ) {
                                $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                                if ( $drmBrand ) {
                                    $drmBrand = $drmBrand;
                                } else {
                                    if(!empty($mpBrandName)){
                                        $drmBrand = DropmatixProductBrand::create([
                                            'brand_name' =>  $mpBrandName,
                                            'user_id' => $user_id,
                                            'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                        ]);
                                    }
                                }
                            }

                            if( $product->offer_start_date <= Carbon::now() && $product->offer_end_date >= Carbon::now() && $product->is_offer_active == 1){
                                $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                            }

                            $drmProductInfo             = [
                                'user_id'               => $user_id,
                                'country_id'            => $country_id,
                                'language_id'           => null,
                                'name'                  => $product->brand .' '. $product->name,
                                'item_number'           => $product->item_number,
                                'ean'                   => $product->ean,
                                'additional_eans'       => json_encode($product->additional_eans),
                                'image'                 => $product->image,
                                'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, $discount),
                                'vk_price'              => 0.00,
                                'vat'                   => NULL,
                                'stock'                 => (empty($stock)) ? 0 : $stock,
                                'category'              => $drmCategory->id ?? null,
                                'ean_field'             => 1,
                                'item_weight'           => $product->item_weight ?? null,
                                'item_size'             => $product->item_size ?? null,
                                'item_color'            => $product->item_color ?? null,
                                'note'                  => $product->note ?? null,
                                'production_year'       => $product->production_year ?? null,
                                'brand'                 => $drmBrand ? $drmBrand->id : null,
                                'materials'             => $product->materials ?? null,
                                'tags'                  => $product->tags ?? null,
                                'update_enabled'        => $product->update_enabled ?? null,
                                'status'                => $product->status ?? null,
                                'gender'                => $product->gender ?? null,
                                'uvp'                   => $product->uvp ?? 0.00,
                                'title'                 => [
                                                            $lang => $product->name ?? null,
                                                        ],
                                'update_status'         => makeUpdateStatusJson(),
                                'description' => [
                                    $lang => $product->description ?? null,
                                ],
                                'delivery_company_id'     => $deliveryCompany->id,
                                'marketplace_supplier_id' => $product->supplier_id ?? null,
                                'marketplace_product_id'  => $product->id,
                                'marketplace_shipping_method' => $product->shipping_method,
                                'shipping_cost'               => $shippingCost ?? 0,
                                'delivery_days'               => $product->delivery_days,
                                'industry_template_data'      => json_encode($product->industry_template_data),
                                'product_type'                => 1,
                                'mp_category_offer'           => $discount,
                                // Additional columns
                                'manufacturer'                => $manufacturer,
                                'manufacturer_link'           => $manufacturer_link,
                                'manufacturer_id'             => $manufacturer_id,
                                'custom_tariff_number'        => $custom_tariff_number,
                                'shipping_company_id'         => $shipping_company_id ?? 8,
                                'region'                      => $region,
                                'country_of_origin'           => $country_of_origin,
                                'min_stock'                   => $min_stock,
                                'min_order'                   => $min_order,
                                'gross_weight'                => $gross_weight,
                                'net_weight'                  => $net_weight,
                                'product_length'              => $product_length,
                                'product_width'               => $product_width,
                                'product_height'              => $product_height,
                                // 'volume'                      => $volume,
                                'packaging_length'            => $packaging_length,
                                'packaging_width'             => $packaging_width,
                                'packaging_height'            => $packaging_height,
                                'item_unit'                   => $item_unit,
                                'packaging_unit'              => $packing_unit,
                                // 'volume_gross'                => $volume_gross,
                            ];

                            $drmProductInfo = array_merge($drmProductInfo,$attributes);

                            $drmProduct = $drmProducts->create($drmProductInfo);
                            $transferLimit--;
                            Log::info("Product create on drm ean ".$drmProduct->ean);

                            if ($trial_checked == 0) {
                                // app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial(\CRUDBooster::myId());
                                $check_trial = DB::connection('drm_core')->table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
                                $check_product = DB::connection('drm_core')->table('drm_products')->where('user_id', $user_id)->first();
                                if (!$check_trial && $check_product != null) {
                                    DB::connection('drm_core')->table('app_trials')->insert([
                                        'user_id' => $user_id,
                                        'app_id' => 0,
                                        'trial_days' => 14,
                                        'start_date' => date("Y-m-d"),
                                    ]);
                                }
                                $trial_checked = 1;
                            }

                            $autoTranferIds[$productId] = $drmProduct->id;

                            $drm_product_categories->insert([
                                'product_id' => $drmProduct->id,
                                'category_id' => $drmCategory->id,
                                'country_id' => $drmCategory->country_id,
                            ]);

                            $mpCoreToDrm->insert([
                                'drm_product_id' => $drmProduct->id,
                                'marketplace_product_id' => $drmProduct->marketplace_product_id,
                                'user_id' => $drmProduct->user_id,
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ]);
                        }
                    } else {
                        break;
                    }
                }
            } else {
                Log::info("Your DRM products transfer limit exceed! Please Upgrade your tarif plan.");

            }

            if($categoryId){
                $autoTransferSubscriptions->where(['user_id'=>$user_id,'category_id'=>$categoryId])->update(['transfered_product_ids'=>$autoTranferIds, 'transfered_products_count'=>$totalTransfered + $count]);
            }

            DB::commit();

            Log::info('Successfully transfered '.$count.' Products');
            if($autoTranferIds){
                $autoTranferIds = implode(",",$autoTranferIds) ?? null;
                // $hitDrm ="https://drm.team/api/drm/mp-auto-transfer-drm/$user_id/$autoTranferIds";
                $hitDrm = "https://drm.software/api/drm/mp-auto-transfer-drm/$user_id/$autoTranferIds"; //Live url
                $autoCheckProduct = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($hitDrm);
    
                // Log::info($hitDrm);
                // Log::info($autoCheckProduct);
            }

            return response()->json([
                'status'      => true,
                'message'     => 'Successfully transfered '.$count.' Products.',
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::info("Something went wrong. ".$e->getMessage());
        }
    }

    public function autoProductsTransferHitting()
    {
        $v2_users = [62,71,212,3878,2454,3675,3987,4146,2455,3417,4173,3602];
        $autoTransferSubscriptions = new AutoTransferSubscription();
        // $autoTransferSubscriptions = $autoTransferSubscriptions->setConnection('drm_team');
        $autoTransferSubscription = $autoTransferSubscriptions->whereNotIn('user_id', $v2_users)->where('status', 1)->get();

        $userIds = $autoTransferSubscription->pluck('user_id')->toArray();

        if (!empty($userIds)) {
            $high_tariff_users = DB::connection('drm_core')
                ->table('purchase_import_plans')
                ->select('cms_user_id as user_id')
                ->whereIn('import_plan_id', [26, 27])
                ->whereIn('cms_user_id', $userIds)
                ->union(
                    DB::connection('drm_core')
                        ->table('dt_tariff_purchases')
                        ->select('user_id')
                        ->where('plan_id', 31)
                        ->whereIn('user_id', $userIds)
                )
                ->pluck('user_id')
                ->toArray();
        } else {
            $high_tariff_users = [];
        }

        foreach($autoTransferSubscription as $categorySubscription){
            dispatch(new \App\Jobs\Marketplace\TransferProductsInstantly($categorySubscription, $high_tariff_users));
        }
    }

    public function bikeApiPriceUpdateByCsv(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $startTime  = microtime(true);

        $api_id  = \App\Enums\Marketplace\ApiResources::BIKE_API_ID;
        $api_csv = MarketplaceApiCsv::where('api_id',$api_id)->where('is_update',0)->first();

        if(!empty($api_csv)){
            $type       = 'csv';
            $csv_array  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($api_csv->csv_url, $type, 'auto', false);
            $api_price  = collect($csv_array)->pluck('price','ean')->toArray();

            $local_product = Product::where('api_id', $api_id)->pluck('ek_price','ean')->toArray();
            $new_price    = array_diff_assoc($api_price,$local_product);

            $array_ean = [];
            foreach($new_price as $ean => $price){
                if(array_key_exists($ean,$local_product)){
                    $array_ean[] = $ean;
                }
            }

            $count = 0;
            foreach(array_chunk($array_ean,500) as $ean){
                $local_products = Product::with('drmProducts')
                                            ->select('ek_price','ean','id','vk_price','api_id','update_status')
                                            ->where('api_id', $api_id)
                                            ->whereIn('ean',$ean)
                                            ->get();
                
                foreach($local_products as $product){
                    if($product->ek_price !=  $new_price[$product->ean] ){
                        $ek_price = $new_price[$product->ean];
                        $vk_price = $new_price[$product->ean] + ($new_price[$product->ean] *0.05);
                        $product->ek_price     = $ek_price;
                        $update_status = $product->update_status;
                        if(isset($update_status['vk_price']) && $update_status['vk_price'] == 1){
                            $product->vk_price     = $vk_price;
                            $drm_products = $product->drmProducts;
                            if(count($drm_products) > 0){
                                $data['vk_price'] = round($vk_price,2);
                                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                                Log::info("Bike API DRM Price sync-".$product->ean);
                            }
                        } else if (isset($update_status['vk_price']) && $update_status['vk_price'] == 0){
                            // VK price update on MP
                        } else {
                            $product->vk_price     = $vk_price;
                            $drm_products = $product->drmProducts;
                            if(count($drm_products) > 0){
                                $data['vk_price'] = round($vk_price,2);
                                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                                Log::info("Bike API DRM Price sync-".$product->ean);
                            }
                        }

                        $product->update();

                        Log::info("Bike API price sync-".$product->ean);
                        $count++;
                    }else{
                        Log::info("Bike API not update-".$product->ean);
                    }
                }
            }

            $api_csv->update(['is_update'=>1]);
            $ex_time =  (microtime(true) - $startTime) ." seconds";

            Log::info($ex_time. "</br>" ."Bike Api price fixed on: " .$count. " Products");
            return response()->json([
                'status'      => true,
                'message'     => $ex_time. "</br>" ."Bike Api price fixed on: " .$count. " Products",
            ]);
        }else{
            MarketplaceApiCsv::where('api_id',$api_id)->update(['is_update'=>0]);
        }
    }

    public function checkMpToDrmProductChanges(){
        $startTime  = microtime(true);
        if(isset($_GET['column']) && $_GET['column'] == 1){
            $drm_products = DrmProduct::whereNotNull('marketplace_product_id')->pluck('stock','marketplace_product_id')->toArray();
            $mp_products = Product::where('shipping_method',1)->pluck('stock','id')->toArray();
    
            $arr_mpid = [];
            $array_html = '';
            foreach($drm_products as $key => $value){
                if(array_key_exists($key,$mp_products) && $value != $mp_products[$key]){
                    $arr_mpid[] = $key;
                    $array_html .= '<tr><td>'.$key.'</td><td>'.$mp_products[$key].'</td><td>'.$value.'</td></tr>';
                }
            }
            dump(count($arr_mpid));
            $html = '<table border="1"><tr><th>MP ID</th><th>MP Stock</th><th>Drm Stock</th></tr>'.$array_html.'</table>';
    
            if(!isset($_GET['updated']))
            return $html;
    
            $count = 0;
            foreach(array_chunk($arr_mpid,100) as $id){
                $local_products = Product::with('drmProducts')
                                            ->where('shipping_method',1)
                                            ->select('id','stock','ean','shipping_method','internel_stock')
                                            ->whereIn('id',$id)
                                            ->get();
                foreach($local_products as $product){
                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        
                        $data['stock'] = $product->shipping_method == 1?$product->stock:$product->internel_stock;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("MP To DRM Stock sync-".$product->ean);
                        $count++;
                    }
                }
            }

            $ex_time =  (microtime(true) - $startTime) ." seconds";

            return response()->json([
                'status'      => true,
                'message'     => $ex_time. "</br>" ."Bike Api stock fixed on: " .$count. " Products",
            ]);
    
        }else{
            dd('parameter not found');
        }
    }

    public function allAPIProductsUVPincrease(){
        
        dd("This function is not working");
        $startTime  = microtime(true);
        $api_id  = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;

        $local_products = Product::with('drmProducts')
                                    ->select('uvp','id','api_id','is_api_available','ean')
                                    ->where('api_id', $api_id)
                                    ->where('is_api_available','<>',3)
                                    ->take(50000)
                                    ->get();
        $count = 0;
        foreach($local_products as $product){
            $old_uvp = $product->uvp;
            $new_uvp = $product->uvp + ($product->uvp * 0.10);
            $product->uvp = $new_uvp;
            $product->is_api_available = 3;

            $drm_products = $product->drmProducts;
            if(count($drm_products) > 0){
                $data['uvp'] = round($new_uvp,2);
                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                Log::info("Product UVP update drm-".$product->ean."-".$old_uvp);
            }
            Log::info("Product UVP update-".$product->ean."-".$old_uvp);
            $product->update();
            $count++;
        }
        $ex_time =  (microtime(true) - $startTime) ." seconds";

        return response()->json([
            'status'      => true,
            'message'     => $ex_time. "</br>" ."Product UVP Update: " .$count. " Products",
        ]);
    }

    public function productBrandManualUpdate(){
        dd("brand update function is not working");
        $brand_arr = DB::table('marketplace_products')
                    ->where('collection_id', 130)
                    ->whereNotNull('brand')
                    ->where('brand', 'NOT REGEXP', '^[0-9]+$')
                    ->pluck('brand')
                    ->toArray();

        $brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($brand_arr);

        $products = Product::where('collection_id', 130)
                            ->whereNotNull('brand')
                            ->where('brand', 'NOT REGEXP', '^[0-9]+$')
                            ->select('id','ean','brand')
                            ->get();

        foreach($products as $product){
            $brand_id = $brands[strtoupper($product->brand)] ?? '';
            
            if(!blank($brand_id)){

                if($product->brand != $brand_id){
                    $product->brand = $brand_id;
                    $product->update();
                    info("brand API product brand sync-".$product->ean);
                }
            }
        }

        dd('brand updated');

    }

    public function getCountAllFilterProducts($categorySubscription){

        $category_id = $categorySubscription->category_id;
        $products = new Product();
        // $products = $products->setConnection('drm_team');
        $productQuery = $products->whereIn('status', [\App\Enums\Marketplace\ProductStatus::ACTIVE])
            ->where('category_id', $category_id)->where('country_id', 1);

        if ($categorySubscription->filter_ek && $categorySubscription->filter_ek[1] > 0) {
            $minPrice = $categorySubscription->filter_ek[0] ?? 0;
            $maxPrice = $categorySubscription->filter_ek[1] ?? 0;
            $rangeColumn = $this->convertRangeBy('ek_price');
            $productQuery->where($rangeColumn, '>=', intval($minPrice))
                ->where($rangeColumn, '<=', intval($maxPrice));
        }
        if ($categorySubscription->filter_uvp && $categorySubscription->filter_uvp[1] > 0) {
            $minPrice = $categorySubscription->filter_uvp[0] ?? 0;
            $maxPrice = $categorySubscription->filter_uvp[1] ?? 0;
            $rangeColumn = $this->convertRangeBy('vk_price');
            $productQuery->where($rangeColumn, '>=', intval($minPrice))
                ->where($rangeColumn, '<=', intval($maxPrice));
        }
        if ($categorySubscription->filter_profit && $categorySubscription->filter_profit[1] > 0) {
            $minPrice = $categorySubscription->filter_profit[0] ?? 0;
            $maxPrice = $categorySubscription->filter_profit[1] ?? 0;
            $productQuery->whereRaw("((uvp - vk_price)/vk_price*100) >= $minPrice");
            $productQuery->whereRaw("((uvp - vk_price)/vk_price*100) <= $maxPrice");
        }
        if ($categorySubscription->filter_quantity && $categorySubscription->filter_quantity[1] > 0) {
            $minPrice = $categorySubscription->filter_quantity[0] ?? 0;
            $maxPrice = $categorySubscription->filter_quantity[1] ?? 0;
            $rangeColumn = $this->convertRangeBy('stock');
            $productQuery->where($rangeColumn, '>=', intval($minPrice))
                ->where($rangeColumn, '<=', intval($maxPrice));
        }
        if (!empty($categorySubscription->brand)) {
            $productQuery->whereIn('brand', $categorySubscription->brand);
        }
        if (!empty($categorySubscription->skip_brand)) {
            $productQuery->whereNotIn('brand', $categorySubscription->skip_brand);
        }
        return  $productQuery;
    }

}
