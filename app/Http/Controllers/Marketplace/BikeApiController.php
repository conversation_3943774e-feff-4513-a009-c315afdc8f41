<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Interfaces\BikeApiInterface;
use App\Jobs\marketplace\BikeApiUpdatedProductsSync;
use App\Models\DrmOrder;
use App\Models\Marketplace\ApiProduct;
use App\Models\Marketplace\BikeApiCredential;
use App\Models\Marketplace\BikeApiProductSegment;
use App\Models\Marketplace\Collection;
use App\Models\Marketplace\Product;
use App\Models\MarketplaceApiSyncHistory;
use App\Models\MarketplaceSyncReport;
use App\Services\Marketplace\ProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Carbon;
class BikeApiController extends Controller implements BikeApiInterface
{
    public $addresses;
    public $apiService;

    public function __construct ()
    {
        $this->apiService   = new \App\Services\Marketplace\BikeApi\BikeApiService();
        $this->addresses    = \App\Enums\Marketplace\ApiResources::ADDRESSES;
    }

    // START:: Products
    // START:: Products

    public function buildProductSyncJobs ()
    {
        $row = $this->apiService->apiCredentials;

        $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'].'?page='.$row->next_import_page ?? 1;
        $response = $this->apiService->fetchData($apiAddress);

        try {
            if ( !array_key_exists('data',$response) || empty($response['data']) ) {
                return [
                    'status' => 'terminated',
                    'msg'    => 'Terminated ! No products found.',
                ];
            }

            $currentPage = $response['meta']['current_page'];
            $lastPage    = $response['meta']['last_page'];

            $jobsCount = 0;
            $tmpPageCount = 1;
            $pagesArr = [];

            $jobLimit = 0;

            for ( $page = $currentPage; $page <= $lastPage; $page++ ) {

                $pagesArr[] = $page;

                if ( $tmpPageCount == 10 || ($page == $lastPage && $tmpPageCount < 10)) {

                    dispatch(new \App\Jobs\Marketplace\BikeApiProductSync($pagesArr));

                    $tmpPageCount = 1;
                    $jobsCount++;

                    $row->update([
                        'next_import_page' => $page,
                    ]);

                    $pagesArr = [];
                } else {
                    $tmpPageCount++;
                }
                $jobLimit++;
            }
            $row->decrement('next_import_page',1);
            return [
                'created_jobs_count' => $jobsCount,
            ];
        } catch (\Exception $e){
            return [
              'error' => $e->getMessage(),
            ];
        }

    }

    public function buildChangesProductsSyncJobs ($days = 1)
    {
        $segments = BikeApiProductSegment::all();
        foreach ($segments as $segment) {
            $segmentCode = $segment->code;
            $this->v2FetchChangedProductsForNDays($segmentCode, 1);
            // dispatch(new \App\Jobs\Marketplace\BikeApiUpdatedProductsSync($segmentCode));
        }
    }

    public function getAllProducts ($pages)
    {
        $row = $this->apiService->apiCredentials;

        $allProducts = [];
        try {
            foreach ($pages as $page) {
                try{
                    $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;
                    $response = $this->apiService->fetchData($apiAddress);

                    if (!array_key_exists('data', $response) || empty($response['data'])) {
                        continue;
                    } else {
                        $allProducts = array_merge($allProducts, $response['data']);
                        $this->apiService->ch = curl_init();
                    }
                } catch (\Exception $e) {
                    return $e->getMessage();
                }
            }
            $this->apiService->pushProductsFromApi($allProducts);

        } catch (\Exception $e) {
            return 'Got exception:: '.$e->getMessage();
        }
    }

    public function getAllProductsManualProcess ($pages = [])
    {
        $row = $row = $this->apiService->apiCredentials;

        if (count($pages) == 0) {
            $targetPage = ($row->next_import_page-1)  + 40;
            for ($c = $row->next_import_page; $c < $targetPage; $c++) {
                $pages[] = $c;
            }
        }

        $allProducts = [];
        // $sync_report_ids = [];
        try {
            foreach ($pages as $page) {
                try{
                    $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;

                    $response   = $this->apiService->fetchData($apiAddress);
                    usleep(100000);

                    if (empty($response) || empty($response['data'])) {

                        $product_of_first = $this->apiService->fetchData($this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . 1);

                        if($page > $product_of_first['meta']['last_page']){
                            $targetPage = 1;
                            break;
                        }

                        // $empty_sync_report = new MarketplaceSyncReport();
                        // $empty_sync_report->sync_time = \Carbon\Carbon::now();
                        // $empty_sync_report->status = 3;
                        // $empty_sync_report->save();

                        // $this->insertApiSyncHistory($page,$response,$empty_sync_report->id);

                        continue;
                    } else {
                        $allProducts = array_merge($allProducts, $response['data']);
                        $this->apiService->ch = curl_init();

                        // $sync_report = new MarketplaceSyncReport();
                        // $sync_report->sync_time = \Carbon\Carbon::now();
                        // $sync_report->status = 2;
                        // $sync_report->save();

                        // $this->insertApiSyncHistory($page,$response,$sync_report->id);

                        // $sync_report_ids[] = $sync_report->id;

                    }

                } catch (\Exception $e) {
                    return $e->getMessage();
                }
            }
            // Insert products to marketplace database
            $this->apiService->pushProductsFromApi($allProducts);

            // MarketplaceSyncReport::whereIn('id', $sync_report_ids)->update([
            //     'status' => 1,
            // ]);

            // Update attributes english to de
            // $this->apiService->changeAttributeEnglishToDe($allProducts);

            $row->next_import_page = $targetPage;
            $row->save();

        } catch (\Exception $e) {
            return 'Got exception:: '.$e->getMessage();
        }
    }

    public function updateProductAttr()
    {
        $credentials = $this->apiService->apiCredentials;

        // $startPage = $credentials->next_update_page;
        $startPage = $credentials->next_import_page;

        $endPage   = $startPage+10;
        $allProducts = [];

        try {
            for ($start = $startPage; $start < $endPage; $start++) {
                $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $start;
                $response = $this->apiService->fetchData($apiAddress);

                if (!array_key_exists('data', $response) || empty($response['data'])) {
                    continue;
                } else {
                    $allProducts = array_merge($allProducts, $response['data']);
                    $this->apiService->ch = curl_init();
                }
            }

            $this->apiService->changeAttributeEnglishToDe($allProducts);

            $credentials->update([
              'next_import_page' => $endPage,
            ]);

            Log::info(count($allProducts));
            Log::info($credentials->next_import_page);

        } catch (\Exception $e) {
            Log::info($e);
        }
    }

    public function getProductById ($id)
    {
        $product = Product::where('api_id',1)->where('id',$id)->first();
        $address =  $this->addresses['FETCH_PRODUCT_BY_ID'].$product->api_product_id;
        $response = $this->apiService->fetchData($address);

        dd($response);

        if (!isset($response['data']['id'])) {
            return response()->json($response);
        }


        // $allProducts = [];
        // $allProducts[] = array_merge($allProducts, $response['data']);
        // $response = $this->apiService->pushProductsFromApi($allProducts);
        // return $response;
    }

    public function productListArguments()
    {
        try {
            $createdCount = 0;
            $updatedCount = 0;
            $address        = $this->addresses['LIST_ALL_SEGMENT'];
            $response       = $this->apiService->fetchData($address);
            if (!empty($response['data'])) {
                $allSegments    = $response['data'];
            } else {
                return $response;
            }
            foreach ( $allSegments as $segment ) {
                $s = BikeApiProductSegment::updateOrCreate(['code'=>$segment['code']], [
                    'code'      => $segment['code'],
                    'name'      => $segment['name'],
                    'status'    => 1,
                ]);
                if (!$s->wasRecentlyCreated && $s->wasChanged()) {
                    $updatedCount++;
                }
                if ($s->wasRecentlyCreated) {
                    $createdCount++;
                }
            }
            return [
                'new_segment'       => $createdCount,
                'modified_segment'  => $updatedCount,
                'all_segments'      => $allSegments,
            ];
        } catch (\Exception $e) {
            dd($e->getMessage());
        }

    }

    public function productsPerSegment($segment)
    {
        $address = $this->addresses['LIST_PRODUCTS_PER_SEGMENT'].$segment;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }



    public function v2FetchChangedProductsForNDays($segment_code, $days =1)
    {
        $address = $this->addresses['V2_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS'].$segment_code.'/changes/'.$days;

        $response = $this->apiService->fetchData($address);
        $allProducts = [];
        try {
            if ( !array_key_exists('data',$response) || empty($response['data']) ) {
                Log::info("Terminated ! No products found.".$segment_code);
            } else {
                $allProducts = array_merge($allProducts, $response['data']);
                if ( $response['meta']['last_page'] > 0 ) {
                    $startPage = $response['meta']['current_page']+1;
                    $lastPage  = $response['meta']['last_page'];
                    for ( $s = $startPage; $s <= $lastPage; $s++ ) {
                        $addr = $address.'?page='.$s;
                        $resp =  $this->apiService->fetchData($addr);
                        $allProducts = array_merge($allProducts, $resp['data']);

                        $this->apiService->ch = curl_init();
                    }
                    $insertInfo = $this->apiService->pushProductsFromApi($allProducts);
                    Log::info("Bike api products update n day change");
                }

            }
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }


    // END:: Products
    // END:: Products

    // START:: Stock
    // START:: Stock
    public function fetchStockChangesInTheLastNMinutes($minutes = null)
    {
        $row = $this->apiService->apiCredentials;

        $now =  \Carbon\Carbon::now()->toDateTimeString();
        $now =  \Carbon\Carbon::parse($now);

        $lastStockUpdatedAt = \Carbon\Carbon::parse($row->stock_updated_at ?? '2020-01-01');
        $minutes =  $now->diffInMinutes($lastStockUpdatedAt) + 1;

        $address = $this->addresses['FETCH_STOCK_CHANGES_IN_LAST_MINUTES'].$minutes;
        $response = $this->apiService->fetchData($address);

        $updatedProductsCount = 0;
        $updatedProductsIds   = [];

        try {
            if (is_array($response) && !empty($response) > 0) {
                $collection = collect($response);
                foreach ( $collection->chunk(100) as $takeChunk ) {
                    foreach ( $takeChunk as $eachOfChunk ) {
                        $product = Product::where('api_product_id', $eachOfChunk['id'])->first();

                        if ( $product && ($product->stock != $eachOfChunk['stock']) ) {
                            $oldStock = $product->old_stock;

                            $product->stock             = $eachOfChunk['stock'];
                            $product->old_stock        = $product->stock;
                            $product->stock_updated_at  = \Carbon\Carbon::now();

                            $product->update();
                            Log::info("Bike Api Stock Update: ". $product->ean);
                            $drmProducts = $product->drmProducts()->get();

                            if(count($drmProducts) > 0){
                                $data['stock'] = $eachOfChunk['stock'];
                                app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drmProducts,$data);
                            }


                            $drmProductIds = [];
                            foreach ($drmProducts as $drmProduct) {
                                $drmProductIds[] = $drmProduct->id;
                            }
                            // Keeping History
                            \App\Models\Marketplace\ApiStockSyncHistory::create([
                                'api_id' => \App\Enums\Marketplace\ApiResources::BIKE_API_ID,
                                'mp_product_id' => $product->id,
                                'bike_api_id' => $product->api_product_id,
                                'drm_product_ids' => $drmProductIds,

                                'mp_updated_atock' => $eachOfChunk['stock'],
                                'drm_updated_stock' => $eachOfChunk['stock'],

                                'sync_time' => \Carbon\Carbon::now(),
                                'status' => 1,
                                'old_stock' => $oldStock,
                            ]);
                            $updatedProductsCount++;
                            $updatedProductsIds[] = $product->id;
                        } else {
                            continue;
                        }
                    }
                }
            }
            $row->update([
                'stock_updated_at' => $now,
            ]);
            Log::info($updatedProductsCount, $updatedProductsIds);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'msg'    => $e->getMessage(),
            ]);
        }
        // return response()->json([
        //     'updated_products_count' => $updatedProductsCount,
        //     'updated_products_ids' => $updatedProductsIds,
        // ]);
    }

// Fetch Stock of a single products
    public function fetchStockPerProductById($id)
    {
        $address = $this->addresses['FETCH_STOCK_PER_PRODUCT'].$id;
        $response = $this->apiService->fetchData($address);

        try {
            if ( is_array($response) && !empty($response) ) {
                $product = \App\Models\Marketplace\Product::where('api_product_id', $response['product_id'])->first();

                // $stockInfo = $product->stock_info;
                $product->stock               = $response['stock'];
                $product->old_stock           = $product->stock;
                $product->stock_updated_at    = \Carbon\Carbon::now();
                $product->update();

                $drmProducts = $product->drmProducts()->get();
                // foreach ($drmProducts as $drmProduct) {
                //     $drmProduct->update([
                //         'stock' => $response['stock'],
                //         'old_stock' => $drmProduct->stock,
                //         'stock_updated_at' => \Carbon\Carbon::now(),
                //     ]);
                // }

                if(count($drmProducts) > 0){
                    $data['stock'] = $response['stock'];
                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drmProducts,$data);
                }

            }
            return response()->json([
                'update_status' => 'success',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ]);
        }
    }
    // END:: Stock
    // END:: Stock




    // START:: Tracking codes
    // START:: Tracking codes
    public function listAllTrackingCodeForUser()
    {
        $address = \App\Enums\Marketplace\ApiResources::ADDRESSES['LIST_ALL_TRACKING_CODES_FOR_USER'];
        $response = $this->apiService->fetchData($address);
        dd($response);
    }

    public function fetchTrackingCodeById($id)
    {
        try {
            $address = $this->addresses['FETCH_TRACKING_CODE_BY_ID'].$id;
            $apiOrder = \App\Models\Marketplace\ApiOrdersResponse::where('order_id', $id)->first();
            $drmOrder = \App\Models\DrmOrder::find($apiOrder->drm_order_id);

            $response = $this->apiService->fetchData($address);

            /*
                $response['data'] = [
                    'tracking_code' => '45345',
                    'carrier'       => 'DHL',
                    'shipment_type' => 'FRT',
                    'status'        => null,
                    'status_info'   => null,
                ];
            */

            if (isset($response['data'])) {

                $parcel = $response['data']['carrier'];
                $userParcelQuery = \App\Models\UserParcelService::where('parcel_name', $parcel)
                                ->where('cms_user_id', $drmOrder->cms_user_id);


                if ($userParcelQuery->exists()) {
                    $userParcelInfo = $userParcelQuery->first();
                } else {
                    $userParcelInfo = \App\Models\UserParcelService::create([
                        'cms_user_id' => $drmOrder->cms_user_id,
                        'parcel_name' => $parcel,
                    ]);
                }

                $newOrderTracking = \App\Models\OrderTracking::where([
                    'order_id'          => $drmOrder->id,
                    'user_id'           => $response['data']['tracking_code'],
                    'package_number'    => $userParcelInfo->id
                ])->first();

                if (!$newOrderTracking) {
                    $newOrderTracking = \App\Models\OrderTracking::create ([
                        'order_id'          => $drmOrder->id,
                        'user_id'           => $drmOrder->cms_user_id,
                        'package_number'    => $response['data']['tracking_code'],
                        'parcel_id'         => $userParcelInfo->id,
                    ]);
                }

                return response()->json(['tracking_infos' => $newOrderTracking]);

            } else {
                return response()->json([
                    'info' => 'Tracking code empty'
                ]);
            }
        } catch (\Exception $e) {
            dd($e);
        }
    }
    // END:: Tracking Codes
    // END:: Tracking Codes



    // START:: Orders
    // START:: Orders
    public function listAllOrdersForUser()
    {
        $address = $this->addresses['LIST_ALL_ORDERS'];
        $response = $this->apiService->fetchData($address);

        return $response;
    }

    public function fetchOrderById($id)
    {
        $address = $this->apiAddresses['FETCH_ORDER_BY_ID'].$id;
        $response = $this->apiService->fetchData($address);
    }

    // Submit order ( Parameter is the drm order ID )
    public function submitAnOrder($orderId)
    {
        $errorReasons = [];
        $address = $this->addresses['SUBMIT_AN_ORDER'];
        $deliveryCompanyId      = \App\Enums\Marketplace\ApiResources::BIKE_API_DELIVERY_COMPANY_ID;

        $drmOrder = \App\Models\DrmOrder::find($orderId);

        if (!$drmOrder) {
            return response()->json([
                'error'     => 'Order not found.',
            ]);
        }
        $customerInfo = getAndProcessCustomerInfosFromDrmOrderForBikeApiOrder($drmOrder);
        $customerInfo = json_encode($customerInfo);

        // Pattern :: ['product_id' => 'Quantity']
        $productQtyArr = collect(json_decode($drmOrder->cart, 1))->pluck('qty', 'product_id');

        $productIdsOfOrderCart = array_column(json_decode($drmOrder->cart, 1), 'product_id');

        // B2bUhrenApi order
        app(\App\Http\Controllers\Marketplace\B2bUhrenApiController::class)->addProductToBasket($drmOrder,$customerInfo);
        // BDroppyApi order
        app(\App\Http\Controllers\Marketplace\BDroppyApiController::class)->orderBDroppyProduct($drmOrder,$customerInfo);
        // BigBuyApi order
        app(\App\Http\Controllers\Marketplace\BigBuyApiController::class)->orderBigBuyProduct($drmOrder,$customerInfo);



        // Get All Bike api products from order
        $drmOrderProducts = \App\Models\DrmProduct::whereIn(
                                'id', $productIdsOfOrderCart
                            )->whereNotNull('marketplace_product_id')
                             ->get();

        // Checking minimun 1 product is found or not
        if (count($drmOrderProducts) == 0) {
            $errorReasons[] = 'There is no products synced with API';
            return response()->json([
                'error'     => 'Suitable products not found.',
                'reason'    => $errorReasons,
            ]);
        }




        // Make a product infos array by specific condition of api products
        $productInfos = [];
        foreach ($drmOrderProducts as $drmOrderProduct) {

            // $apiProduct = \App\Models\Marketplace\Product::find($drmOrderProduct->marketplace_product_id); //Need to optimize
            $apiProduct = \App\Models\Marketplace\Product::where([
                'id'=>$drmOrderProduct->marketplace_product_id,
                'api_id' => \App\Enums\Marketplace\ApiResources::BIKE_API_ID
                ]
            )->first(); //Need to optimize

            if (isset($apiProduct) && isset($apiProduct->api_product_id)) {
                $qty = $productQtyArr[$drmOrderProduct->id];
                $stock  = $this->getStockOfSingleProduct($apiProduct->api_product_id);

                if ($qty <= $stock) {
                    $productInfos[] = [
                        'amount'    => $qty,
                        'product_id'=> $apiProduct->api_product_id,
                        'assembly'  => false,
                    ];
                } else {
                    $errorReasons[] = 'Product id '.$drmOrderProduct->id.' - max amount exceded. Max valid amount '.$stock;
                    continue;
                }
            } else {
                continue;
            }

        }

        // Checing at least 1 product is available
        if (!count($productInfos)) {
            return response()->json([
                'error'     => 'Suitable products not found.',
                'reason'    => $errorReasons,
            ]);
        }


        $referenceNumber = "DRM-".$orderId; // order unique reference number
        $order = json_encode([
            "reference" => $referenceNumber,
            "orderlines" => $productInfos
        ]);


        $orderInfo = '{
          "customer": '.$customerInfo.',
          "order": '.$order.'
        }';

        if(!isset($_GET['send']))
        dd($orderInfo);


        $response = $this->apiService->fetchData($address, 'POST', $orderInfo);

        // Checcking we reached or not the orders info
        if (!isset($response['data'])) {

            // $drm_data = [
            //     'token'         => "Zd6tQv8Cvd",
            //     'order_id'      => $orderId,
            //     'message'       => $response,
            //     'parcel_number' => '',
            //     'parcel_service'=> '',
            //     'status'        => 'exception',
            //     'date'          => Carbon::now()
            // ];

            // $drm_data = json_encode($drm_data);
            // app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json([
                'error'    => 'api_refused',
                'response' => $response,
            ]);
        }

        $placedOrderInfo = $response['data'];
        // making attributes for storing order response
        $drm_data = [
            'token'         => "Zd6tQv8Cvd",
            'order_id'      => $orderId,
            'message'       => 'Order placed to marketplace API Successfully!',
            'parcel_number' => '',
            'parcel_service'=> '',
            'status'        => 'transfer',
            'date'          => Carbon::now()
        ];

        $drm_data = json_encode($drm_data);
        app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

        $orderAttributes = [
            'drm_order_id'  => $orderId,
            'api_id'        => 1,
            'order_id'      => $placedOrderInfo['order_id'] ?? '',
            'reference_no'      => $placedOrderInfo['reference'] ?? '',
            'invoice_number'    => $placedOrderInfo['invoice_number'] ?? '',
            // 'tracking_codes'    => $placedOrderInfo['tracking_codes'] ?? '',
            'shipping_cost'     => $placedOrderInfo['shipping_cost'] ?? '',
            'total'             => $placedOrderInfo['total'] ?? '',
            'customer_infos'    => [
                                     'name'     => $placedOrderInfo['name'] ?? '',
                                     'street'   => $placedOrderInfo['street'] ?? '',
                                     'houseno'  => $placedOrderInfo['houseno'] ?? '',
                                     'postcode' => $placedOrderInfo['zipcode'] ?? '',
                                     'city'     => $placedOrderInfo['city'] ?? '',
                                     'country'  => $placedOrderInfo['country'] ?? '',
                                   ],
            'product_infos'     => $placedOrderInfo['orderlines'] ?? '',
            'misc'              => $placedOrderInfo,
            'status'            => 1,
            'order_date'        => \Carbon\Carbon::now(),
        ];

        Log::info($orderAttributes);

        $insertedOrder = \App\Models\Marketplace\ApiOrdersResponse
                            ::create($orderAttributes);

        if ($insertedOrder) {
            return response()->json([
                'staus' => 'order_submited',
                'msg'   => 'Order has been submitted successfully',
                'order_info'    => $insertedOrder,
            ]);

        } else {
            $errorReasons[] = 'Order reference '.$referenceNumber.' can not be saved in Database.';
            return response()->json([
                'error' => 'Can not be saved in database',
                'reason'=>  $errorReasons,
            ]);
        }

    }


    public function fetchOrdersForlastNDays($days)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/orders/latest/7';
        $response = $this->apiService->fetchData($address);
        dd($response);
    }
    // END:: Orders
    // END:: Orders


    public function fallbackProductSync() {
        $row = $this->apiService->apiCredentials;

        $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'].'?per_page=700';
        $response = $this->apiService->fetchData($apiAddress);

        $results = $this->apiService->pushProductsFromApi($response['data'], 20);

        $row->update([
            'next_import_page' => $response['meta']['current_page']+1,
        ]);
    }


    public function getStockOfSingleProduct ($id)
    {
        $address = $this->addresses['FETCH_PRODUCT_BY_ID'].$id;
        $response = $this->apiService->fetchData($address);
        
        return (int)$response['data']['stock'] ?? 0;
    }


    public function getUser($user)
    {
        $user = \App\User::find($user);
        dd($user);
    }

    public function changeAttrInDe($productId)
    {
        $product = \App\Models\Marketplace\Product::findOrFail($productId);
        if ($product->api_product_id > 0) {
            $apiProduct = $this->getProductById($product->api_product_id);
        } else {
            return 'Product is not bike api product';
        }

        $allAttributes = $apiProduct['attributes'];

        // START:: PRODUCT MISC
        $itemColors = '';
        $materials  = '';
        $gender     = '';
        $attributes = $apiProduct['attributes'];
        foreach ($attributes as $attribute) {
            if ($attribute['name_en'] == 'Colour') $itemColors .= $attribute['value_de'].',';
            if ($attribute['name_en'] == 'Material') $materials .= $attribute['value_de'].',';
            if ($attribute['name_en'] == 'Gender') $gender .= $attribute['value_de'].',';
        }

        $attr = [];
        $attr['materials'] = substr($materials, 0, -1);
        $attr['item_color'] = substr($itemColors, 0, -1);
        $attr['gender'] = substr($gender, 0, -1) ?? '';

        $product->update($attr);
    }

    public function insertApiSyncHistory($page,$response,$sync_report_id){

        $api_sync_history = new MarketplaceApiSyncHistory();
        $api_sync_history->page_no = $page;
        $api_sync_history->api_id = \App\Enums\Marketplace\ApiResources::BIKE_API_ID;
        $api_sync_history->start_time = \Carbon\Carbon::now();
        $api_sync_history->response = $response;
        $api_sync_history->sync_report_id = $sync_report_id;
        $api_sync_history->save();
    }

    public function sendOrder($products,$customer_info,$order_info){

        $required_field = ["first_name","last_name","street","zipcode","city" ,"country" ,"address"];
        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, BIKE API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'],422);
        }

        $customerInfo = json_encode($customer_info);

        $productInfos = [];
        foreach($products as $product){

            $qty        = $product['qty'];
            $api_stock  = $this->getStockOfSingleProduct($product['api_product_id']);

            if($qty <= $api_stock){
                $productInfos[] = [
                    'amount'    => $qty,
                    'product_id'=> $product['api_product_id'],
                    'assembly'  => false,
                ];
            } else {

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, BIKE API Products have no stock - '. $product['ean'],
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'error_level'   => 'STOCK',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status' => 'error', 'message' => 'max quantity exceded'],422);
            }
        }

        if(count($productInfos) > 0){
            $referenceNumber = "DRM-".$order_info['order_id'];
            $order = json_encode([
                "reference"  => $referenceNumber,
                "orderlines" => $productInfos
            ]);

            $orderInfo = '{
                "customer": '.$customerInfo.',
                "order": '.$order.'
              }';

            if(!isLocal()){
                $address = $this->addresses['SUBMIT_AN_ORDER'];
                $response = $this->apiService->newBuildRequest($address, 'POST', $orderInfo);
                
                $placedOrderInfo = [];
                if(isset($response) && $response['status_code'] == 200 || $response['status_code'] == 201 ){

                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order placed to marketplace API Successfully!',
                        'parcel_number' => '',
                        'parcel_service'=> '',
                        'status'        => 'transfer',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $placedOrderInfo = $response['data'];
                    $orderAttributes = [
                        'drm_order_id'   => $order_info['order_id'],
                        'api_id'         => 1,
                        'order_id'       => $placedOrderInfo['order_id'] ?? 0,
                        'reference_no'   => $placedOrderInfo['reference'] ?? '',
                        'invoice_number' => $placedOrderInfo['invoice_number'] ?? '',
                        'shipping_cost'  => $placedOrderInfo['shipping_cost'] ?? 0,
                        'total'          => $placedOrderInfo['total'] ?? 0,
                        'customer_infos' => [
                                            'name'     => $placedOrderInfo['name'] ?? '',
                                            'street'   => $placedOrderInfo['street'] ?? '',
                                            'houseno'  => $placedOrderInfo['houseno'] ?? '',
                                            'postcode' => $placedOrderInfo['postcode'] ?? '',
                                            'city'     => $placedOrderInfo['city'] ?? '',
                                            'country'  => $placedOrderInfo['country']
                                        ],
                        'product_infos'   => $placedOrderInfo['orderlines'] ?? '',
                        'misc'            => $placedOrderInfo,
                        'status'          => 1,
                        'order_date'      => \Carbon\Carbon::now(),
                    ];

                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], ($placedOrderInfo['order_id'] ?? 0));

                    return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'],200);

                }else if($response['status_code'] == 403 ){

                    $placedOrderInfo = $response['data'] ?? [];
                    $orderAttributes = [
                        'drm_order_id'   => $order_info['order_id'],
                        'api_id'         => 1,
                        'order_id'       => $placedOrderInfo['order_id'] ?? 0,
                        'reference_no'   => $placedOrderInfo['reference'] ?? '',
                        'invoice_number' => $placedOrderInfo['invoice_number'] ?? '',
                        'shipping_cost'  => $placedOrderInfo['shipping_cost'] ?? 0,
                        'total'          => $placedOrderInfo['total'] ?? 0,
                        'customer_infos' => [
                                            'name'     => $placedOrderInfo['name'] ?? '',
                                            'street'   => $placedOrderInfo['street'] ?? '',
                                            'houseno'  => $placedOrderInfo['houseno'] ?? '',
                                            'postcode' => $placedOrderInfo['postcode'] ?? '',
                                            'city'     => $placedOrderInfo['city'] ?? '',
                                            'country'  => $placedOrderInfo['country'] ?? ''
                                        ],
                        'product_infos'   => $placedOrderInfo['orderlines'] ?? '',
                        'misc'            => $response ?? '',
                        'status'          => 2,
                        'order_date'      => \Carbon\Carbon::now(),
                    ];

                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order Transfer Failed, BIKE API Not allowed to place order, Access denied. Bike API',
                        'parcel_number' => '',
                        'parcel_service'=> '',
                        'status'        => 'exception_delivery',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    return response()->json(['status' => 'error', 'message' => 'Order Transfer Failed'],403);

                }else if($response['status_code'] != 200 && $response['status_code'] != 201 && $response['status_code'] != 403){

                    $placedOrderInfo = $response['data'] ?? [];
                    $orderAttributes = [
                        'drm_order_id'   => $order_info['order_id'],
                        'api_id'         => 1,
                        'order_id'       => $placedOrderInfo['order_id'] ?? 0,
                        'reference_no'   => $placedOrderInfo['reference'] ?? '',
                        'invoice_number' => $placedOrderInfo['invoice_number'] ?? '',
                        'shipping_cost'  => $placedOrderInfo['shipping_cost'] ?? 0,
                        'total'          => $placedOrderInfo['total'] ?? 0,
                        'customer_infos' => [
                                            'name'     => $placedOrderInfo['name'] ?? '',
                                            'street'   => $placedOrderInfo['street'] ?? '',
                                            'houseno'  => $placedOrderInfo['houseno'] ?? '',
                                            'postcode' => $placedOrderInfo['postcode'] ?? '',
                                            'city'     => $placedOrderInfo['city'] ?? '',
                                            'country'  => $placedOrderInfo['country'] ?? ''
                                        ],
                        'product_infos'   => $placedOrderInfo['orderlines'] ?? '',
                        'misc'            => $response ?? '',
                        'status'          => 2,
                        'order_date'      => \Carbon\Carbon::now(),
                    ];

                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order Transfer Failed, BIKE API',
                        'parcel_number' => '',
                        'parcel_service'=> '',
                        'status'        => 'exception_delivery',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    return response()->json(['status' => 'error', 'message' => 'Order Transfer Failed'],422);
                }
            }
        }
    }

    public function getApiTracking($api_order_response){
        info("bike api tracking sync start............");
        foreach($api_order_response as $key => $order){

            $address  = $this->addresses['FETCH_TRACKING_CODE_BY_ID'].$order->order_id;
            $response = $this->apiService->fetchData($address);

            if (!empty($response['data'])) {
                foreach($response['data'] as $res){
                    if(!empty($res['tracking_code'])){
                        $bike_api_tracking = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order->drm_order_id,
                            'message'       => 'order shipped',
                            'parcel_number' => $res['tracking_code'],
                            'parcel_service'=> $res['carrier'],
                            'status'        => 'shipped',
                            'date'          => Carbon::now()
                        ];

                        $bike_api_tracking = json_encode($bike_api_tracking);
                        app(ProductService::class)->sendOrderTrackingToDRM($bike_api_tracking);
                        $order->tracking_codes = $res['tracking_code'];
                    }
                }

                $order->status = 3;
                $order->update();
            }
        }
        info("Bike api tracking sync done...............");
    }
}
