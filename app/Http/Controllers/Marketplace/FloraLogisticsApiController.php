<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;

class FloraLogisticsApiController extends Controller
{
    public function sendOrder($products,$customer_info,$order_info){
        
        $required_field = ["first_name","last_name","email","house_no","street","zipcode","city" ,"country"];

        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, FLORA LOGISTICS API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'],422);
        }


        $shipping_data = [
            "type" => "Order",
            "attributes" => [
                "reference" => (string)$order_info['order_id'],
                "phone" => $customer_info['phone'],
                "email" => $customer_info['email'],
                "shipping_address" => [
                    "company_name" => empty($customer_info['company']) ? $customer_info['first_name'].' '.$customer_info['last_name'] : $customer_info['company'],
                    "first_name"   => $customer_info['first_name'],
                    "last_name"    => $customer_info['last_name'],
                    "street_name"  => $customer_info['street'],
                    "house_number" => $customer_info['house_no'],
                    "zip_code"     => $customer_info['zipcode'],
                    "city"         => $customer_info['city'],
                    "country_code" => $customer_info['country'],
                ],
            ],
        ];
        
        $products_array = [];

        foreach($products as $product){

            $products_array[] = [
                "type"  => "OrderLine",
                "attributes" => [
                    "quantity" =>  (int)$product['qty'],
                ],
                "relationships" => [
                    "product" => [
                        "data" => [
                            "type" => "Product",
                            "id"   => $product['api_product_id'],
                        ],
                    ],
                ],
            ];
        }

        $order_data = [
            "data" => $shipping_data,
            "included" => $products_array,
        ];

        if(count($products_array) > 0){
            
            $order_data = json_encode($order_data);

            if(!isLocal()){
                $base_url = \App\Enums\Marketplace\ApiResources::FloraLogistics['ORDER_URL'];
                $url = $base_url.'?api_key=1hZJmqpvpIWYXu8Qd1InvkyzKjuSzHYCznMZGpyuDoy1MHDrCiiZhIXtaOkMwLBhoNxUH3xGQMg9nPei0z2uNX5FoC8paA6f6Ro9';
                
                $res = $this->buildRequest($url,$order_data,'POST');
            }else{
                dd($order_data);
            }
            if($res['status_code'] == 201){
                $orderAttributes = [
                            'drm_order_id'  => $order_info['order_id'],
                            'api_id'        => \App\Enums\Marketplace\ApiResources::FloraLogistics['API_ID'],
                            'order_id'      => 0,
                            'invoice_number'=> $order_info['invoice_number'] ?? 0,
                            'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                            'total'         => $order_info['total'] ?? 0,
                            'customer_infos'=> [
                                                'name'     => $full_name ?? '',
                                                'street'   => $customer_info['street'] ?? '',
                                                'houseno'  => $customer_info['house_no'] ?? '',
                                                'postcode' => $customer_info['zipcode'] ?? '',
                                                'city'     => $customer_info['city'] ?? '',
                                                'country'  => $customer_info['country'] ?? '',
                                                ],
                            'product_infos' => $products_array ?? '',
                            'misc'          => $res ?? '',
                            'status'        => 1,
                            'order_date'    => \Carbon\Carbon::now(),
                        ];

                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order placed to marketplace API Successfully!',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'transfer',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], 0);
                return response()->json(['status'=>'success','message'=>'Order Successfully Transfer'],200);

            }else{

                $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::FloraLogistics['API_ID'],
                    'order_id'      => 0,
                    'invoice_number'=> $order_info['invoice_number'] ?? 0,
                    'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                    'total'         => $order_info['total'] ?? 0,
                    'customer_infos'=> [
                                        'name'     => $full_name ?? '',
                                        'street'   => $customer_info['street'] ?? '',
                                        'houseno'  => $customer_info['house_no'] ?? '',
                                        'postcode' => $customer_info['zipcode'] ?? '',
                                        'city'     => $customer_info['city'] ?? '',
                                        'country'  => $customer_info['country'] ?? '',
                                        ],
                    'product_infos' => $products_array ?? '',
                    'misc'          => $res ?? '',
                    'status'        => 2,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, FLORA LOGISTICS API',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status'=>'error','message'=>'Order Transfer Failed'],$res['status_code']);
            }

        }
    }

    private function buildRequest ($api_url,$data=[], $method='GET') {
        $headers = array(
            'Content-Type: application/json'
        );
        $ch     = curl_init();
        curl_setopt($ch, CURLOPT_URL,$api_url);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_TIMEOUT, 400);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, $method=="POST"?1:0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if ( !empty($data) && $method=="POST" ) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }

        $resp['data'] = curl_exec($ch);
        $resp['status_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $resp;
    }

    public function getApiTracking($api_order_response){

        info('flora tracking start.........');
        foreach($api_order_response as $key => $order){

            if($key > 0){
                sleep(1);
            }

            if($order->drm_order_id && $order->api_id == \App\Enums\Marketplace\ApiResources::FloraLogistics['API_ID']){

                $base_url = \App\Enums\Marketplace\ApiResources::FloraLogistics['TRACKING_URL'];
                $url = $base_url.$order->drm_order_id.'&api_key=1hZJmqpvpIWYXu8Qd1InvkyzKjuSzHYCznMZGpyuDoy1MHDrCiiZhIXtaOkMwLBhoNxUH3xGQMg9nPei0z2uNX5FoC8paA6f6Ro9';
                
                $tracking_codes = $this->buildRequest($url);
                if($tracking_codes['status_code'] == 200){
                    $tracking_datas = json_decode($tracking_codes['data'],true);
                    if(!blank($tracking_datas['data'])){
                        foreach($tracking_datas['data'] as $tracking_data){
                            if(!blank($tracking_data['attributes']['track_and_trace_number']) && !blank($tracking_data['attributes']['method'])){
                                
                                $drm_data = [
                                    'token'         => "Zd6tQv8Cvd",
                                    'order_id'      => $order->drm_order_id,
                                    'message'       => 'order shipped',
                                    'parcel_number' => $tracking_data['attributes']['track_and_trace_number'] ?? '',
                                    'parcel_service'=> $tracking_data['attributes']['method'] ?? '',
                                    'status'        => 'shipped',
                                    'date'          => Carbon::now()
                                ];

                                $drm_data = json_encode($drm_data);
                                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                                $order->tracking_codes = $tracking_data['attributes']['track_and_trace_number'] ?? '';
                                $order->status = 3;
                                $order->update();
                            }
                        };
                    }else{
                        info("flora tracking not found");
                    }
                }
            }
        }
        info('flora tracking end.........');
    }
}
