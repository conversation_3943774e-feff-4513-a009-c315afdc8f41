<?php

namespace App\Http\Controllers\Marketplace;

use App\Enums\Marketplace\ApiResources;
use App\Http\Controllers\Controller;
use App\Services\Marketplace\BtsHolesalerApi\BTSHolesalerApiService;
use Illuminate\Support\Carbon;

class BTSWholesalerApiController extends Controller
{
    public function sendOrder($products,$customer_info,$order_info){
       
        $required_field = ["first_name","last_name","zipcode","city" ,"country" ,"address", "phone"];
        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, BTS WHOLESALE API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            
           app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'],422);
        }


        $products_params = [];
        foreach ($products as $key => $product) {
            $products_params["products[{$key}][sku]"] = $product['ean'];
            $products_params["products[{$key}][quantity]"] = (int)$product['qty'];
        }

        $address_params = [
            'address[country_code]' => $customer_info['country'],
            'address[postal_code]' => $customer_info['zipcode'],
        ];

        $shipping_url = ApiResources::BTSWholesalar['GET_SHIPPING_ID'] . '?' . http_build_query(array_merge($address_params, $products_params));
        
        $shipping_id = $this->getShippingId($shipping_url);

        if(count($products_params) > 0){
            $productInfo =  [
                'payment_method'  => 'wallet',
                'shipping_cost_id'=> $shipping_id,
                'client_name'     => $customer_info['first_name'] .' '.$customer_info['last_name'],
                'address'         => $customer_info['address'],
                'postal_code'     => $customer_info['zipcode'],
                'city'            => $customer_info['city'] ?? '',
                'country_code'    => $customer_info['country'] ?? '',
                'telephone'       => $customer_info['phone'] ?? '',
                'dropshipping'    => 1,
            ];

            if(!isLocal()){
                $order_send_url = ApiResources::BTSWholesalar['CREATE_ORDER'];
                $order_data     = array_merge($productInfo,$products_params);

                $response       = app(BTSHolesalerApiService::class)->fetchData($order_send_url,'POST',http_build_query($order_data));
                
            if(isset($response) && $response['status_code'] == 200 ){

                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order placed to marketplace API Successfully!',
                        'parcel_number' => '',
                        'parcel_service'=> '',
                        'status'        => 'transfer',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], 0);

                    $orderAttributes = [
                        'drm_order_id'  => $order_info['order_id'] ?? 0,
                        'api_id'        => ApiResources::BTSWholesalar['API_ID'],
                        'order_id'      =>  0,
                        'invoice_number'=> $order_info['invoice_number'] ?? 0,
                        'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                        'total'         => $order_info['total'] ?? 0,
                        'customer_infos'=> [
                            'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                            'street'   => $customer_info['street'] ?? '',
                            'houseno'  => $customer_info['house_no'] ?? '',
                            'postcode' => $customer_info['zipcode'] ?? '',
                            'city'     => $customer_info['city'] ?? '',
                            'country'  => $customer_info['country'] ?? '',
                        ],
                        'product_infos' => $order_data ?? '',
                        'misc'          => $response ?? '',
                        'status'        => 1,
                        'order_date'    => \Carbon\Carbon::now(),
                    ];

                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);
                   
                    return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'],200);

                }else if(isset($response) && $response['status_code'] != 400 ){

                    $orderAttributes = [
                        'drm_order_id'  => $order_info['order_id'] ?? 0,
                        'api_id'        => ApiResources::BTSWholesalar['API_ID'],
                        'order_id'      => $response['order_id'] ?? 0,
                        'invoice_number'=> $order_info['invoice_number'] ?? 0,
                        'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                        'total'         => $order_info['total'] ?? 0,
                        'customer_infos'=> [
                            'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                            'street'   => $customer_info['street'] ?? '',
                            'houseno'  => $customer_info['house_no'] ?? '',
                            'postcode' => $customer_info['zipcode'] ?? '',
                            'city'     => $customer_info['city'] ?? '',
                            'country'  => $customer_info['country'] ?? '',
                        ],
                        'product_infos' => $order_data ?? '',
                        'misc'          => $response ?? '',
                        'status'        => 2,
                        'order_date'    => \Carbon\Carbon::now(),
                    ];
                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    if($response['code'] == 'ER003'){
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed, BTS WHOLESALE API Products have no stock - '. $response['order_id']['message'] ?? '',
                            'parcel_number' => '',
                            'parcel_service'=> '',
                            'error_level'   => 'STOCK',
                            'status'        => 'exception',
                            'date'          => Carbon::now()
                        ];
                    }else{
                        $drm_data = [
                            'token'         => "Zd6tQv8Cvd",
                            'order_id'      => $order_info['order_id'],
                            'message'       => 'Order Transfer Failed,',
                            'parcel_number' => '',
                            'parcel_service'=> '',
                            'status'        => 'exception_delivery',
                            'date'          => Carbon::now()
                        ];
                    }


                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    return response()->json(['status' => 'error', 'message' => 'Order Transfer Failed'],200);
                
                }else if(isset($response) && $response['status_code'] == 400 ){

                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order_info['order_id'],
                        'message'       => 'Order Transfer Failed, BTS WHOLESALE API No Enough Money in wallet',
                        'parcel_number' => '',
                        'parcel_service'=> '',
                        'status'        => 'exception',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);


                    $orderAttributes = [
                        'drm_order_id'  => $order_info['order_id'] ?? 0,
                        'api_id'        => ApiResources::BTSWholesalar['API_ID'],
                        'order_id'      => 0,
                        'invoice_number'=> $order_info['invoice_number'] ?? 0,
                        'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                        'total'         => $order_info['total'] ?? 0,
                        'customer_infos'=> [
                            'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                            'street'   => $customer_info['street'] ?? '',
                            'houseno'  => $customer_info['house_no'] ?? '',
                            'postcode' => $customer_info['zipcode'] ?? '',
                            'city'     => $customer_info['city'] ?? '',
                            'country'  => $customer_info['country'] ?? '',
                        ],
                        'product_infos' => $order_data ?? '',
                        'misc'          => $response ?? '',
                        'status'        => 2,
                        'order_date'    => \Carbon\Carbon::now(),
                    ];
                    \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                    return response()->json(['status' => 'error', 'message' => 'Order Transfer Failed'],200);

                }
            }else{
                dd("localy order transfer failed");
            }
        }
    }

    private function getShippingId($shipping_url){
        $shipping_response = app(BTSHolesalerApiService::class)->fetchData($shipping_url);
        
        if(isset($shipping_response) && $shipping_response['status_code'] == 200 ){
            $shipping_data = $shipping_response['data'];
            $shippingCosts = array_column($shipping_data, 'shipping_cost');
            $lowestIndex = array_search(min($shippingCosts), $shippingCosts);

            return $shipping_data[$lowestIndex]['id'];
        }else{
            dd("order transfer failed for shipping cost id problem");
        }

    }
}
