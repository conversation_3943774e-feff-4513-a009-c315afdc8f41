<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Interfaces\B2bUhrenApiInterface;
use App\Models\DrmProduct;
use App\Models\Marketplace\B2bUhrenApiBrand;
use App\Models\Marketplace\Product;
use App\Services\Marketplace\B2bUhrenApi\B2bUhrenApiService;
use App\Services\Marketplace\CsvService;
use Illuminate\Support\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Marketplace\AllApiUpdateSchedule;
use DB;

class B2bUhrenApiController extends Controller implements B2bUhrenApiInterface
{
    public function brandSync(){
        return true;
        $brands = app(B2bUhrenApiService::class)->getBrands();
        $brands = json_decode($brands);
        if(isset($brands->rows)){
            foreach($brands->rows as $brand){
                $data = [
                    'id_brand' => $brand->id_brand,
                    'name' => $brand->name,
                    'group' => $brand->group,
                    'category' => $brand->category,
                ];
                B2bUhrenApiBrand::updateOrCreate(['id_brand' => $data['id_brand']], $data);
            }
            Log::info("B2bUhrenApi Brand sync Done!");
        }else{
            Log::error("B2buhren Brand Not found");
        }

    }
    /**
     * get product by brand
     * @return products
     */
    public function getProductsByBrand($brandId){
        return true;
        $products = app(B2bUhrenApiService::class)->getProductByBrand($brandId);
        $products = json_decode($products);

        return $products;
    }

    public function fetchProductBYBrand($brand){
        $product = $this->getProductsByBrand($brand);
        dd($product);
    }

    public function getProductById($id){
        $product = app(B2bUhrenApiService::class)->getProductById($id);
        $product = json_decode($product);

        return $product;
    }

    public function getShippingCountry(){
        $shipping_contry = app(B2bUhrenApiService::class)->getShippingCountry();

        return $shipping_contry;
    }

    public function buildProductSyncJobs()
    {
        return true;
        ini_set('max_execution_time', '0');
        ini_set('memory_limit', -1);

        try {
            // Insert or update for next update time

            $api_id = 2;
            $update_time = \Carbon\Carbon::now()->addMinutes(5);
            app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);

            // $api_update_table = new AllApiUpdateSchedule();
            // $api_update_schedule = $api_update_table->where('api_id', 2)->first();
            // if($api_update_schedule){
            //     $api_update_schedule->next_update_time = \Carbon\Carbon::now()->addMinutes(5);
            //     $api_update_schedule->update();
            //     Log::info('B2Buheren api scheduler update for next update time');
            // }else{
            //     $attributes = [
            //         'api_id' 			 => 2,
            //         'next_update_time'	 => \Carbon\Carbon::now()->addMinutes(5),
            //     ];
            //     $api_update_table->create($attributes);
            //     Log::info('B2Buheren api scheduler create for next update time');
            // }
            // End insert or update for next update time
            // $brands = $this->getAllBrands();
            // $brands = json_decode($brands);
            $brands = B2bUhrenApiBrand::where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE)->take(40)->get();

            if(!empty($brands)){
                $items = array();
                $category = array();
                $cnt = 0;
                foreach ($brands as $key => $brand)
                {
                    $category[str_replace(' ', '', $brand->name)] = str_replace(' ', '', $brand->group);

                    $temp = $this->getProductsByBrand($brand->id_brand);

                    if(!empty($temp) && $temp->rows) {
                        $items = array_merge( $items, $temp->rows);
                    }
                    $brand->update([
                        'sync_status'   => \App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_DISABLE,
                        'sync_count'    => $brand->sync_count + 1,
                        'sync_time'     => time(),
                        'count_product' => isset($temp->rows)? (count($temp->rows) <= $brand->count_product?$brand->count_product:count($temp->rows)):$brand->count_product,
                    ]);
                }

                if(isset($items) && !empty($items)){
                    Log::info("B2bUhrenApi Product sync job dispatched!");
                    // dispatch(new \App\Jobs\Marketplace\B2bUhrenApiProductSync($items,$category));
                    app(B2bUhrenApiService::class)->pushProductsOldDBFromApi($items,$category);

                } else {
                    Log::info("B2bUhrenApi Product sync job not dispatched!");
                }

                if(!B2bUhrenApiBrand::where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE)->exists()){
                    B2bUhrenApiBrand::where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_DISABLE)
                    ->update(['sync_status'=>\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE]);
                }
            }

        } catch (\Exception $e) {
            return $e;
        } finally {

        }
    }

    public function addProductToBasket($drmOrder,$customerInfo){

        $contry = app(B2bUhrenApiService::class)->shippingCountryArr();

        $drmCustomerInfo = json_decode($drmOrder->customer_info);
        $customerInfo = json_decode($customerInfo);
        $productInfo = array();
        foreach(json_decode($drmOrder->cart) as $cart){
            $product = Product::where([
                'id'=>$cart->marketplace_product_id,
                'api_id' => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID
            ])->first();
            if(isset($product)){
                $apiProduct = $this->getProductById($product->api_product_id);
                if(isset($apiProduct) && $cart->qty <= $apiProduct->rows[0]->stock){
                    $productInfo[] = array(
                        'id_product'         => $product->api_product_id,
                        'qty'                => $cart->qty,
                        // 'user_id_order'      => $drmOrder->order_id_api,
                        // 'user_id_user'       => $drmOrder->drm_customer_id,
                        'user_company_name'  => $customerInfo->company ?? '',
                        'user_first_name'    => $customerInfo->first_name,
                        'user_last_name'     => $customerInfo->last_name,
                        'user_address'       => $customerInfo->address,
                        'user_city'          => $customerInfo->city,
                        'user_country'       => $contry[$customerInfo->country],
                        'user_zipcode'       => $customerInfo->zipcode,
                        'user_state'         => $customerInfo->street,
                        // 'user_phone'         => '************',
                        // 'user_mobile'        => '************',
                        'user_mail'          => $customerInfo->email,
                        'user_retail_price'  => $cart->amount,
                        'user_discount'      => $cart->product_discount,
                        'user_price'         => $cart->rate,
                        // 'user_cod'           => 1,
                        // 'user_note'          => base64_encode('testo cliente'),
                        // 'internal_note'      => base64_encode('testo esercente')
                    );
                }else{
                    Log::info('Product id '.$product->api_product_id.' - max amount exceded. Max valid amount '.$cart->qty);
                    continue;
                }

            }
        }

        if(count($productInfo) > 0){

            if(!isset($_GET['send']))
            dd($productInfo);


            $basket_item = app(B2bUhrenApiService::class)->addItemsToBasket($productInfo);
            Log::info("B2bUhrenApi Add Item Basket". $basket_item);

            $basket_item = json_decode($basket_item);
            if($basket_item->success == true){
                $checkout = app(B2bUhrenApiService::class)->orderCheckout();
                Log::info("B2bUhrenApi Order Complete".$checkout);
                if(json_decode($checkout)->success == true){
                    $orderResponse = json_decode($checkout);
                    // dd($orderResponse->items[0]->id_order_api);
                    $orderAttributes = [
                    'drm_order_id'  => $drmOrder->id,
                    'api_id'        => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                    'order_id'      => $orderResponse->items[0]->id_order_api,
                    // 'reference_no'      => $placedOrderInfo['reference'],
                    'invoice_number'    => $drmOrder->invoice_number,
                    // 'tracking_codes'    => $placedOrderInfo['tracking_codes'],
                    'shipping_cost'     => $drmOrder->shipping_cost ?? 0,
                    'total'             => $drmOrder->total ?? 0,
                    'customer_infos'    => [
                                             'name'     => $customerInfo->first_name . $customerInfo->last_name ?? '',
                                             'street'   => $customerInfo->street ?? '',
                                             'houseno'  => $customerInfo->house_no ?? '',
                                             'postcode' => $customerInfo->zipcode ?? '',
                                             'city'     => $customerInfo->city ?? '',
                                             'country'  => $customerInfo->country ?? '',
                                           ],
                    // 'product_infos'     => $placedOrderInfo['orderlines'],
                    // 'misc'              => $placedOrderInfo,
                    'status'            => 1,
                    'order_date'        => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                }else{
                Log::info("B2bUhrenApi Order Error" .$checkout);
                }
            }else{
                Log::info("B2bUhrenApi add item basket error" .$basket_item);
            }
        }
    }

    public function fetchProductById($id){
        $product = app(B2bUhrenApiService::class)->getProductById($id);
        $product = json_decode($product);

        return response()->json($product);
    }

    public function deleteProductWithDrmProduct($id){
        dd("not found");
        ini_set('max_execution_time', '0');
        ini_set('memory_limit',-1);
        dd("NO ENTRY");
       $products = Product::where(['api_id' => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID])->get('id');

       $drm_count = 0;
        if(count($products) > 0){

            $drm_prouct = \DB::connection('drm_core')->table('drm_products')->whereIn('marketplace_product_id', $products)->delete();

           dd($products[count($products)-1], $drm_prouct);
           return "Delete B2bUhren api product with Drm product";
        }else{
            return "B2bUhren Api Product Not Found";
        }
    }

    public function updateStockByCsv(){
        return true;
        $local_products = Product::where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)->where('stock','>',0)->pluck('api_product_id')->toArray();

        $json_data = app(B2bUhrenApiService::class)->getJsonData();
        $json_data = json_decode($json_data);
        $id_product = array_column($json_data, 'id_product');
        

       if(count($id_product) > 1){
            $b2bproducts = array_diff($local_products, $id_product);
            
            if(!isset($_GET['updated']))
            dd($b2bproducts);

            foreach(array_chunk($b2bproducts, 2000) as $b2bproduct){
                $mp_products = Product::with('drmProducts')
                ->where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)
                ->whereIn('api_product_id', $b2bproduct)->get();
                $stock_out_sales_trac = [];
                foreach($mp_products as $mp_product){
                    $old_stock = $mp_product->stock;
                    $mp_product->stock             = 0;
                    $mp_product->old_stock         = $old_stock;
                    $mp_product->stock_updated_at  = \Carbon\Carbon::now();

                    $stock_out_sales_trac[] = [
                        'marketplace_product_id'=> $mp_product->id,
                        'sales_stock'           => $old_stock,
                        'sales_amount'          => $old_stock * $mp_product->ek_price,
                        'created_at'            => \Carbon\Carbon::now(),
                    ];

                    $drm_products = $mp_product->drmProducts;
                    $mp_product_update = $mp_product->update();

                    if($mp_product_update){

                        if(count($drm_products) > 0){
                            $data['stock'] = 0;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                            Log::info("B2b DRM product sync-".$mp_product->ean);
                        }

                        app(\App\Services\Marketplace\DuplicateEanService::class)->duplicateEanCheck($mp_product->ean);

                    }

                    Log::info("B2b product sync-".$mp_product->ean);
                }
                
                if(count($stock_out_sales_trac) > 0) DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);
                dd($b2bproduct);
            }
       }

    }

    public function getAllB2bProducts(){
        $local_products = Product::where('api_id',\App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID)->where('stock','>',0)->pluck('ean')->toArray();

        dd($local_products);
    }

    public function sendOrder($products,$customer_info,$order_info){

        $required_field = ["first_name","last_name","street","zipcode","city" ,"country" ,"address"];
        $missing = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }


        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, B2BUHREN API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error, required data address, city, postcode and coyntry'],422);
        }


        if(strtoupper($customer_info['country'])  == "CH"){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, B2BUHREN API not possible to transfer the order to Switzerland',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error, not possible to transfer the order to Switzerland'],422);
        }

        $contry = app(B2bUhrenApiService::class)->shippingCountryArr();

        if(!isset($contry[strtoupper($customer_info['country'])])){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, B2BUHREN API required data country is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error, required data country is missing'],422);
        }

        $productInfo = [];
        foreach($products as $product){

            $apiProduct = $this->getProductById($product['api_product_id']);

            if(isset($apiProduct) && $product['qty'] <= $apiProduct->rows[0]->stock){
                $productInfo[] = array(
                    'id_product'         => $product['api_product_id'],
                    'qty'                => $product['qty'],
                    'user_company_name'  => $customer_info['company'] ?? '',
                    'user_first_name'    => $customer_info['first_name'],
                    'user_last_name'     => $customer_info['last_name'],
                    'user_address'       => $customer_info['address'],
                    'user_city'          => $customer_info['city'],
                    'user_country'       => $contry[$customer_info['country']],
                    'user_zipcode'       => $customer_info['zipcode'],
                    'user_state'         => $customer_info['street'],
                    'user_phone'         => $customer_info['phone'] ?? '+34 919 01 99 48',
                    'user_mail'          => $customer_info['email'],
                    'user_retail_price'  => $product['amount'],
                    'user_discount'      => $product['product_discount'],
                    'user_price'         => $product['rate'],
                );
            }else{
                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, B2BUHREN API max stock exceded or product not found',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status' => 'error', 'message' => 'Validation error, required data country is missing'],422);
            }
        }

        if(count($productInfo) > 0){

            $basket_item = app(B2bUhrenApiService::class)->addItemsToBasket($productInfo);
            Log::info("B2bUhrenApi Add Item Basket". $basket_item);

            $basket_item = json_decode($basket_item);
            if($basket_item->success == true){

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order placed to marketplace API Successfully!',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'transfer',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                $checkout = app(B2bUhrenApiService::class)->orderCheckout();
                Log::info("B2bUhrenApi Order Complete".$checkout);
                if(json_decode($checkout)->success == true){
                    $orderResponse = json_decode($checkout);
                    $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                    'order_id'      => $orderResponse->items[0]->id_order_api,
                    'invoice_number'    => $order_info['invoice_number'] ?? 0,
                    'shipping_cost'     => $order_info['shipping_cost'] ?? 0,
                    'total'             => $order_info['total'] ?? 0,
                    'customer_infos'    => [
                                             'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                                             'street'   => $customer_info['street'] ?? '',
                                             'houseno'  => $customer_info['house_no'] ?? '',
                                             'postcode' => $customer_info['zipcode'] ?? '',
                                             'city'     => $customer_info['city'] ?? '',
                                             'country'  => $customer_info['country'] ?? '',
                                           ],
                    'misc'              => $basket_item ?? '',
                    'status'            => 1,
                    'order_date'        => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);
                app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], ($orderResponse->items[0]->id_order_api ?? 0));
                }else{
                    Log::info("B2bUhrenApi Order Error" .$checkout);
                }
            }else{
                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, B2BUHREN API '. $basket_item->message ?? '',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::B2BUHREN_API_ID,
                    'order_id'      => 0,
                    'invoice_number'    => $order_info['invoice_number'] ?? 0,
                    'shipping_cost'     => $order_info['shipping_cost'] ?? 0,
                    'total'             => $order_info['total'] ?? 0,
                    'customer_infos'    => [
                                             'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                                             'street'   => $customer_info['street'] ?? '',
                                             'houseno'  => $customer_info['house_no'] ?? '',
                                             'postcode' => $customer_info['zipcode'] ?? '',
                                             'city'     => $customer_info['city'] ?? '',
                                             'country'  => $customer_info['country'] ?? '',
                                           ],
                    'misc'              => $basket_item ?? '',
                    'status'            => 2,
                    'order_date'        => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                return response()->json(['status' => 'error', 'message' => 'Validation error, required data country is missing'],422);
            }
        }

    }

}
