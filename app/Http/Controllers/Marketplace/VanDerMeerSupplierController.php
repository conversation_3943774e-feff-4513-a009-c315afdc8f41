<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\Marketplace\Product;
use Illuminate\Support\Carbon;
use Exception;
use SimpleXMLElement;
use App\Services\FTPService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use Google\Cloud\Translate\V2\TranslateClient;

class VanDerMeerSupplierController extends Controller

{
    private $FtpService;
    public function __construct()
    {
        // $isLocal = isLocal();
        $config = [
            'host' => 'ftp.vdmdtg.nl', //van der ftp
            'root' => '/',
            'username' => 'DROPMATIX',
            'password' => 'g9IJUsr1stEWdfA',
            'port' => 21,
        ];
        $this->FtpService = new FTPService($config);
    }

    /**

     * Write code on Method

     *

     * @return response()

     */

    public function getDataFromFtpXmlFile()
    {
        //dd('work from local');
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $startTime  = microtime(true);
        $product_insert_count = 0;
        $product_update_count = 0;
        $path        = '/export';
        $fileName    = 'exportXML_UTF8.xml';
        $fileContent = $this->FtpService->getFileToFtp($path, $fileName);
dd($fileContent);
        //$translate = new TranslateClient(['key' => 'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
        $xmlItem = new SimpleXMLElement($fileContent);
dd($xmlItem);
        $source = 'nl';
        $word_count = 0;
        $productData = [];
        $target_lang = 'de';

        foreach ($xmlItem->Product as $product) {

            dd('product',$product);
            $mp_product  = Product::with('drmProducts')
                ->select('id', 'api_product_id', 'ean', 'stock', 'ek_price', 'vk_price', 'delivery_company_id', 'old_stock', 'stock_updated_at', 'shipping_method')
                // ->where('delivery_company_id',)
                ->where('supplier_id', 2570)
                ->where('ean', (string)$product->EAN)->first();

            if (!blank($mp_product)) {
                if ($mp_product->shipping_method == \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                    $update_column  = [];

                    $new_ek_price   = number_format((string)$product->UWPRIJS_INC, 2);
                    $old_ek_price   = number_format($mp_product->ek_price, 2);


                    if ($mp_product->stock != (string)$product->VOORRAAD_CNS_EENHEID) {
                        $update_column['stock']              = (string)$product->VOORRAAD_CNS_EENHEID;
                        $update_column['old_stock']          = $mp_product->stock;
                        $update_column['stock_updated_at']   = \Carbon\Carbon::now();
                    }

                    if ($old_ek_price != $new_ek_price) {
                        $update_column['ek_price'] = $new_ek_price;
                        $update_column['vk_price'] = number_format(($new_ek_price + ($new_ek_price * 0.05)), 2);
                    }

                    if (count($update_column) > 0) {
                        $drm_products = $mp_product->drmProducts;
                        if (count($drm_products) > 0) {
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products, $update_column);
                            Log::info('VanDerMeer DRM Products Updated: ' . $mp_product->ean);
                        }

                        $mp_product->update($update_column);

                       // app(\App\Services\Marketplace\DuplicateEanService::class)->duplicateEanCheck($mp_product->ean);
                        Log::info('VanDerMeer Updated - ' . $mp_product->ean, $update_column);

                        $product_update_count++;
                    } else {
                        Log::info('VanDerMeer not Updated - ' . $mp_product->ean, $update_column);
                    }
                } else {
                    continue;
                }
            } else {

                $title  = (string)$product->BASIS_OMS ?? [];
                $description = (string)$product->ARTIKEL_CONTENT_HTML ?? [];
                $brand = (string)$product->MERK ?? [];
                $images = ['https://images.vandermeerdiertotaalgroothandel.nl/' . (string)$product->ARINTNUM . '.jpg'];

                if (empty((string)$product->UWPRIJS_INC) || empty($title) || empty((string)$product->EAN) || empty($images)) {
                    continue;
                }

                if (app(\App\Services\Marketplace\ProductService::class)->validateEAN((string)$product->EAN) == false) continue;

                if ((string)$product->COUNTRY_OF_ORIGIN == 'NL') {

                    //Sleep after limit over
                    if ($word_count > 95000) {
                        sleep(10); //Prevoius 100
                        $word_count = 0;
                    }

                    //Translate products
                    $results = $translate->translateBatch([$title, $description, $brand], ['source' => $source, 'target' => $target_lang]);

                    //Output
                    $title = $results[0]['text'];
                    $description = $results[1]['text'];
                    $brand = $results[2]['text'];
                    //count translated word
                    $word_count += (str_word_count($title) + str_word_count($brand) + str_word_count(strip_tags($description), 0));

                    $productData = [
                        'api_product_id'        => (string)$product->ARINTNUM ?? '',
                        'item_number'           => (string)$product->EAN ?? '',
                        'name'                  => $title ?? '',
                        'brand'                 => $brand ?? '',
                        'ean'                   => (string)$product->EAN,
                        'ek_price'              => (string)$product->UWPRIJS_INC,
                        'vk_price'              => number_format(($product->UWPRIJS_INC + ($product->UWPRIJS_INC * 0.05)), 2),
                        'uvp'                   => (string)$product->ADVIESPRIJS_INC_STUK ?? 0,
                        'description'           => $description ?? '',
                        'image'                 => json_encode($images) ?? '',
                        'stock'                 => (string)$product->VOORRAAD_CNS_EENHEID,
                        // 'delivery_company_id'   => ,
                        'category_id'           => 123,
                        'status'                => 0,
                        'item_weight'           => (string)$product->GEWICHT ?? '',
                        'item_size'             => '',
                        'item_color'            => '',
                        'gender'                => '',
                        'materials'             => '',
                        'vat'                   => (string)$product->BTW_PERC,
                        'tags'                  => '',
                        'note'                  => '',
                        'production_year'       => '',
                        'delivery_days'         => 3,
                        'collection_id'         => 0,
                        'shipping_method'       => 1,
                        'shipping_cost'         => 5.5,
                        'internel_stock'        => 0,
                        'supplier_id'           => 2570,
                    ];
                } else if ((string)$product->COUNTRY_OF_ORIGIN == 'DE') {
                    $productData = [
                        'api_product_id'        => (string)$product->ARINTNUM ?? '',
                        'item_number'           => (string)$product->EAN ?? '',
                        'name'                  => $title ?? '',
                        'brand'                 => $brand ?? '',
                        'ean'                   => (string)$product->EAN,
                        'ek_price'              => (string)$product->UWPRIJS_INC,
                        'vk_price'              => number_format(($product->UWPRIJS_INC + ($product->UWPRIJS_INC * 0.05)), 2),
                        'uvp'                   => (string)$product->ADVIESPRIJS_INC_STUK ?? 0,
                        'description'           => $description ?? '',
                        'image'                 => json_encode($images) ?? '',
                        'stock'                 => (string)$product->VOORRAAD_CNS_EENHEID,
                        // 'delivery_company_id'   => ,
                        'category_id'           => 123,
                        'status'                => 0,
                        'item_weight'           => (string)$product->GEWICHT ?? '',
                        'item_size'             => '',
                        'item_color'            => '',
                        'gender'                => '',
                        'materials'             => '',
                        'vat'                   => (string)$product->BTW_PERC,
                        'tags'                  => '',
                        'note'                  => '',
                        'production_year'       => '',
                        'delivery_days'         => 3,
                        'collection_id'         => 0,
                        'shipping_method'       => 1,
                        'shipping_cost'         => 5.5,
                        'internel_stock'        => 0,
                        'supplier_id'           => 2570,
                    ];
                }

                if (count($productData) > 0) {
                    //Product::insert($productData);
                    $productData = [];
                }
            }
        }

        $ex_time =  (microtime(true) - $startTime) . " seconds";
        $message = "Execution Time : " . $ex_time . " | Total Insert : " . $product_insert_count . " | Total Update : " . $product_update_count;
        info($message);
        return response()->json($message);
    }
}
