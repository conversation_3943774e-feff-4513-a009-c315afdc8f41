<?php

namespace App\Http\Controllers\Marketplace;
use Log;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;

class BinoMertensApiController extends Controller

{

    /**

     * Write code on Method

     *

     * @return response()

     */

    public function getDataFromXmlFile(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $startTime  = microtime(true);
        $product_insert_count = 0;
        $product_update_count = 0;
        try{

            $api_id = \App\Enums\Marketplace\ApiResources::BINO_MERTENS_API_ID;

            $update_time = \Carbon\Carbon::now()->addMinutes(30);
            app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);

            $rows   = json_decode(json_encode(simplexml_load_file(\App\Enums\Marketplace\ApiResources::BINO_MERTENS_XML_URL)), true)['item'];
            $rows   = collect($rows)->unique('ean')->toArray();
            $default_shipping_cost = \App\Enums\Marketplace\ApiResources::BINO_MERTENS_SHIPPING_COST;
            $shipping_cost_with_percentage = $default_shipping_cost + ($default_shipping_cost * 0.10);
            
            $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
            $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

            foreach(array_chunk($rows,500) as $chunk_rows){
                $mp_products  = Product::with('drmProducts')
                                        ->select('id','api_id','ean','stock','ek_price','vk_price','delivery_company_id','old_stock','stock_updated_at','shipping_method','uvp','shipping_cost','category_id')
                                        ->where('delivery_company_id',\App\Enums\Marketplace\ApiResources::BINO_MERTENS_DELIVERY_COMPANY_ID)
                                        ->whereIn('ean', array_column($chunk_rows, 'ean'))->get();
                $attributes  = [];
                $salesTrac = [];
                foreach($chunk_rows as $item){
                    $mp_product = $mp_products->where('ean',$item['ean'])->first();

                    if($mp_product){
                        if($mp_product->shipping_method == \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING){
                            $update_column  = [];

                            $new_ek_price   = number_format($item['price_wholesale'],2);
                            $old_ek_price   = number_format($mp_product->ek_price,2);


                            if($mp_product->stock != $item['stock_quantity']){
                                $update_column['stock']              = $item['stock_quantity'];
                                $update_column['old_stock']          = $mp_product->stock;
                                $update_column['stock_updated_at']   = \Carbon\Carbon::now();

                                if($mp_product->stock > $item['stock_quantity']){
                                    $discres_stock = $mp_product->stock - $item['stock_quantity'];
                                    $salesTrac[] = [
                                        'marketplace_product_id'    => $mp_product->id,
                                        'sales_stock'               => $discres_stock,
                                        'sales_amount'              => $discres_stock * $mp_product->ek_price,
                                        'created_at'                => \Carbon\Carbon::now(),
                                    ];
                                }
                            }

                            if($old_ek_price != $new_ek_price){
                                $new_vk = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($new_ek_price, $calculation, $mp_product->uvp, $shipping_cost_with_percentage);
                                $update_column['ek_price'] = $new_ek_price;
                                $update_column['vk_price'] = round($new_vk,2);

                                if(isset($local_category_im[$mp_product->category_id]) && $local_category_im[$mp_product->category_id] > 0){
                                    $update_column['im_handel'] =  $new_vk + (($new_vk * $local_category_im[$mp_product->category_id]) / 100);
                                }
                            }

                            if(count($update_column) > 0 ){
                                $drm_products = $mp_product->drmProducts;
                                if(count($drm_products) > 0){
                                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$update_column);
                                    Log::info('Bino DRM Products Updated: '.$mp_product->ean);
                                }

                                $mp_product->update($update_column);

                                app(\App\Services\Marketplace\DuplicateEanService::class)->duplicateEanCheck($mp_product->ean);
                                Log::info('Bino Updated - '.$mp_product->ean, $update_column);

                                $product_update_count++;

                            }else{
                                Log::info('Bino not Updated - '.$mp_product->ean, $update_column);
                            }

                        }else{
                            continue;
                        }

                    }else{

                        $images   = [];
                        $images[] = $item['image_link'] ?? [];
                        for($i=1;$i < 12 ;$i++){
                            if(!empty($item['image_url_'.$i])){
                                $images[] = $item['image_url_'.$i];
                            }
                        }

                        $product_name = preg_replace('/\s+/', ' ', $item['name']) ?? '';
                        $product_discription = !empty($item['description']) ? $item['description'] : '';
                        if(empty($item['price_wholesale']) || empty($product_name) || empty($images) || empty($item['ean']) || empty($item['sku'])){
                            continue;
                        }

                        $product_uvp = ( $item['price'] + ( $item['price'] * 0.10 )) ?? 0;

                        if(app(\App\Services\Marketplace\ProductService::class)->validateEAN($item['ean']) == false) continue;

                        $vkPrice = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($item['price_wholesale'], $calculation, $product_uvp, $shipping_cost_with_percentage);

                        $im_handel = 0;
                        if(isset($local_category_im[123]) && $local_category_im[123] > 0){
                            $im_handel =  $vkPrice + (($vkPrice * $local_category_im[123]) / 100);
                        }

                        $product_size = (!empty($item['width']) ? "Width : ".$item['width'] : '') . (!empty($item['height']) ? ", Height :". $item['height'] : '');
                        $status_set_attributes = [
                            'item_number'   => $item['sku'] ?? '', 
                            'brand'         => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_BRAND ?? '', 
                            'ek_price'      => $item['price_wholesale'], 
                            'uvp'           => $product_uvp ?? 0, 
                            'delivery_days' => 3, 
                            'vat'           => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_VAT
                        ];
                        
                        $attributes[] = [
                            'api_id'                => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_API_ID,
                            'api_product_id'        => $item['sku'] ?? '',
                            'item_number'           => $item['sku'] ?? '',
                            'name'                  => $product_name ?? '',
                            'brand'                 => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_BRAND ?? '',
                            'ean'                   => $item['ean'],
                            'ek_price'              => $item['price_wholesale'],
                            'vk_price'              => $vkPrice,
                            'uvp'                   => $product_uvp ?? 0,
                            'description'           => $product_discription ?? '',
                            'image'                 => json_encode($images) ?? '',
                            'stock'                 => $item['stock_quantity'],
                            'delivery_company_id'   => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_DELIVERY_COMPANY_ID,
                            'category_id'           => 123,
                            'status'                => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($product_name,$product_discription,$images,$item['stock_quantity'],$status_set_attributes),
                            'item_weight'           => !empty($item['weight']) ? $item['weight'] : '',
                            'item_size'             => $product_size,
                            'item_color'            => '',
                            'gender'                => '',
                            'materials'             => !empty($item['material']) ? $item['material'] : '',
                            'vat'                   => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_VAT,
                            'tags'                  => '',
                            'note'                  => '',
                            'production_year'       => '',
                            'delivery_days'         => 3,
                            'collection_id'         => 0,
                            'shipping_method'       => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                            'shipping_cost'         => $shipping_cost_with_percentage,
                            'real_shipping_cost'    => $default_shipping_cost,
                            'internel_stock'        => 0,
                            'im_handel'             => $im_handel ?? 0,
                        ];

                        $product_insert_count++;
                    }
                }

                if(count($attributes) > 0){
                    Product::insert($attributes);
                    $attributes = [];
                }

                if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);

            }

            $ex_time =  (microtime(true) - $startTime) ." seconds";
            $message = "Execution Time : ". $ex_time . " | Total Insert : ". $product_insert_count . " | Total Update : ". $product_update_count;
            info($message);
            return response()->json($message);
        }catch(Exception $e){
            dd($e);
        }
    }

    public function sendOrder($products,$customer_info,$order_info){

        $required_field = ["first_name","last_name","zipcode","city" ,"country" ,"address"];
        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, BINO MERTENS API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'],422);
        }

        foreach($products as $o_product){
            
            $local_p = Product::where('id',$o_product['product_id'])
                                ->where('stock','>=', (int)$o_product['qty'])
                                ->select('id','stock')
                                ->first();
            
            if(blank($local_p)){
                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, BINO MERTENS API Products have no stock - '. $o_product['ean'],
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'error_level'   => 'STOCK',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status' => 'error', 'message' => 'Stock not available'],422);
            }
        }

        if(!isLocal()){

            $token = 'binomertinesorderemaimp';
            $url = env('BINOMERTENS_ORDER_SEND_URL').'?token='.$token.'&order_id='.$order_info['order_id'];
            
            $response = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($url);

            $orderAttributes = [
                'drm_order_id'  => $order_info['order_id'] ?? 0,
                'api_id'        => \App\Enums\Marketplace\ApiResources::BINO_MERTENS_API_ID,
                'order_id'      => 0,
                'invoice_number'=> $order_info['invoice_number'] ?? 0,
                'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                'total'         => $order_info['total'] ?? 0,
                'customer_infos'=> [
                    'name'     => $customer_info['first_name'] . $customer_info['last_name'] ?? '',
                    'street'   => $customer_info['street'] ?? '',
                    'houseno'  => $customer_info['house_no'] ?? '',
                    'postcode' => $customer_info['zipcode'] ?? '',
                    'city'     => $customer_info['city'] ?? '',
                    'country'  => $customer_info['country'] ?? '',
                ],
                'product_infos' => $products_array ?? '',
                'misc'          => '',
                'status'        => 3,
                'order_date'    => \Carbon\Carbon::now(),
            ];

            \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);
            app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], ($response['order_id'] ?? 0));
            return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'],200);
        }
    }

    public function stockOutProduct(){
        
        $api_id = \App\Enums\Marketplace\ApiResources::BINO_MERTENS_API_ID;

        $update_time = \Carbon\Carbon::now()->addMinutes(30);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);
        
        $rows   = json_decode(json_encode(simplexml_load_file(\App\Enums\Marketplace\ApiResources::BINO_MERTENS_XML_URL)), true)['item'];
        $api_ean   = collect($rows)->unique('ean')->pluck('ean')->toArray();
        
        $local_product_ean  = Product::where('api_id', $api_id)
                            ->where('stock', '>', 0)
                            ->pluck('ean')
                            ->toArray();
        $array_ean  = array_diff($local_product_ean,$api_ean);
        
        if (count($array_ean) > 0) {
            foreach(array_chunk($array_ean ,1500) as $ean){

                $products = Product::with('drmProducts')
                            ->select('id','api_id','stock','ean','old_stock','stock_updated_at','ek_price')
                            ->where('api_id', $api_id)
                            ->whereIn('ean',$ean)
                            ->get();
    
                $stock_out_sales_trac = [];
                foreach($products as $product){
                    $old_stock = $product->stock;
                    $product->stock             = 0;
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    $stock_out_sales_trac[] = [
                        'marketplace_product_id'=> $product->id,
                        'sales_stock'           => $old_stock,
                        'sales_amount'          => $old_stock * $product->ek_price,
                        'created_at'            => \Carbon\Carbon::now(),
                    ];

                    $drm_products = $product->drmProducts;
                    if(count($drm_products)){
                        $data['stock'] = 0;
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        info("binomertins Stock sync drm - " .$product->ean);
                    }

                    $product->update();
                    info("binomertins Stock sync - api -".$api_id." - " .$product->ean);
                }

                if(count($stock_out_sales_trac) > 0) DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);
            }
        }else{
            info("No stock out binomertins product found from api");
        }

    }
}
