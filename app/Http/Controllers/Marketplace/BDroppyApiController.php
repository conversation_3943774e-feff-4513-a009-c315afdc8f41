<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\Marketplace\MarketplaceBdroppyCatelog;
use App\Models\Marketplace\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Queue;
use App\Jobs\Marketplace\BDroppyProductSync;
use Log;
use App\Models\Marketplace\AllApiUpdateSchedule;
class BDroppyApiController extends Controller
{
    public $service;
    public $apis;

    public function __construct ()
    {
        $this->service  = new \App\Services\Marketplace\BDroppy\ApiService();
        $this->apis     = \App\Enums\Marketplace\ApiResources::BDROPPY_APIS;
    }
    public function importProducts ()
    {
        // Insert or update for next update time
        $api_id = 3;
        $update_time = \Carbon\Carbon::now()->addMinutes(120);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);

        // $api_update_table = new AllApiUpdateSchedule();
        // $api_update_schedule = $api_update_table->where('api_id', 3)->first();
        // if($api_update_schedule){
        //     $api_update_schedule->next_update_time = \Carbon\Carbon::now()->addMinutes(120);
        //     $api_update_schedule->update();
        //     Log::info('BDroppy api scheduler update for next update time');
        // }else{
        //     $attributes = [
        //         'api_id' 			 => 3,
        //         'next_update_time'	 => \Carbon\Carbon::now()->addMinutes(120),
        //     ];
        //     $api_update_table->create($attributes);
        //     Log::info('BDroppy api scheduler create for next update time');
        // }
        // End insert or update for next update time
        $catelogs = MarketplaceBdroppyCatelog::get();
        foreach($catelogs as $catelog){
            $dispatch_limit = 5;
            for ( $i = 0; $i < $dispatch_limit; $i++ ) {
                $this->dispatch(new BDroppyProductSync($catelog));
            }
        }
    }

    public function getCategories ()
    {
        $filters     = [];
        $categories   = $this->service->fetchData($this->apis['CATEGORIES'], "GET");
        foreach($categories as $category){

            $mpCat = 68;
            if ( $category['code'] == 'clothing' ) $mpCat = 12;
            if ( $category['code'] == 'accessories' ) $mpCat = 31;
            if ( $category['code'] == 'accessories' ) $mpCat = 31;
            if ( $category['code'] == 'bags' ) $mpCat = 27;
            if ( $category['code'] == 'cosmetics' ) $mpCat = 11;
            if ( $category['code'] == 'underwear' ) $mpCat = 12;
            if ( $category['code'] == 'shoes' ) $mpCat = 32;


            \App\Models\Marketplace\MarketplaceBdroppyCategory::updateOrCreate([
               'api_category_id' => $category['_id'],
            ],[
                'api_category_id'               => $category['_id'],
                'name'                          => $category['code'],
                'code'                          => $category['code'],
                'marketplace_category_id'       => $mpCat,
                'active'                        => 1,
                'status'                        =>$category['active'],
            ]);
        }
        Log::info("BDRoppy Category Inserted");
    }

    public function getCatelogList ()
    {
        $filters    = [];
        $catelogs   = $this->service->fetchData($this->apis['USER_CATELOG_LIST'], "GET");
        $this->service->updateCatelogList ($catelogs);

        Log::info( 'Catelog List Updated' );
    }

    public function testProductSync ()
    {
        Log::info("BDroppy product sync job run !");
        $catelogs = MarketplaceBdroppyCatelog::get();

        foreach ( $catelogs as $catelog ) {
            $filter = [
                'acceptedlocales'   => 'en_US,de_DE',
                'user_catalog'      => $catelog->catelog_id,
                'pageSize'          => \App\Enums\Marketplace\ApiResources::BDROPPY_API_PER_PAGE_PRODUCT,
                'page'              => $catelog->next_import_page,
            ];

            $filter = http_build_query($filter);

            $products = app( \App\Services\Marketplace\BDroppy\ApiService::class )
            ->fetchData( \App\Enums\Marketplace\ApiResources::BDROPPY_APIS['PRODUCTS'].$filter );
            if(isset($products)){
                app( \App\Services\Marketplace\BDroppy\ApiService::class )
                ->insertProductsToMarketplace($products);

                if($catelog->next_import_page >= $products['totalPages']){
                    $catelog->next_import_page = 1;
                    $catelog->update();
                }else{
                    $catelog->increment('next_import_page');
                }
            }

        }
    }

    public function orderBDroppyProduct($drmOrder,$customerInfo){

        $customerInfo = json_decode($customerInfo);
        $products = array();

        foreach(json_decode($drmOrder->cart) as $cart){
            $api_product_id = Product::where([
                'id'=>$cart->marketplace_product_id,
                'api_id' => \App\Enums\Marketplace\ApiResources::BDROPPY_API_ID
            ])->first();

            if(isset($api_product_id->api_product_id)){

                $products = array(
                    'stockId'=>$api_product_id->api_product_id,
                    'quantity'=>$cart->qty,
                );
            }
        }

        if(count($products) > 0){
            $productInfo = array(
                'key'             =>"",
                'date'            => \Carbon\Carbon::now(),
                'recipient'       =>[
                    'email'         => $customerInfo->email,
                    'recipient'     =>  $customerInfo->first_name . $customerInfo->last_name,
                    'careof'        => "",
                    'cfpiva'        => "",
                    'customerKey'   => "",
                    'notes'         => "",
                    'address'       => [
                        'streetName'    => $customerInfo->street,
                        'zip'           => $customerInfo->zipcode,
                        'city'          => $customerInfo->city,
                        'province'      => "",
                        'countrycode'   => $customerInfo->country,
                    ],
                    'phone'         => [
                        'prefix'        => "",
                        'number'        => "",
                    ],
                ],
                'items'            => $products
            );
            $data = json_encode($productInfo);

            if(!isset($_GET['send']))
            dd($productInfo);


            $response = app( \App\Services\Marketplace\BDroppy\ApiService::class )
                ->fetchData( \App\Enums\Marketplace\ApiResources::BDROPPY_APIS['CREATE_ORDER'],'POST' ,$data);

            if(isset($response)){

                    $orderAttributes = [
                    'drm_order_id'  => $drmOrder->id,
                    'api_id'        => \App\Enums\Marketplace\ApiResources::BDROPPY_API_ID,
                    'order_id'      => $response['_id'],
                    'invoice_number'=> $drmOrder->invoice_number,
                    'shipping_cost' => $drmOrder->shipping_cost ?? 0,
                    'total'         => $drmOrder->total ?? 0,
                    'customer_infos'=> [
                                        'name'     => $customerInfo->first_name . $customerInfo->last_name ?? '',
                                        'street'   => $customerInfo->street ?? '',
                                        'houseno'  => $customerInfo->house_no ?? '',
                                        'postcode' => $customerInfo->zipcode ?? '',
                                        'city'     => $customerInfo->city ?? '',
                                        'country'  => $customerInfo->country ?? '',
                                        ],
                    'product_infos' => $products,
                    'misc'          => $response,
                    'status'        => 1,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);
                Log::info("Bdroppy order success. Order Id = ". $response['_id']);
            }else{
                Log::info("BDroppy Order Error" .$response);
            }
        }

    }

    public function oldProductUpdate(){
        $catelogs = MarketplaceBdroppyCatelog::get();

        foreach ( $catelogs as $catelog ) {
            $filter = [
                'acceptedlocales'   => 'en_US,de_DE',
                'user_catalog'      => $catelog->catelog_id,
                'pageSize'          => \App\Enums\Marketplace\ApiResources::BDROPPY_API_PER_PAGE_PRODUCT,
                'page'              => $catelog->next_import_page,
            ];

            $filter = http_build_query($filter);

            $products = app( \App\Services\Marketplace\BDroppy\ApiService::class )
            ->fetchData( \App\Enums\Marketplace\ApiResources::BDROPPY_APIS['PRODUCTS'].$filter );

            if(isset($products['items'])){
                foreach($products['items'] as $product){

                    $mp_product = Product::where([
                        'api_id'=> \App\Enums\Marketplace\ApiResources::BDROPPY_API_ID,
                        'api_product_id' => $product['id']
                        ])->first();

                    if(!empty($mp_product)){
                        if(isset($product['models'][0]['barcode'])){
                            $mp_product->update([
                                'api_product_id' => $product['models'][0]['id'],
                                'stock' => $product['models'][0]['availability'],
                                'ean' => $product['models'][0]['barcode'],
                                'old_stock' => null,
                                'stock_updated_at' => null,
                            ]);

                            $drmProducts = $mp_product->drmProducts()->get();
                            if(count($drmProducts) > 0){
                                foreach ($drmProducts as $drmProduct) {
                                    $drmProduct->update([
                                        'stock' => $product['models'][0]['availability'],
                                        'ean' => $product['models'][0]['barcode'],
                                    ]);
                                }
                            }
                            Log::info("BDroppy old product update " . " BDroppy_id " . $product['models'][0]['id']);
                        }

                    }else{
                        Log::info("BDroppy old Product Not Found");
                    }


                }

                if($catelog->next_import_page >= $products['totalPages']){
                    $catelog->next_import_page = 1;
                    $catelog->update();
                }else{
                    $catelog->increment('next_import_page');
                }
            }

        }
    }
}
