<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use Carbon\Carbon;

class EladyController extends Controller
{
    public function sendOrder($products,$customer_info,$order_info){
        return true;
        $required_field = ["first_name","email","address","zipcode" ,"country"];

        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return response()->json(['status' => 'error', 'message' => 'Validation error'],422);
        }

        $elady_skq = [];
        $elady_qty = [];

        foreach($products as $product){
            $elady_skq[] = $product['item_number'];
            $elady_qty[] = $product['qty'];
        }

        if(count($elady_skq) > 0 && count($elady_qty) > 0){
            
            $order_data = [
                "login"         => 'drap4731mc',
                "password"      => 'UZU5v6H2c1XR',
                "ship_name"     => $customer_info['first_name'].' '. $customer_info['last_name'],
                "ship_email"    => $customer_info['email'],
                "ship_tel"      => $customer_info['phone'],
                "ship_zip"      => $customer_info['zipcode'],
                "ship_country"  => $customer_info['country'],
                "ship_address"  => $customer_info['address'],
                "sku"           => $elady_skq,
                "quantity"      => $elady_qty,
            ];

            if(!isLocal()){
                $order_data = http_build_query($order_data);

                $url = 'https://dropshipping.elady.com/dropshipping/api_order.php?'.$order_data;
                $res = app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($url,'POST');
            }else{
                dd($order_data);
            }

            if($res['status_code'] == 200 && $res['status'] == 1){
                $orderAttributes = [
                            'drm_order_id'  => $order_info['order_id'],
                            'api_id'        => \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID,
                            'order_id'      => 0,
                            'invoice_number'=> $order_info['invoice_number'] ?? 0,
                            'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                            'total'         => $order_info['total'] ?? 0,
                            'customer_infos'=> [
                                                'name'     => $full_name ?? '',
                                                'street'   => $customer_info['street'] ?? '',
                                                'houseno'  => $customer_info['house_no'] ?? '',
                                                'postcode' => $customer_info['zipcode'] ?? '',
                                                'city'     => $customer_info['city'] ?? '',
                                                'country'  => $customer_info['country'] ?? '',
                                                ],
                            'product_infos' => $elady_skq ?? '',
                            'misc'          => $res ?? '',
                            'status'        => 1,
                            'order_date'    => \Carbon\Carbon::now(),
                        ];

                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order placed to marketplace API Successfully!',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'transfer',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], 0);
                return response()->json(['status'=>'success','message'=>'Order Successfully Transfer'],200);

            }else if($res['status_code'] == 200 && $res['status'] == 2){

                $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID,
                    'order_id'      => 0,
                    'invoice_number'=> $order_info['invoice_number'] ?? 0,
                    'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                    'total'         => $order_info['total'] ?? 0,
                    'customer_infos'=> [
                                        'name'     => $full_name ?? '',
                                        'street'   => $customer_info['street'] ?? '',
                                        'houseno'  => $customer_info['house_no'] ?? '',
                                        'postcode' => $customer_info['zipcode'] ?? '',
                                        'city'     => $customer_info['city'] ?? '',
                                        'country'  => $customer_info['country'] ?? '',
                                        ],
                    'product_infos' => $elady_skq ?? '',
                    'misc'          => $res ?? '',
                    'status'        => 2,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, Products have no stock',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'error_level'   => 'STOCK',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status'=>'error','message'=>'Order Transfer Failed'],$res['status_code']);
            }else{
                $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID,
                    'order_id'      => 0,
                    'invoice_number'=> $order_info['invoice_number'] ?? 0,
                    'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                    'total'         => $order_info['total'] ?? 0,
                    'customer_infos'=> [
                                        'name'     => $full_name ?? '',
                                        'street'   => $customer_info['street'] ?? '',
                                        'houseno'  => $customer_info['house_no'] ?? '',
                                        'postcode' => $customer_info['zipcode'] ?? '',
                                        'city'     => $customer_info['city'] ?? '',
                                        'country'  => $customer_info['country'] ?? '',
                                        ],
                    'product_infos' => $elady_skq ?? '',
                    'misc'          => $res ?? '',
                    'status'        => 2,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed. System error',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'exception_delivery',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return response()->json(['status'=>'error','message'=>'Order Transfer Failed'],$res['status_code']);
            }

        }
    }

}
