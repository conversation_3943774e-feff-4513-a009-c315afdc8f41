<?php

namespace App\Http\Controllers\Marketplace;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class SmsController extends Controller
{
    public function sendSms(){
        return true;
        if(isset($_GET['message_content'])){
            $message_content = $_GET['message_content'];
            dispatch(new \App\Jobs\Marketplace\SendSmsJob($message_content));
            Log::info("message sent");
            return response()->json(['status' => 'success'],200);
        }else{
            return response()->json(['status' => 'message_content required'],402);
        }
    }

    public function processSendSms($message_content){
        
        // $number = ['01776217594','01765310132','01738362296'];
        $number = ['01765310132'];
        
        $m_n = implode(",",$number);

        $data = [
            'apikey' => "06e80c43462e998f",
            'secretkey' => "3733d087",
            'callerID' => '8809612448803',
            'toUser' => $m_n,
            'messageContent' => $message_content,
        ];
        $query = http_build_query($data);
        $url = "http://smpp.ajuratech.com:7788/sendtext?$query";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        $ce = curl_exec($ch);

        return $ce;

        curl_close($ch);

    }
}
