<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\Marketplace\MarketplaceBdroppyCategory;
use App\Models\Marketplace\MarketplaceBdroppyCatelog;
use App\Models\Marketplace\Product;
use App\Services\Marketplace\B2bUhrenApi\B2bUhrenApiTestService;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use DB;
class TestController extends Controller
{
    protected $product;

    public $apis;

    protected $catelog;
    protected $bdroppyCategory;
    public $bikeApiService;
    public $bikeApiAddresses;

    public function __construct(Product $product,MarketplaceBdroppyCatelog $catelog,MarketplaceBdroppyCategory $bdroppyCategory)
    {
        $this->product = $product;
        $this->apis     = \App\Enums\Marketplace\ApiTestResources::BDROPPY_APIS;
        $this->catelog = $catelog;
        $this->bdroppyCategory = $bdroppyCategory;
        $this->bikeApiAddresses = \App\Enums\Marketplace\ApiTestResources::ADDRESSES;

        $this->bikeApiService = new \App\Services\Marketplace\Dev\BikeApiService();
    }
    public function brandSync(){
        $brands = app(B2bUhrenApiTestService::class)->getBrands();
        $brands = json_decode($brands);
        if(isset($brands->rows)){
            foreach($brands->rows as $brand){
                $data = [
                    'id_brand' => $brand->id_brand,
                    'name' => $brand->name,
                    'group' => $brand->group,
                    'category' => $brand->category,
                ];
                DB::connection('drm_team')->table('marketplace_b2b_uhren_api_brands')->updateOrInsert(['id_brand' => $data['id_brand']], $data);
            }
            Log::info("B2bUhrenApi Brand sync Done!");
        }else{
            Log::error("B2buhren Brand Not found");
        }

    }

    public function buildProductSyncJobs()
    {
        ini_set('max_execution_time', '0');
        ini_set('memory_limit', -1);

        try {
            $brands = DB::connection('drm_team')->table('marketplace_b2b_uhren_api_brands')
            ->where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE)
            ->take(5)->get();
            if(!empty($brands)){
                $items = array();
                $category = array();
                $cnt = 0;
                foreach ($brands as $key => $brand)
                {
                    $category[str_replace(' ', '', $brand->name)] = str_replace(' ', '', $brand->group);

                    $temp = app(B2bUhrenApiTestService::class)->getProductByBrand($brand->id_brand);
                    $temp = json_decode($temp);
                    if($temp->rows) {
                        $items = array_merge( $items, $temp->rows);
                    }

                    DB::connection('drm_team')->table('marketplace_b2b_uhren_api_brands')->where('id',$brand->id)
                        ->update([
                            'sync_status'   => \App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_DISABLE,
                            'sync_count'    => $brand->sync_count + 1,
                            'sync_time'     => time(),
                            'count_product' => isset($temp->rows)? (count($temp->rows) <= $brand->count_product?$brand->count_product:count($temp->rows)):$brand->count_product,
                        ]);
                }

                if(isset($items) && !empty($items)){
                    Log::info("B2bUhrenApi Product sync job dispatched!");
                    $this->pushB2bUhrenProductsOldDBFromApi($items,$category);
                } else {
                    Log::info("B2bUhrenApi Product sync job not dispatched!");
                }

                if(!DB::connection('drm_team')->table('marketplace_b2b_uhren_api_brands')->where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE)->exists()){
                    DB::connection('drm_team')->table('marketplace_b2b_uhren_api_brands')->where('sync_status',\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_DISABLE)
                    ->update(['sync_status'=>\App\Enums\Marketplace\B2bUhrenApiBrandStatus::SYNC_ABLE]);
                }
            }

        } catch (\Exception $e) {
            return $e;
        } finally {

        }
    }

    public function pushB2bUhrenProductsOldDBFromApi ($apiProductsArr,$category)
    {
        $insertedProductsCount  = 0;

        try{
            if(isset($apiProductsArr)){
                foreach(array_chunk($apiProductsArr,100) as $chunk_products){
                    foreach($chunk_products as $product){

                        $staticDalivaryDays = array("0"=>"10", "1"=>"4", "2"=>"1");
                        $vkPrice = $product->price + ($product->price *0.05);
                        $uvp     = $product->retail_price;

                        $oldProduct = $this->product->setConnection('drm_team')->where([
                                'api_id'=>\App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_ID,
                                'api_product_id' => $product->id_product
                            ])->first();

                        if($product->stock > 2){
                            $stock = floor((($product->stock * 90)/100));
                        }else{
                            $stock = $product->stock;
                        }
                        if(isset($oldProduct)){

                            if(!empty($product->price) && !empty($product->ean) && !empty($product->images)){

                                if($oldProduct->stock != $stock){
                                    $new_stock = $stock;
                                    $old_stock = $oldProduct->stock;
                                    $stock_updated_at = \Carbon\Carbon::now();
                                }else{
                                    $new_stock = $stock;
                                    $old_stock = $oldProduct->old_stock;
                                    $stock_updated_at = $oldProduct->stock_updated_at;
                                }

                                // $drm_products = $oldProduct->drmProducts()->get();
                                // if(count($drm_products) > 0){
                                //     app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$vkPrice,$stock,$uvp);
                                // }

                                $attribut = [];
                                if(!empty($product->attributes_array) && count($product->attributes_array) > 0){
                                    foreach($product->attributes_array as $p_a){
                                       $attribut[$p_a->group_name] = $p_a->value_name;
                                    }
                                }
                                if(!empty($category[str_replace(' ', '',$product->brand_name)])){
                                    $category_id = app(B2bUhrenApiTestService::class)->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand_name)]);
                                }else{
                                    $category_id = app(B2bUhrenApiTestService::class)->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand)]);
                                }

                                $images = [];
                                if(!empty($product->images)){
                                foreach ( $product->images as $media ) {
                                        $images[] = $media->image_path;
                                    }
                                }

                                $data =[
                                    'api_product_id' => $product->id_product ?? null,
                                    'name'           => $product->name ?? '',
                                    'brand'          => $product->brand_name ?? '',
                                    'ek_price'       => $product->price ?? 1,
                                    'uvp'            => $uvp ?? 1,
                                    'description'    => '',
                                    'image'          => $images ?? [],
                                    'stock'          => $new_stock,
                                    'old_stock'      => $old_stock,
                                    'stock_updated_at' => $stock_updated_at,
                                    'category_id'    => $category_id ?? 1,
                                    'materials'      => $materials ?? '',
                                    'item_color'     => $itemColors ?? '',
                                    'gender'         => $attribute['Geschlecht'] ?? '',
                                    'item_size'      => $attribute['Gehäuseabmessung'] ?? '',
                                    'item_weight'    => $product->weight ?? '',
                                    'misc'           => $product->attributes,
                                    'delivery_days'  => $staticDalivaryDays[$product->speed_shipping] ?? \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_DEFAULT_DELIVERY_DAYS,
                                ];

                                $insertedModel = $oldProduct->update($data);

                                if (!$insertedModel) Log::error($product);
                                Log::info('B2bUhren Updated - '.$product->id_product);
                                $insertedProductsCount += $insertedModel ? 1 : 0;
                            }
                        }else{

                            if(!empty($product->price) && !empty($product->ean) && !empty($product->images)){
                                $attribut = [];

                                if(!empty($product->attributes_array) && count($product->attributes_array) > 0){
                                    foreach($product->attributes_array as $p_a){
                                       $attribut[$p_a->group_name] = $p_a->value_name;
                                    }
                                }

                                if(!empty($category[str_replace(' ', '',$product->brand_name)])){
                                    $category_id = app(B2bUhrenApiTestService::class)->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand_name)]);
                                }else{
                                    $category_id = app(B2bUhrenApiTestService::class)->getCategoryIdOfApiProduct($category[str_replace(' ', '', $product->brand)]);
                                }

                                $images = [];

                                if(!empty($product->images)){
                                    foreach ( $product->images as $media ) {
                                            $images[] = $media->image_path;
                                        }
                                    }

                                    $data =[
                                        'api_id' => \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_ID,
                                        'api_product_id' => $product->id_product ?? null,
                                        'item_number'    => $product->reference ?? null,
                                        'name'           => $product->name ?? '',
                                        'brand'          => $product->brand_name ?? '',
                                        'ean'            => $product->ean ?? '',
                                        'ek_price'       => $product->price ?? 1,
                                        'vk_price'       => $vkPrice,
                                        'uvp'            => $uvp ?? 1,
                                        'description'    => '',
                                        'image'          => $images ?? [],
                                        'stock'          => $stock ?? 0,
                                        'supplier_id'    => \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_SUPPLIER_ID,
                                        'delivery_company_id' => \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_DELIVERY_COMPANY_ID,
                                        'status'         => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                                        'category_id'    => $category_id ?? 1,
                                        'collection_id'  => 0,
                                        'shipping_method'=> \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                                        'shipping_cost'  => \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_DEFAULT_SHIPPING_COST,
                                        'category'       => '',

                                        'materials'      => $materials ?? '',
                                        'item_color'     => $itemColors ?? '',
                                        'gender'         => $attribute['Geschlecht'] ?? '',
                                        'item_size'      => $attribute['Gehäuseabmessung'] ?? '',
                                        'item_weight'    => $product->weight ?? '',
                                        'internel_stock' => 0,
                                        'misc'           => $product->attributes,
                                        'delivery_days'  => $staticDalivaryDays[$product->speed_shipping] ?? \App\Enums\Marketplace\ApiTestResources::B2BUHREN_API_DEFAULT_DELIVERY_DAYS,
                                    ];

                                $insertedModel = $this->product->setConnection('drm_team')->create($data);

                                if (!$insertedModel) Log::error($product);
                                Log::info('B2bUhren Inserted - '.$product->id_product);

                                $insertedProductsCount += $insertedModel ? 1 : 0;
                            }
                        }
                    }
                }

            }

            return "success";
        } catch(Exception $e){
            dd($e);
        }
        return response()->json([
            'status'                    => 1,
            'products_created_count'    => $insertedProductsCount,
        ]);
    }

    // bdroppy

    public function getCategories ()
    {
        $filters     = [];
        $categories   = app(\App\Services\Marketplace\BDroppy\BDroppyApiTestService::class)->fetchData($this->apis['CATEGORIES'], "GET");
        foreach($categories as $category){

            $mpCat = 0;
            if ( $category['value'] == 'clothing' ) $mpCat = 12;
            if ( $category['value'] == 'accessories' ) $mpCat = 31;
            if ( $category['value'] == 'accessories' ) $mpCat = 31;
            if ( $category['value'] == 'bags' ) $mpCat = 27;
            if ( $category['value'] == 'cosmetics' ) $mpCat = 11;
            if ( $category['value'] == 'underwear' ) $mpCat = 12;
            if ( $category['value'] == 'shoes' ) $mpCat = 32;

            $this->bdroppyCategory->connection('drm_team')->updateOrCreate([
               'api_category_id' => $category['_id'],
            ],[
                'api_category_id'               => $category['_id'],
                'name'                          => $category['name'],
                'code'                          => $category['value'],
                'marketplace_category_id'       => $mpCat,
                'active'                        => 1,
                'status'                        =>$category['status'],
            ]);
        }
        Log::info("BDRoppy Category Inserted");
    }

    public function getCatelogList ()
    {
        $filters    = [];
        $catelogs   = app(\App\Services\Marketplace\BDroppy\BDroppyApiTestService::class)->fetchData($this->apis['USER_CATELOG_LIST'], "GET");

        app(\App\Services\Marketplace\BDroppy\BDroppyApiTestService::class)->updateCatelogList ($catelogs);

        Log::info( 'Catelog List Updated' );
    }

    public function testProductSync ()
    {
        Log::info("BDroppy product sync job run !");
        $catelogs = $this->catelog->setConnection('drm_team')->get();

        foreach ( $catelogs as $catelog ) {
            $filter = [
                'acceptedlocales'   => 'en_US,de_DE',
                'user_catalog'      => $catelog->catelog_id,
                'pageSize'          => \App\Enums\Marketplace\ApiTestResources::BDROPPY_API_PER_PAGE_PRODUCT,
                'page'              => $catelog->next_import_page,
            ];

            $filter = http_build_query($filter);

            $products = app( \App\Services\Marketplace\BDroppy\BDroppyApiTestService::class )
            ->fetchData( \App\Enums\Marketplace\ApiTestResources::BDROPPY_APIS['PRODUCTS'].$filter );

            if(isset($products)){
                app( \App\Services\Marketplace\BDroppy\BDroppyApiTestService::class )
                ->insertProductsToMarketplace($products);

                if($catelog->next_import_page >= $products['totalPages']){
                    $catelog->next_import_page = 1;
                    $catelog->update();
                }else{
                    $catelog->increment('next_import_page');
                }
            }

        }
    }



    /*
        SATRT:: BIKE API TEST CODES
        SATRT:: BIKE API TEST CODES
    */
    public function bikeApiImportProducts()
    {
        $allProducts = [];

        $row = $this->bikeApiService->apiCredentials;
        $end = $row->next_import_page + 5;

        for ( $page = $row->next_import_page; $page < $end; $page++ ) {
            $apiAddress = $this->bikeApiAddresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;
            $response = $this->bikeApiService->fetchData($apiAddress);
            
            dd($response);

            if (!array_key_exists('data', $response) || empty($response['data'])) {
                continue;
            } else {
                $allProducts = array_merge($allProducts, $response['data']);
                $this->apiService->ch = curl_init();
            }
        }

        // try {
        //     foreach ($pages as $page) {
        //         try{
        //             $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;
        //             $response = $this->bikeApiService->fetchData($apiAddress);
        //             dd($response);

        //             if (!array_key_exists('data', $response) || empty($response['data'])) {
        //                 continue;
        //             } else {
        //                 $allProducts = array_merge($allProducts, $response['data']);
        //                 $this->apiService->ch = curl_init();
        //             }
        //         } catch (\Exception $e) {
        //             return $e->getMessage();
        //         }
        //     }
        //     $this->apiService->pushProductsFromApi($allProducts);

        // } catch (\Exception $e) {
        //     return 'Got exception:: '.$e->getMessage();
        // }

        $this->bikeApiService->pushProductsFromApi();
    }


}
