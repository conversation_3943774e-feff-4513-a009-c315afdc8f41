<?php

namespace App\Http\Controllers\Marketplace;

use Log;
use App\Models\DrmProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Interfaces\ApiControllerInterface;
use App\Models\Marketplace\MPCountryProduct;
use App\Services\Marketplace\ProductService;
use App\Services\Marketplace\Vidaxl\VidaxlApiService;

class VidaXlApiController extends Controller implements ApiControllerInterface
{
    public function getDataFromCsv(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $url        = \App\Enums\Marketplace\ApiResources::VidaXLCsv;
        $type       = 'csv';
        $api_csv_products       = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);
        $rows                   = collect($api_csv_products)->unique('EAN')->toArray();
        info('get csv data');
        $api_brands = array_unique(array_column($rows,'Brand'));
        $brands = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);

        $api_id             = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;
        $deliveryCompanyId  = \App\Enums\Marketplace\ApiResources::VIDAXL_DELIVERY_COMPANY_ID;
        $shippingCost       = \App\Enums\Marketplace\ApiResources::VIDAXL_SHIPPING_COST;

        $maping_categories  = DB::table('api_category_mapping')
                            ->where('api_id',\App\Enums\Marketplace\ApiResources::VIDAXL_API_ID)
                            ->where('is_complete',1)
                            ->select('api_category_id','mp_category_id')
                            ->get();

        $categories =[];
        foreach($maping_categories as $m_category){
            $categories[$m_category->api_category_id] = $m_category->mp_category_id;
        }

        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();
        foreach(array_chunk($rows,1000) as $chunk_rows){
            $old_products = Product::with('drmProducts')->where('api_id',$api_id)->whereIn('ean',array_column($chunk_rows,'EAN'))->get();

            $attributes =[];
            foreach($chunk_rows as $item){

                $old_product_exist = $old_products->where('api_id',$api_id)->where('ean',$item['EAN'])->first();
                if(isset($old_product_exist)){
                    // $old_stock = $old_product_exist->stock;
                    // $old_ek_price = $old_product_exist->ek_price;

                    // $drm_products = $old_product_exist->drmProducts;

                    // if($old_stock != $item['Stock']){

                    //     $old_product_exist->stock             = $item['Stock'];
                    //     $old_product_exist->old_stock         = $old_stock;
                    //     $old_product_exist->stock_updated_at  = \Carbon\Carbon::now();

                    // }

                    // if($old_ek_price != $item['B2B price']){
                    //     $old_product_exist->ek_price       = $item['B2B price'];
                    //     $old_product_exist->vk_price       = $item['B2B price'] + ($item['B2B price']*0.05);
                    // }

                    // if($old_stock != $item['Stock'] || $old_ek_price != $item['B2B price']){

                    //     if(count($drm_products) > 0){
                    //         $drm_ek_price = ($item['B2B price'] + ($item['B2B price']*0.05));
                    //         $data['vk_price'] = round($drm_ek_price,2);
                    //         $data['stock'] = $item['Stock'];
                    //         app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                    //         Log::info("Vidaxl API DRM product sync-".$old_product_exist->ean);
                    //     }

                    //     $old_product_exist->update();
                        info("Vidaxl product sync-".$old_product_exist->ean);

                    // }

                }else{

                    $images = [];
                    for($i=1;$i < 12 ;$i++){
                        if(!empty($item['Image '.$i])){
                            $images[] = $item['Image '.$i];
                        }
                    }
                    $title =  str_replace("vidaXL","",$item['Product_title']);
                    if(empty($item['B2B price']) || empty($title) || empty($images) || empty($item['EAN']) || $item['Stock'] < 2 ){
                        continue;
                    }

                    if(app(\App\Services\Marketplace\ProductService::class)->validateEAN($item['EAN']) == false) continue;

                    $properties = '';
                    if(isset($item['Properties'])){
                        preg_match_all("/\bMaterial\:+([^<,]+)/", $item['Properties'], $matches);

                        if(isset($matches[1][0])){
                            $properties = $matches[1][0];
                        }
                    }
                    $product_uvp = ( $item['Webshop price'] + ( $item['Webshop price'] * 0.10 ) ) ?? 0;

                    $vkPrice            = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($item['B2B price'], $calculation, $product_uvp, $shippingCost);

                    $category_id_after_mappping = !empty($categories[$item['Category_id']]) ? $categories[$item['Category_id']] : 68;

                    $im_handel = 0;
                    if(isset($local_category_im[$category_id_after_mappping]) && $local_category_im[$category_id_after_mappping] > 0){
                        $im_handel =  $vkPrice + (($vkPrice * $local_category_im[$category_id_after_mappping]) / 100);
                    }

                    $status_set_attributes = [
                        'item_number'   => $item['SKU'] ?? null, 
                        'brand'         => $brands[strtoupper($item['Brand'])] ?? $item['Brand'], 
                        'ek_price'      => $item['B2B price'], 
                        'uvp'           => $product_uvp, 
                        'delivery_days' => $item['estimated_total_delivery_time'] ?? 3, 
                        'vat'           => \App\Enums\Marketplace\ApiResources::VIDAXL_VAT
                    ];

                    $attributes[] = [
                        'api_id'                => $api_id,
                        'api_product_id'        => $item['SKU'] ?? null,
                        'item_number'           => $item['SKU'] ?? '',
                        'name'                  => $title ?? '',
                        'brand'                 => $brands[strtoupper($item['Brand'])] ?? $item['Brand'],
                        'ean'                   => $item['EAN'],
                        'ek_price'              => $item['B2B price'],
                        'vk_price'              => $vkPrice,
                        'uvp'                   => $product_uvp,
                        'description'           => $item['HTML_description'] ?? '',
                        'image'                 => json_encode($images),
                        'stock'                 => $item['Stock'],
                        'supplier_id'           => 0,
                        'delivery_company_id'   => $deliveryCompanyId,
                        'category_id'           => $category_id_after_mappping,
                        'api_category_id'       => $item['Category_id'] ?? '',
                        'status'                => app(\App\Services\Marketplace\ProductService::class)->checkRequiredField($title,$item['HTML_description'],$images,$item['Stock'],$status_set_attributes),
                        'item_weight'           => $item['Weight'] ?? '',
                        'item_size'             => $item['Size'] ?? '',
                        'item_color'            => $item['Color'] ?? '',
                        'gender'                => $item['Gender'] ?? '',
                        'materials'             => $properties,
                        'vat'                   => \App\Enums\Marketplace\ApiResources::VIDAXL_VAT,
                        'tags'                  => '',
                        'note'                  => '',
                        'production_year'       => '',
                        'delivery_days'         => $item['estimated_total_delivery_time'] ?? 3,
                        'collection_id'         => 0,
                        'shipping_method'       => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                        'shipping_cost'         => $shippingCost,
                        'real_shipping_cost'    => 0.00,
                        'internel_stock'        => 0,
                        'created_at'            => \Carbon\Carbon::now(),
                        'misc'                  => json_encode($item),
                        'im_handel'             => $im_handel ?? 0,

                    ];

                    info("inserted in array");
                }

            }

            if(!empty($attributes)){
                Product::insert($attributes);
                $attributes =[];
                Log::info("Vidaxl product inserted");
            }

        }

        // $product2 = new Product();
        // $mp_data = $product2->setConnection('drm_ream')->where('api_id',$api_id)->pluck('ean')->toArray();

        // $delete_data = array_diff($mp_data, array_column($rows, 'EAN') );
        // $product3 = new Product();
        // $product3->setConnection('drm_ream')->where('api_id',$api_id)->whereIn('ean',$delete_data)->update([
        //     'stock'             => 0,
        //     'stock_updated_at'  => \Carbon\Carbon::now(),
        // ]);

    }

    public function stockUpdateFromCsv(){

        $api_id     = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;

        $update_time = \Carbon\Carbon::now()->addMinutes(30);
        app(\App\Services\Marketplace\ProductService::class)->updateApiSyncSchedule($api_id,$update_time);

        $url        = \App\Enums\Marketplace\ApiResources::VidaXLCsv;
        $type       = 'csv';
        $rows       = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);


        info('Load csv data');

        if(count($rows) > 10){

            // $this->updateMissingProduct($rows,$api_id);
            // $this->stockSync($rows,$api_id);
            // $this->priceSync($rows,$api_id);
            $this->uvpPriceSync($rows,$api_id);
        }


    }

    private function updateMissingProduct($rows,$api_id){

        Log::info("Vidaxl Missing Product");
        $api_ean           = collect($rows)->pluck('EAN')->toArray();
        $local_product_ean = Product::where('api_id', $api_id)->where('country_id', 1)->where('stock','!=',0)->pluck('ean')->toArray();
        $array_ean         = array_diff($local_product_ean,$api_ean);

        if(count($array_ean) > 0){
            foreach(array_chunk($array_ean,500) as $ean){
                $local_products = Product::with('drmProducts')
                                            ->select('stock','ean','id','old_stock','stock_updated_at','ek_price')
                                            ->where('api_id', $api_id)
                                            ->where('country_id', 1)
                                            ->whereIn('ean',$ean)
                                            ->get();

                $stock_out_sales_trac = [];
                foreach($local_products as $product){

                    if($product->stock > 0){

                        $old_stock = $product->stock;

                        $product->stock             = 0;
                        $product->old_stock         = $old_stock;
                        $product->stock_updated_at  = \Carbon\Carbon::now();

                        $stock_out_sales_trac[] = [
                            'marketplace_product_id'=> $product->id,
                            'sales_stock'           => $old_stock,
                            'sales_amount'          => $old_stock * $product->ek_price,
                            'created_at'            => \Carbon\Carbon::now(),
                        ];

                        $drm_products = $product->drmProducts;
                        if(count($drm_products) > 0){
                            $data['stock'] = 0;
                            app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                            Log::info("vidaxl drm stock sync-".$product->ean);
                        }

                        $product->update();
                        Log::info("vidaxl stock sync-".$product->ean);

                    }
                }

                if(count($stock_out_sales_trac) > 0)  DB::table('marketplace_product_sales_information')->insert($stock_out_sales_trac);
            }
        }else{
            Log::info("vidaxl missing not found..........................");
        }
    }

    private function stockSync($rows,$api_id){

        Log::info("Vidaxl Stock Sync");
        $api_products_ean_stock   = collect($rows)->pluck('Stock','EAN')->toArray();
        $local_products_ean_stock = Product::where('api_id', $api_id)->where('country_id', 1)->pluck('stock','ean')->toArray();

        $new_ean_stock            = array_diff_assoc($api_products_ean_stock,$local_products_ean_stock);

        $array_ean = [];
        foreach($new_ean_stock as $ean => $stock){
            if(array_key_exists($ean,$local_products_ean_stock)){
                $array_ean[] = $ean;
            }
        }

        foreach(array_chunk($array_ean,1500) as $ean){
            $local_mp_products = Product::with('drmProducts')
                                        ->select('stock','ean','id','old_stock','stock_updated_at','ek_price')
                                        ->where('api_id', $api_id)
                                        ->whereIn('ean',$ean)
                                        ->where('country_id', 1)
                                        ->get();
            $salesTrac = [];
            foreach($local_mp_products as $product){
                if($product->stock !=  $new_ean_stock[$product->ean] ){

                    $old_stock = $product->stock;

                    $product->stock             = $new_ean_stock[$product->ean];
                    $product->old_stock         = $old_stock;
                    $product->stock_updated_at  = \Carbon\Carbon::now();

                    if($old_stock > $new_ean_stock[$product->ean]){

                        $discres_stock = $old_stock - $new_ean_stock[$product->ean];
                        $salesTrac[] = [
                            'marketplace_product_id'    => $product->id,
                            'sales_stock'               => $discres_stock,
                            'sales_amount'              => $discres_stock * $product->ek_price,
                            'created_at'                => \Carbon\Carbon::now(),
                        ];

                    }

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['stock'] = $new_ean_stock[$product->ean];
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("Vidaxl API DRM product sync-".$product->ean);
                    }

                    $product->update();

                    Log::info("Vidaxl API product sync-".$product->ean);
                }
            }

            if(count($salesTrac) > 0)  DB::table('marketplace_product_sales_information')->insert($salesTrac);
        }
        Log::info("Vidaxl Sync completed..................");
    }

    private function priceSync($rows,$api_id){

        info("Vidaxl price Sync");
        $api_products_price = collect($rows)->pluck('B2B price','EAN')->toArray();
        $local_products     = Product::where('api_id', $api_id)->where('country_id', 1)->pluck('ek_price','ean')->toArray();
        $new_price_ean      = array_diff_assoc($api_products_price, array_map('floatval', $local_products));
        $array_ean          = array_keys(array_intersect_key($new_price_ean,$local_products));

        $calculation       = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

        foreach(array_chunk($array_ean,1500) as $ean){
            $local_mp_products = Product::with('drmProducts')
                ->select('id','ean','uvp','api_id','ek_price','old_ek_price','ek_price_updated_at','vk_price','old_vk_price','vk_price_updated_at','im_handel','category_id','item_number','shipping_cost','update_status','real_shipping_cost')
                ->where('api_id', $api_id)
                ->whereIn('ean',$ean)
                ->where('country_id', 1)
                ->get();

            foreach($local_mp_products as $product){
                $new_ek_price = $new_price_ean[$product->ean];
                // $old_ek_price = $product->ek_price;
                if($product->ek_price !=  $new_ek_price ){

                    app(\App\Services\Marketplace\ProductService::class)
                                ->mpNewPriceCalculation($product,$new_ek_price,$calculation,$local_category_im,$product->shipping_cost,true);

                    // $product->ek_price             = $new_ek_price;
                    // $product->old_ek_price         = $old_ek_price;
                    // $product->ek_price_updated_at  = \Carbon\Carbon::now();

                    // // $new_vk_price = ($new_ek_price + ($new_ek_price * 0.05 ));
                    // $new_vk_price =app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($new_ek_price, $calculation, $product->uvp, $product->shipping_cost);
                    
                    // $old_vk_price = $product->vk_price;

                    // $product->vk_price             = $new_vk_price;
                    // $product->old_vk_price         = $old_vk_price;
                    // $product->vk_price_updated_at  = \Carbon\Carbon::now();

                    // $drm_products = $product->drmProducts;
                    // if(count($drm_products) > 0){
                    //     $data['vk_price'] = round($new_vk_price,2);
                    //     app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                    //     Log::info("Vidaxl API DRM product sync-".$product->ean);
                    // }

                    // $product->update();
                    info("Vidaxl API product sync-".$product->ean);
                }
            }
        }
        info("Vidaxl Sync completed..................");
    }

    private function uvpPriceSync($rows,$api_id){
        
        $api_products_price = collect($rows)->pluck('Webshop price','EAN')->toArray();
        $local_products     = Product::where('api_id', $api_id)->where('country_id', 1)->pluck('uvp','ean')->toArray();
        $new_price_ean      = array_diff_assoc($api_products_price, array_map('floatval', $local_products));
        $array_ean          = array_keys(array_intersect_key($new_price_ean,$local_products));
        $syncable_uvp_count = count($array_ean);
        $synced_uvp_count   = 0;
        foreach(array_chunk($array_ean,1500) as $ean){
            $local_mp_products = Product::with('drmProducts')
                ->select('id','api_id','ean','uvp')
                ->where('api_id', $api_id)
                ->whereIn('ean',$ean)
                ->where('country_id', 1)
                ->get();
            
            foreach($local_mp_products as $product){
                $synced_uvp_count++;
                $new_uvp_price = $new_price_ean[$product->ean];
                if($product->uvp !=  $new_uvp_price ){

                    $product->uvp             = $new_uvp_price;

                    $drm_products = $product->drmProducts;
                    if(count($drm_products) > 0){
                        $data['uvp'] = round($new_uvp_price,2);
                        app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct($drm_products,$data);
                        Log::info("Vidaxl API DRM product uvp sync-".$product->ean);
                    }

                    $product->update();
                    Log::info("Vidaxl API product uvp sync-".$product->ean. ' - total - ' . $syncable_uvp_count . ' - synced - ' . $synced_uvp_count);
                }
            }
        }
        Log::info("Vidaxl uvp Sync completed..................");
    }

    public function sendOrder($products,$customer_info,$order_info){

        $required_field = ["first_name","last_name","zipcode","city" ,"country" ,"address"];
        $missing        = [];
        foreach($customer_info as $key => $customerInfo){
            if(in_array($key,$required_field) &&  empty($customerInfo)){
                $missing[] = $key;
            }
        }

        if(!empty($missing)){
            $drm_data = [
                'token'         => "Zd6tQv8Cvd",
                'order_id'      => $order_info['order_id'],
                'message'       => 'Order Transfer Failed, VIDAXL API required data '. implode(" ,",$missing) .' is missing',
                'parcel_number' => '',
                'parcel_service'=> '',
                'status'        => 'exception',
                'date'          => Carbon::now()
            ];

            $drm_data = json_encode($drm_data);
            app(\App\Services\Marketplace\ProductService::class)->sendOrderTrackingToDRM($drm_data);

            return true;
        }

        $full_name = $customer_info['first_name'].' '. $customer_info['last_name'];

        $products_array = [];

        foreach($products as $product){

            $products_array[] = [
                'product_code'    => $product['item_number'],
                'quantity'        => $product['qty'],
                'addressbook'     => [
                    'address'        => $customer_info['address'],
                    'address2'       => "",
                    'city'           => $customer_info['city'],
                    'province'       => $customer_info['street'],
                    'postal_code'    => (string) $customer_info['zipcode'],
                    'country'        => $customer_info['country'],
                    'name'           => $full_name,
                    'phone'          => $customer_info['phone'],
                    'comments'       => "",
                ],

            ];
        }

        if(count($products_array) > 0){
            $productInfo = [
                    'customer_order_reference' => (string) $order_info['order_id'],
                    'comments_customer'        => "Please deliver asap",
                    'addressbook'              => [
                        'country'              => $customer_info['country'],
                    ],
                    'order_products'           => $products_array,
                ];

            $order_data = json_encode($productInfo);
            if(!isLocal()){
                $url = \App\Enums\Marketplace\ApiResources::VIDAXL_CREATE_ORDER_URL;

                if(trim(strtoupper($customer_info['country']))  == "AT" || trim(strtoupper($customer_info['country']))  == "AUSTRIA"){
                    $res = app(VidaxlApiService::class)->buildRequestForVidaxlATCountry($url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "ES" || trim(strtoupper($customer_info['country']))  == "SPAIN"){
                    $res = app(VidaxlApiService::class)->buildRequestForVidaxlESCountry($url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "FR" || trim(strtoupper($customer_info['country']))  == "FRANCE"){
                    $fr_user_email = '<EMAIL>';
                    $fr_token = "37257a52-7906-4674-8730-53acf818169a";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($fr_user_email,$fr_token,$url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "PT" || trim(strtoupper($customer_info['country']))  == "PORTUGAL"){
                    $pt_user_email = '<EMAIL>';
                    $pt_token = "bb6cdaa0-9d88-4512-9660-2dad95ea3954";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($pt_user_email,$pt_token,$url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "BE" || trim(strtoupper($customer_info['country']))  == "BELGIUM"){
                    $be_user_email = '<EMAIL>';
                    $be_token = "74364563-3d7b-40d8-8d2f-116817f11d44";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($be_user_email,$be_token,$url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "CH" || trim(strtoupper($customer_info['country']))  == "SWITZERLAND"){
                    $ch_user_email = '<EMAIL>';
                    $ch_token = "91716eb8-75b8-44af-9561-afd80b3208fa";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($ch_user_email,$ch_token,$url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "IT" || trim(strtoupper($customer_info['country']))  == "ITALY"){
                    $it_user_email = '<EMAIL>';
                    $it_token = "98a283f1-90be-4e2e-9cb6-22772683d68d";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($it_user_email,$it_token,$url,$order_data,'POST');
                }else if(trim(strtoupper($customer_info['country']))  == "CZ" || trim(strtoupper($customer_info['country']))  == "CZECH REPUBLIC"){
                    $cz_user_email = '<EMAIL>';
                    $cz_token = "60538e9b-ab90-4a08-8a74-b2f46821d3ac";
                    $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($cz_user_email,$cz_token,$url,$order_data,'POST');
                }else{
                    $res = app(VidaxlApiService::class)->buildRequest($url,$order_data,'POST');
                }
                
            }else{
                dd($order_data);
            }
            if($res['status_code'] == 200){
                $orderAttributes = [
                            'drm_order_id'  => $order_info['order_id'],
                            'api_id'        => \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID,
                            'order_id'      => 0,
                            'invoice_number'=> $order_info['invoice_number'] ?? 0,
                            'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                            'total'         => $order_info['total'] ?? 0,
                            'customer_infos'=> [
                                                'name'     => $full_name ?? '',
                                                'street'   => $customer_info['street'] ?? '',
                                                'houseno'  => $customer_info['house_no'] ?? '',
                                                'postcode' => $customer_info['zipcode'] ?? '',
                                                'city'     => $customer_info['city'] ?? '',
                                                'country'  => $customer_info['country'] ?? '',
                                                ],
                            'product_infos' => $products_array ?? '',
                            'misc'          => $res ?? '',
                            'status'        => 1,
                            'order_date'    => \Carbon\Carbon::now(),
                        ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order placed to marketplace API Successfully!',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'transfer',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(ProductService::class)->sendOrderTrackingToDRM($drm_data);
                app(\App\Services\Marketplace\ProductService::class)->updateDrmOrder($order_info['order_id'], 0);
                return true;

            }else{

                $orderAttributes = [
                    'drm_order_id'  => $order_info['order_id'],
                    'api_id'        => \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID,
                    'order_id'      => 0,
                    'invoice_number'=> $order_info['invoice_number'] ?? 0,
                    'shipping_cost' => $order_info['shipping_cost'] ?? 0,
                    'total'         => $order_info['total'] ?? 0,
                    'customer_infos'=> [
                                        'name'     => $full_name ?? '',
                                        'street'   => $customer_info['street'] ?? '',
                                        'houseno'  => $customer_info['house_no'] ?? '',
                                        'postcode' => $customer_info['zipcode'] ?? '',
                                        'city'     => $customer_info['city'] ?? '',
                                        'country'  => $customer_info['country'] ?? '',
                                        ],
                    'product_infos' => $products_array ?? '',
                    'misc'          => $res ?? '',
                    'status'        => 2,
                    'order_date'    => \Carbon\Carbon::now(),
                ];
                \App\Models\Marketplace\ApiOrdersResponse::create($orderAttributes);

                if($res['status_code'] == 422){
                    $api_err_message = json_decode($res['data'])->message ?? "required data missing";
                }else{
                    $api_err_message = "";
                }
                $drm_data = [
                    'token'         => "Zd6tQv8Cvd",
                    'order_id'      => $order_info['order_id'],
                    'message'       => 'Order Transfer Failed, VIDAXL API . '. $api_err_message ?? '',
                    'parcel_number' => '',
                    'parcel_service'=> '',
                    'status'        => 'exception',
                    'date'          => Carbon::now()
                ];

                $drm_data = json_encode($drm_data);
                app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                return true;
            }

        }
    }

    public function getApiTracking($api_order_response){

        info("Vidaxl tracking start..............");
        foreach($api_order_response as $key => $order){

            if($key > 0){
                sleep(1);
            }

            $url = \App\Enums\Marketplace\ApiResources::VIDAXL_GET_ORDER_URL.$order->drm_order_id;
            $res = app(VidaxlApiService::class)->buildRequest($url,'','GET');

            if($res['status_code'] == 200){

                $res = json_decode($res['data']);
                if(isset($res[0]->order->status_order_id) && $res[0]->order->status_order_id == 7){
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order cancel',
                        'parcel_number' => $res[0]->order->shipping_tracking ?? '',
                        'parcel_service'=> $res[0]->order->shipping_option_name ?? '',
                        'status'        => 'cancel',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->status = 2;
                    $order->update();

                }else if(isset($res[0]->order->shipping_tracking) && !empty($res[0]->order->shipping_tracking) && $res[0]->order->status_order_id != 7){
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order shipped',
                        'parcel_number' => $res[0]->order->shipping_tracking,
                        'parcel_service'=> $res[0]->order->shipping_option_name,
                        'status'        => 'shipped',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->tracking_codes = $res[0]->order->shipping_tracking;
                    $order->status = 3;
                    $order->update();

                }
            }
        }
        info('Vidaxl tracking end.........');

        // $this->austiraAccountOrderTracking($api_order_response);
        // info('Vidaxl tracking AT end.........');
        // $this->spainAccountOrderTracking($api_order_response);
        // info('Vidaxl tracking spain end.........');
        $this->getApiAllCountryTracking($api_order_response);
    }

    // vidaxl austira account order tracking
    public function austiraAccountOrderTracking($api_order_response){

        foreach($api_order_response as $key => $order){

            if($key > 0){
                sleep(1);
            }

            $url = \App\Enums\Marketplace\ApiResources::VIDAXL_GET_ORDER_URL.$order->drm_order_id;
            $res = app(VidaxlApiService::class)->buildRequestForVidaxlATCountry($url,'','GET');

            if($res['status_code'] == 200){

                $res = json_decode($res['data']);
                if(isset($res[0]->order->status_order_id) && $res[0]->order->status_order_id == 7){
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order cancel',
                        'parcel_number' => $res[0]->order->shipping_tracking ?? '',
                        'parcel_service'=> $res[0]->order->shipping_option_name ?? '',
                        'status'        => 'cancel',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->status = 2;
                    $order->update();

                }else if(isset($res[0]->order->shipping_tracking) && !empty($res[0]->order->shipping_tracking) && $res[0]->order->status_order_id != 7){
                    
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order shipped',
                        'parcel_number' => $res[0]->order->shipping_tracking,
                        'parcel_service'=> $res[0]->order->shipping_option_name,
                        'status'        => 'shipped',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->tracking_codes = $res[0]->order->shipping_tracking;
                    $order->status = 3;
                    $order->update();

                }
            }
        }

    }

    // vidaxl SPAIN account order tracking
    public function spainAccountOrderTracking($api_order_response){

        foreach($api_order_response as $key => $order){

            if($key > 0){
                sleep(1);
            }

            $url = \App\Enums\Marketplace\ApiResources::VIDAXL_GET_ORDER_URL.$order->drm_order_id;
            $res = app(VidaxlApiService::class)->buildRequestForVidaxlESCountry($url,'','GET');

            if($res['status_code'] == 200){

                $res = json_decode($res['data']);
                if(isset($res[0]->order->status_order_id) && $res[0]->order->status_order_id == 7){
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order cancel',
                        'parcel_number' => $res[0]->order->shipping_tracking ?? '',
                        'parcel_service'=> $res[0]->order->shipping_option_name ?? '',
                        'status'        => 'cancel',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->status = 2;
                    $order->update();

                }else if(isset($res[0]->order->shipping_tracking) && !empty($res[0]->order->shipping_tracking) && $res[0]->order->status_order_id != 7){
                    
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order shipped',
                        'parcel_number' => $res[0]->order->shipping_tracking,
                        'parcel_service'=> $res[0]->order->shipping_option_name,
                        'status'        => 'shipped',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->tracking_codes = $res[0]->order->shipping_tracking;
                    $order->status = 3;
                    $order->update();

                }
            }
        }

    }

    public function getApiAllCountryTracking($order_from_db)
    {
        if(count($order_from_db) > 0){
            foreach($this->allCountryCrediantial() as $country => $crediantial){
                $this->trackingProcess($crediantial['email'],$crediantial['token'],$order_from_db);
            }
        }
    }
    private function allCountryCrediantial():array
    {     
        return [
            'AT' => [
                'email' => '<EMAIL>',
                'token' => 'c17d06f6-8414-4e7d-87be-e02f0dfd34ad',
                // 'old_token' => '2c2a8206-3f70-4acd-9e85-cb6c871d8942',
            ],
            'ES' => [
                'email' => '<EMAIL>',
                'token' => '48046243-abda-4690-9df1-8bb4fb3f2665',
            ],
            'FR' => [
                'email' => '<EMAIL>',
                'token' => '37257a52-7906-4674-8730-53acf818169a',
            ],
            "PT" => [
                'email' => '<EMAIL>',
                'token' => "bb6cdaa0-9d88-4512-9660-2dad95ea3954",
            ],
            "BE" => [
                'email' => '<EMAIL>',
                'token' => "74364563-3d7b-40d8-8d2f-116817f11d44",
            ],
            "CH" => [
                'email' => '<EMAIL>',
                'token' => "91716eb8-75b8-44af-9561-afd80b3208fa",
            ],
            "IT" => [
                'email' => '<EMAIL>',
                'token' => "98a283f1-90be-4e2e-9cb6-22772683d68d",
            ],
            "CZ" => [
                'email' => '<EMAIL>',
                'token' => "60538e9b-ab90-4a08-8a74-b2f46821d3ac",
            ],
        ];

    }

    /**
     * @param string $email
     * @param string $token
     * @param array $order_from_db
     * @return void
     */
    private function trackingProcess($email,$token,$order_from_db)
    {
        $base_url = \App\Enums\Marketplace\ApiResources::VIDAXL_GET_ORDER_URL;
        foreach($order_from_db as $key => $order){

            if($key > 0){
                sleep(1);
            }

            $url = $base_url.$order->drm_order_id;
            $res = app(VidaxlApiService::class)->buildRequestVidaxlByCountry($email,$token,$url,'','GET');

            if($res['status_code'] == 200){

                $res = json_decode($res['data']);
                if(isset($res[0]->order->status_order_id) && $res[0]->order->status_order_id == 7){
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order cancel',
                        'parcel_number' => $res[0]->order->shipping_tracking ?? '',
                        'parcel_service'=> $res[0]->order->shipping_option_name ?? '',
                        'status'        => 'cancel',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->status = 2;
                    $order->update();

                }else if(isset($res[0]->order->shipping_tracking) && !empty($res[0]->order->shipping_tracking) && $res[0]->order->status_order_id != 7){
                    
                    $drm_data = [
                        'token'         => "Zd6tQv8Cvd",
                        'order_id'      => $order->drm_order_id,
                        'message'       => 'order shipped',
                        'parcel_number' => $res[0]->order->shipping_tracking,
                        'parcel_service'=> $res[0]->order->shipping_option_name,
                        'status'        => 'shipped',
                        'date'          => Carbon::now()
                    ];

                    $drm_data = json_encode($drm_data);
                    app(ProductService::class)->sendOrderTrackingToDRM($drm_data);

                    $order->tracking_codes = $res[0]->order->shipping_tracking;
                    $order->status = 3;
                    $order->update();

                }
            }
        }
    }

    public function chPriceSync(){

        $url        = \App\Enums\Marketplace\ApiResources::VidaXLChCsv;
        $type       = 'csv';
        $rows       = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);

        Log::info("Vidaxl CH price Sync Start");
        $api_products_price = collect($rows)->pluck('B2B price','EAN')->toArray();
        $drm_products       = DrmProduct::where('user_id', 3420)->where('marketplace_delivery_company_id', \App\Enums\Marketplace\ApiResources::VIDAXL_DELIVERY_COMPANY_ID)->get();
                              
        $local_products     = $drm_products->pluck('ek_price', 'ean')->toArray();
        $new_price_ean      = array_diff_assoc($api_products_price, array_map('floatval', $local_products));
        $array_ean          = array_keys(array_intersect_key($new_price_ean, $local_products));
        $calculation        = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();

        foreach(array_chunk($array_ean, 1500) as $ean){
            $local_drm_products = $drm_products->whereIn('ean', $ean);

            foreach($local_drm_products as $drm_product){
                $new_ek_price = $new_price_ean[$drm_product->ean];
                $old_ek_price = $drm_product->ek_price;
                $new_vk_price = round(app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($new_ek_price, $calculation, $drm_product->uvp, $drm_product->shipping_cost), 2);
                if($old_ek_price != $new_ek_price ){
                    $data['ch_vk_price'] = $new_vk_price;
                    app(\App\Services\Marketplace\ProductService::class)->syncDrmProduct(collect([$drm_product]), $data);
                    Log::info("Vidaxl CH API DRM product sync-".$drm_product->ean);
                }
            }
        }
        Log::info("Vidaxl CH price Sync completed..................");
    }

    public function insertChProdctInfo(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $url        = \App\Enums\Marketplace\ApiResources::VidaXLChCsv;
        $type       = 'csv';
        $api_csv_products  = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);
        $rows              = collect($api_csv_products)->unique('EAN')->toArray();
        info('get csv data');
       
        $api_id             = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;
        $shippingCost       = \App\Enums\Marketplace\ApiResources::VIDAXL_SHIPPING_COST;

        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id',24)->first();
        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

        foreach(array_chunk($rows,1000) as $chunk_rows){

            $old_products = new Product();
            $old_products = $old_products->setConnection('drm_team')
                                        ->where('api_id',$api_id)
                                        ->whereIn('ean',array_column($chunk_rows,'EAN'))
                                        ->get();

            $county_exist_products = new MPCountryProduct();
            $county_exist_products = $county_exist_products->setConnection('drm_team')->where('api_id', $api_id)
                                        ->whereIn('api_product_id',array_column($chunk_rows,'SKU'))
                                        ->where('country_id', 83)
                                        ->select('id', 'api_id', 'api_product_id')
                                        ->get();

            $attributes =[];
            foreach($chunk_rows as $item){

                $county_exist_product = $county_exist_products->where('api_product_id', $item['SKU'])->first();
                if (isset($county_exist_product)) {
                    continue;
                }

                $old_product_exist = $old_products->where('api_id',$api_id)->where('ean',$item['EAN'])->first();

                if(isset($old_product_exist)){

                    $title =  str_replace("vidaXL","",$item['Product_title']);
                    if(empty($item['B2B price']) || empty($title)){
                        continue;
                    }

                    $product_uvp = ( $item['Webshop price'] + ( $item['Webshop price'] * 0.10 ) ) ?? 0;

                    $vkPrice     = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($item['B2B price'], $calculation, $product_uvp, $shippingCost);

                    $im_handel = 0;
                    if(isset($local_category_im[$old_product_exist->category_id]) && $local_category_im[$old_product_exist->category_id] > 0){
                        $im_handel =  $vkPrice + (($vkPrice * $local_category_im[$old_product_exist->category_id]) / 100);
                    }

                    $attributes[] = [
                        'country_id'            => 83,
                        'product_id'            => $old_product_exist->id,
                        'api_id'                => $api_id,
                        'api_product_id'        => $item['SKU'] ?? null,
                        'item_number'           => $item['SKU'] ?? '',
                        'name'                  => $title ?? '',
                        'ek_price'              => $item['B2B price'],
                        'vk_price'              => $vkPrice,
                        'uvp'                   => $product_uvp,
                        'description'           => $item['HTML_description'] ?? '',
                        'shipping_cost'         => $shippingCost,
                        'real_shipping_cost'    => 0.00,
                        'im_handel'             => $im_handel,
                    ];

                    info("inserted in array");
                }
            }

            if(!empty($attributes)){
                $county_exist_products = new MPCountryProduct();
                $county_exist_products->setConnection('drm_team')->insert($attributes);
                $attributes = [];
            }

        }

    }

}
