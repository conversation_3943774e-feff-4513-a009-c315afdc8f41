<?php

namespace App\Http\Controllers\Marketplace;

use DateTime;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Repositories\OrderRepository;
use Illuminate\Support\Facades\Storage;
use App\Services\Marketplace\SwissPostService;

class SwissPostController extends Controller
{
    private string $clientId = "28";
    private $order_info = [];
    private $order_id = 0;

    public function sendOrder($products, $customer_info, $order_info)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $this->order_info = $order_info;
        $this->order_id = $order_info['order_id'];
        $this->clientId = in_array($order_info['user_id'], [3984, 4175]) ? "35" : $this->clientId;
        
        // Handle Swiss orders - get original address if this is a duplicate
        $actualCustomerInfo = $this->getActualCustomerInfoForLabel($customer_info, $order_info);

        $label_token = $this->getToken('NES_ECOMMERCE_LABEL_GENERATION_PUBLIC', $order_info['user_id']);
        if (!$label_token) {
            return $this->storeOrderLog('Label token not generated');
        }

        $label = $this->generateLabel($products, $actualCustomerInfo, $order_info, $label_token);

        if (isset($label['response']['mailpieceId'])) {
            return $this->handleLabelSuccess($label, $products, $actualCustomerInfo, $order_info['user_id']);
        }

        return $this->storeOrderLog('Label generation failed', $label);
    }

    private function handleLabelSuccess($label, $products, $customer_info, $user_id)
    {
        $pdf_url = $this->saveLabelPdf($label['response']['label']);
        if (blank($pdf_url)) {
            return $this->storeOrderLog('Label PDF URL not found');
        }

        $preadvice_token = $this->getToken('NES_ECOMMERCE_INBOUND_INCOMING_PREADVICE_PUBLIC', $user_id);
        $preAdvice = $this->generatePreAdvice($products, $customer_info, $this->order_info, $preadvice_token, $label['response']['mailpieceId']);

        if (isset($preAdvice['status_code']) && ($preAdvice['status_code'] == 200 || $preAdvice['status_code'] == 201)) {
            $this->generatePreAdviceManifest($preadvice_token, $label['response']['mailpieceId'], $pdf_url);
            return response()->json(['status' => 'success', 'message' => 'Order Successfully Transfer'], 200);
        }

        return $this->storeOrderLog('Pre advice failed', $preAdvice);
    }

    private function getToken(string $scope, $user_id): ?string
    {
        return app(SwissPostService::class)->generateToken($scope, $user_id);
    }

    private function generateLabel($products, $customer_info, $order_info, string $label_token)
    {
        $url = env('SWISSPOST_URL') . '/logistics/international/ecommerce/v1/label/generation/v1/generate/pdf';

        $data = $this->buildLabelData($products, $customer_info, $order_info);

        return app(SwissPostService::class)->buildCurlRequest($url, $label_token, 'POST', json_encode($data));
    }

    private function buildLabelData($products, $customer_info, $order_info)
    {
        return [
            "clientId" => $this->clientId,
            "orderNumber" => (string)$order_info['order_id'],
            "customerAddress" => $this->buildAddressData($customer_info),
            "attributes" => $this->buildLabelAttributes($products, 'PRIORITY'),
            "labelDefinition" => ["labelFileType" => "PDF"],
            "return" => ["address" => $this->returnAddressData()]
        ];
    }

    private function returnAddressData()
    {
        return [
            "companyName" => 'Post CH AG - Logistik-Services',
            "firstName" => 'Camperfriend',
            "lastName" => 'Gbr',
            "street" => 'Flughofstrasse',
            "houseNumber" => '106',
            "postcode" => '8153',
            "city" => 'Rümlang',
            "country" => 'CH',
            "pobox" => "",
            "emails" => ['<EMAIL>', '<EMAIL>']
        ];
    }

    private function buildAddressData($customer_info)
    {
        return [
            "companyName" => $customer_info['company'] . ' ' . $customer_info['first_name'] . ' ' . $customer_info['last_name'],
            "firstName" => $customer_info['first_name'] ?? '',
            "lastName" => $customer_info['last_name'] ?? '',
            // "street" => ($customer_info['street'] ?? '') . ' ' . (substr($customer_info['house_no'], 0, 6) ?? ''),
            "street" => ($customer_info['street'] ?? ''),
            "houseNumber" => substr($customer_info['house_no'], 0, 6) ?? '',
            "postcode" => $customer_info['zipcode'],
            "city" => $customer_info['city'],
            "country" => 'CH',
            "pobox" => "",
            "emails" => [$customer_info['email']]
        ];
    }

    private function buildLabelAttributes($products, $additionalService)
    {
        info($products);
        return [
            "totalGrossWeightInGrams" => (int) ceil(floatval($products->sum(function ($item) {
                return empty($item['item_weight']) ? 0 : (int) $item['item_weight'] * 1000;
            }))),
            "format" => "PARCEL",
            "labelType" => "POSTPAC",
            "additionalService" => [$additionalService]
        ];
    }

    private function generatePreAdvice($products, $customer_info,  $order_info,  $preadvice_token,  $mailpieceId)
    {
        $url = env('SWISSPOST_URL') . '/logistics/international/ecommerce/v1/inbound/incoming/preadvice/v1/clients/' . $this->clientId . '/orders/' . $order_info['order_id'];
        $data = $this->buildPreAdviceData($products, $customer_info, $order_info, $mailpieceId);

        return app(SwissPostService::class)->preAdviceOrderCreate($url, $preadvice_token, $data);
    }

    private function buildPreAdviceData($products,  $customer_info,  $order_info,  $mailpieceId)
    {
        $dateTimeWithMilliseconds = (new DateTime())->format('Y-m-d\TH:i:s.vP');

        return [
            "clientId" => $this->clientId,
            "orderNumber" => (string)$order_info['order_id'],
            "invoiceNumber" => "2024-" . (string)$order_info['order_id'],
            "invoiceTimestamp" => $dateTimeWithMilliseconds,
            "customerAddress" => $this->buildAddressData($customer_info),
            "mailpieces" => [[
                "mailpieceId" => $mailpieceId,
                "internationalCommercialTerms" => "DELIVERED_DUTY_PAID",
                "movementReferenceNumber" => "23DE810319247850B7",
                "shippingTimestamp" => $dateTimeWithMilliseconds,
                "items" => $this->buildProductItems($products)
            ]],
            "subClients" => []
        ];
    }

    private function buildProductItems($products)
    {
        $items = [];
        foreach ($products as $product) {
            $items[] = [
                "originCountry" => "DE",
                "netWeightInGrams" => empty($product['item_weight']) ? 0 : (int) ceil(floatval($product['item_weight'] * 1000)),
                "grossWeightInGrams" => empty($product['item_weight']) ? 1 : (int) ceil(floatval($product['item_weight'] * 1000)) + 1,
                "count" => $product['qty'],
                "customsValueInCents" => (int) ceil(floatval($this->priceCalculation($product))),
                "europeanArticleNumber" => (string)$product['ean'],
                "customsCurrency" => "CHF",
                "description" => Str::limit($product['description'], 248) ?? '',
                "itemType" => "COMMERCE",
                "harmonizedTariffSchedule" => ["originCode" => "95030000", "destinationCode" => "95030000"],
                "articleNumber" => $product['item_number'],
                "unitOfQuantity" => "UNIT",
                "preferentialTreatment" => false,
            ];
        }
        return $items;
    }

    private function priceCalculation($product)
    {
        $total = ($product['amount'] ?? 0) + ($product['shipping_cost'] ?? 0);
        if (isset($product['tax']) && $product['tax'] > 0) {
            $total =  $total + ($total * $product['tax'] / 100);
        }

        if (isset($product['currency']) && "CHF" == strtoupper($product['currency'])) {
            return $total * 100;
        } else {
            return ($total * $this->getCurrencyRate()) * 100;
        }
    }

    private function getCurrencyRate()
    {
        $currency = DB::connection('drm_core')->table('currency_rates')->orderBy('id', 'desc')->first();
        return isset($currency->rates) ? (json_decode($currency->rates, true)['CHF'] ?? 1) : 1;
    }

    private function generatePreAdviceManifest($token,  $mailpieceId,  $pdf_url)
    {
        $url = env('SWISSPOST_URL') . '/logistics/international/ecommerce/v1/inbound/incoming/preadvice/v1/clients/' . $this->clientId . '/manifests';
        $data = [
            "clientId" => $this->clientId,
            "transportNumber" => $mailpieceId,
            "remarks" => "",
            "mailpieces" => [["mailpieceId" => $mailpieceId]]
        ];

        $response = app(SwissPostService::class)->buildCurlRequest($url, $token, 'POST', json_encode($data));

        $this->storeOrderLog('Minifest created', $response, $pdf_url, $mailpieceId);
        $this->sendTrackingToDrm($mailpieceId);
        $this->sendEmail($pdf_url);

        return true;
    }

    private function saveLabelPdf($pdfString)
    {
        $pdfContent = base64_decode($pdfString);
        $fileName = 'marketplace/swisspost/' . $this->order_id . '-label-' . time() . '.pdf';
        Storage::disk('space')->put($fileName, $pdfContent, 'public');

        return Storage::disk('space')->url($fileName);
    }

    private function storeOrderLog($message, array $response = [], $pdf_url = '', $mailpieceId = null)
    {
        return DB::table('swiss_api_responses')->insert([
            'drm_order_id' => $this->order_id,
            'response' => json_encode($response ?? [], true) ?? [],
            'status_message' => $message ?? '',
            'pdf_url'    => $pdf_url,
            'mailpiece_id' => $mailpieceId,
        ]);
    }

    private function sendEmail($pdf_url)
    {
        $drm_data = [
            'token'    => "tyMcR63U78vg1Nm",
            'order_id' => $this->order_id,
            'pdf_link' => $pdf_url,
        ];

        $drm_data = json_encode($drm_data);
        $drm_sync_url = env('DRM_V7_URL') . '/api/swisspost/mail/qr-code-pdf-sent';
        app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($drm_sync_url, 'POST', $drm_data);
    }
    private function sendTrackingToDrm($tracking_number)
    {
        $drm_data = [
            'token'         => "Zd6tQv8Cvd",
            'order_id'      => $this->order_id,
            'parcel_number' => $tracking_number,
            'parcel_service' => 'SwissPost',
        ];

        $drm_data = json_encode($drm_data);
        $drm_sync_url = env('DRM_V7_URL') . '/api/order-tracking-sync';
        app(\App\Services\Marketplace\ProductService::class)->buildCurlRequest($drm_sync_url, 'POST', $drm_data);
    }

    public function retrnLabel($order_id)
    {
        $this->order_id = $order_id;

        $order_response =  DB::table('swiss_api_responses')->where('drm_order_id', $order_id)->orderBy('id', 'desc')->first();

        // Validate if there's an order response and mailpiece ID
        if (!isset($order_response)) {
            return response()->json(['status' => 'error', 'message' => 'Order information not found'], 422);
        }

        $orderInfo = app(OrderRepository::class)->getDiliveryOrderInfoById($order_id);

        if (!$orderInfo) {
            return response()->json(['status' => 'error', 'message' => 'Order information not found'], 422);
        }
        $this->clientId = in_array($orderInfo['order_info']['user_id'], [3984, 4175]) ? "35" : $this->clientId;

        $label_token = $this->getToken('NES_ECOMMERCE_LABEL_GENERATION_PUBLIC', $orderInfo['order_info']['user_id']);
        if (!$label_token) {
            $this->storeOrderLog('Label token not generated');
            return response()->json(['status' => 'error', 'message' => 'Label token not generated'], 422);
        }

        // Generate label
        $label = $this->createRetunLabelPdf($orderInfo, $label_token);
        info($label);
        if (!isset($label['response']['mailpieceId'])) {
            $this->storeOrderLog('Return Label pdf not generated', $label);
            return response()->json(['status' => 'error', 'message' => 'Return Label pdf not generated'], 422);
        }

        $pdf_url = $this->saveLabelPdf($label['response']['label']);
        $data = ['outgoingMailpieceId' => $label['response']['mailpieceId']];

        // Build and send SwissPost API request for return
        $return_token = $this->getToken('NES_ECOMMERCE_RETURNS_PUBLIC', $orderInfo['order_info']['user_id']);
        $order_return_url = env('SWISSPOST_URL') . '/logistics/international/ecommerce/v1/returns/v1/clients/' . $this->clientId . '/outgoing/orders/' . $order_id;

        $response = app(SwissPostService::class)->buildCurlRequest($order_return_url, $return_token, 'PUT', json_encode($data));

        // Check response and store log based on success or failure
        $status = isset($response['status_code']) && in_array($response['status_code'], [200, 201]) ? 'success' : 'error';
        $message = $status === 'success' ? 'Return label created' : 'Return label creation failed';
        $this->storeOrderLog($message, $response);

        return response()->json([
            'status' => $status,
            'message' => $message,
            'tracking_number' => $status === 'success' ? $label['response']['mailpieceId'] : null,
            'label_url' => $status === 'success' ? $pdf_url : null
        ], $status === 'success' ? 200 : 422);
    }

    private function createRetunLabelPdf($orderInfo, $label_token)
    {

        $url = env('SWISSPOST_URL') . '/logistics/international/ecommerce/v1/label/generation/v1/generate/pdf';

        $data = [
            "clientId" => $this->clientId,
            "orderNumber" => (string)$this->order_id,
            "customerAddress" => $this->buildAddressData($this->returnLabelAddress()),
            "attributes" => $this->buildLabelAttributes(collect($orderInfo['products']), 'GAS'),
            "labelDefinition" => ["labelFileType" => "PDF"],
            "return" => ["address" => $this->buildAddressData($orderInfo['customer_info'])]
        ];
        info(json_encode($data));
        return app(SwissPostService::class)->buildCurlRequest($url, $label_token, 'POST', json_encode($data));
    }
    private function returnLabelAddress()
    {
        return [
            "company" => 'Post CH AG - Logistik-Services',
            "first_name" => 'Freeart',
            "last_name" => 'GmbH',
            "street" => 'Flughofstrasse',
            "house_no" => "106",
            "zipcode" => '8153',
            "city" => 'Rümlang',
            "country" => 'CH',
            "email" => '<EMAIL>'
        ];
    }
    
    /**
     * Get actual customer info for Swiss Post labels
     * If this is a Swiss duplicate order, use original Swiss address for labels
     */
    private function getActualCustomerInfoForLabel($customer_info, $order_info)
    {
        // Check if this is a Swiss duplicate order
        if (isset($order_info['is_swiss_duplicate']) && $order_info['is_swiss_duplicate']) {
            // Get the original Swiss address from the duplication record
            $originalOrderId = $order_info['original_order_id'] ?? null;
            if ($originalOrderId) {
                $swissOrderStatus = app(\App\Services\Marketplace\SwissOrderService::class)->getSwissOrderStatus($originalOrderId);
                if ($swissOrderStatus['is_swiss_order']) {
                    return $swissOrderStatus['original_address'];
                }
            }
        }
        
        return $customer_info;
    }
}
