<?php

namespace App\Http\Controllers\Marketplace\Vidaxl;

use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;

class CountryWiseProductController extends Controller
{
    public function insertChProduct()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        info('vidaxl CH product insert-----------------------------');

        $country_id = 83;
        $url        = 'http://transport.productsup.io/e9ba40e8e3597b1588a0/channel/188050/vidaXL_ch_de_dropshipping.csv';
        $type       = 'csv';

        $api_csv_products = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);
        $rows = collect($api_csv_products)->unique('EAN')->toArray();

        info('vidaxl CH data load-----------------------------');
        $this->insertProduct($rows, $country_id);
    }

    public function insertESProduct()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $country_id = 8;
        $url        = 'http://transport.productsup.io/7947c7becca20572b9b6/channel/188064/vidaXL_es_dropshipping.csv';
        $type       = 'csv';
        $api_csv_products = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);
        $rows = collect($api_csv_products)->unique('EAN')->toArray();

        info('vidaxl es data load-----------------------------');
        $this->insertProduct($rows, $country_id);
    }

    public function insertATProduct()
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $country_id = 74;
        $url        = 'http://transport.productsup.io/9e92912654ad834d7822/channel/188085/vidaXL_at_dropshipping.csv';
        $type       = 'csv';
        $api_csv_products = app(\App\Http\Controllers\Marketplace\CollectionController::class)->csvToArray($url, $type, 'auto', false);
        $rows = collect($api_csv_products)->unique('EAN')->toArray();

        info('vidaxl es data load-----------------------------');
        $this->insertProduct($rows, $country_id);
    }


    private function insertProduct($apiProducts, $country_id)
    {
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $api_brands = array_unique(array_column($apiProducts, 'Brand'));
        $brands     = app(\App\Services\Marketplace\ProductService::class)->brandInsertAndUpdate($api_brands);

        $api_id             = \App\Enums\Marketplace\ApiResources::VIDAXL_API_ID;
        $deliveryCompanyId  = \App\Enums\Marketplace\ApiResources::VIDAXL_DELIVERY_COMPANY_ID;
        $shippingCost       = \App\Enums\Marketplace\ApiResources::VIDAXL_SHIPPING_COST;

        $maping_categories  = DB::table('api_category_mapping')
            ->where('api_id', $api_id)
            ->where('is_complete', 1)
            ->select('api_category_id', 'mp_category_id')
            ->get();

        $categories = [];
        foreach ($maping_categories as $m_category) {
            $categories[$m_category->api_category_id] = $m_category->mp_category_id;
        }

        $calculation = DB::connection('drm_core')->table('marketplace_profit_calculations')->where('id', 24)->first();

        $local_category_im = app(\App\Services\Marketplace\ProductService::class)->getCategoryIdWithIMHandel();

        foreach (array_chunk($apiProducts, 1500) as $chunk_rows) {
            $old_products = Product::where('api_id', $api_id)->whereIn('ean', array_column($chunk_rows, 'EAN'))->get();

            $attributes = [];
            foreach ($chunk_rows as $item) {

                $old_product_exist = $old_products->where('api_id', $api_id)
                    ->where('ean', $item['EAN'])
                    ->where('country_id', $country_id)
                    ->first();

                if (isset($old_product_exist)) {
                    continue;
                } else {

                    $is_image_process = 0;
                    $images = [];
                    // image
                    for ($i = 1; $i < 12; $i++) {
                        if (!empty($item['Image ' . $i])) {
                            $images[] = $item['Image ' . $i];
                        }
                    }

                    $title =  str_replace("vidaXL", "", $item['Product_title']);
                    if (empty($item['B2B price']) || empty($title) || empty($images) || empty($item['EAN']) || $item['Stock'] < 2) {
                        continue;
                    }

                    if (app(\App\Services\Marketplace\ProductService::class)->validateEAN($item['EAN']) == false) continue;

                    $another_country_exist = $old_products->where('api_id', $api_id)
                        ->where('ean', $item['EAN'])
                        ->first();

                    if ($another_country_exist) {
                        $category_id_after_mappping = $another_country_exist->category_id;
                    } else {
                        $category_id_after_mappping = !empty($categories[$item['Category_id']]) ? $categories[$item['Category_id']] : 68;
                    }

                    $properties = '';
                    if (isset($item['Properties'])) {
                        preg_match_all("/\bMaterial\:+([^<,]+)/", $item['Properties'], $matches);

                        if (isset($matches[1][0])) {
                            $properties = $matches[1][0];
                        }
                    }

                    $brand_id = $brands[strtoupper($item['Brand'])] ?? $item['Brand'];

                    $product_uvp = ($item['Webshop price'] + ($item['Webshop price'] * 0.10)) ?? 0;

                    $vkPrice = app(\App\Services\Marketplace\ProductService::class)->vkPriceCalculation($item['B2B price'], $calculation, $product_uvp, $shippingCost);

                    $im_handel = 0;
                    if (isset($local_category_im[$category_id_after_mappping]) && $local_category_im[$category_id_after_mappping] > 0) {
                        $im_handel =  $vkPrice + (($vkPrice * $local_category_im[$category_id_after_mappping]) / 100);
                    }

                    $attributes[] = [
                        'api_id'                => $api_id,
                        'api_product_id'        => $item['SKU'] ?? null,
                        'item_number'           => $item['SKU'] ?? '',
                        'name'                  => $title ?? '',
                        'brand'                 => $brand_id,
                        'ean'                   => $item['EAN'],
                        'ek_price'              => $item['B2B price'],
                        'vk_price'              => $vkPrice,
                        'uvp'                   => $product_uvp,
                        'description'           => $item['HTML_description'] ?? '',
                        'image'                 => json_encode($images),
                        'is_image_process'      => $is_image_process,
                        'stock'                 => $item['Stock'],
                        'supplier_id'           => 0,
                        'delivery_company_id'   => $deliveryCompanyId,
                        'category_id'           => $category_id_after_mappping,
                        'api_category_id'       => $item['Category_id'] ?? '',
                        'status'                => 1,
                        'item_weight'           => $item['Weight'] ?? '',
                        'item_size'             => $item['Size'] ?? '',
                        'item_color'            => $item['Color'] ?? '',
                        'gender'                => $item['Gender'] ?? '',
                        'materials'             => $properties,
                        'vat'                   => \App\Enums\Marketplace\ApiResources::VIDAXL_VAT,
                        'tags'                  => '',
                        'note'                  => '',
                        'production_year'       => '',
                        'delivery_days'         => $item['estimated_total_delivery_time'] ?? 3,
                        'collection_id'         => 0,
                        'shipping_method'       => \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING,
                        'shipping_cost'         => $shippingCost,
                        'real_shipping_cost'    => 0.00,
                        'internel_stock'        => 0,
                        'created_at'            => \Carbon\Carbon::now(),
                        'misc'                  => json_encode([]),
                        'im_handel'             => $im_handel ?? 0,
                        'country_id'            => $country_id,
                    ];

                    info("inserted in array");
                }
            }

            if (!empty($attributes)) {
                Product::insert($attributes);
                $attributes = [];
                dd('inserted');
            }
        }
    }

    public function brandInsertAndUpdateForTeam(array $api_brands): array
    {
        if (!blank($api_brands)) {
            $local_brands = DB::connection('drm_team')->table('marketplace_product_brand')
                ->pluck('brand_name')
                ->toArray();

            $api_brand_arr   = array_map('strtoupper', $api_brands);
            $local_brand_arr = array_map('strtoupper', $local_brands);

            $brands = array_diff($api_brand_arr, $local_brand_arr);

            if (count($brands) > 0) {
                $new_brand = [];
                foreach ($brands as $key => $brand) {
                    if (!blank($brand)) {
                        $new_brand[] = [
                            'brand_name' => $brand,
                            'user_id'    => 2455,
                            'brand_logo' => null,
                        ];
                    }
                }
                DB::connection('drm_team')->table('marketplace_product_brand')->insert($new_brand);
            }
        }

        $local_all_brands =  DB::connection('drm_team')->table('marketplace_product_brand')->pluck('id', 'brand_name')->toArray();
        return array_change_key_case($local_all_brands, CASE_UPPER);
    }
}
