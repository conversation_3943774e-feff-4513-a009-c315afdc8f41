<?php

namespace App\Http\Controllers\BigBuy;

use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\Marketplace\ProductService;
use App\Services\Marketplace\BigBuyApi\InsertProduct;
use App\Services\Marketplace\BigBuyApi\BigBuyApiService;
use App\Services\Marketplace\BigBuyApi\PriceSyncProcess;
use App\Services\Marketplace\BigBuyApi\StockSyncProcess;

class CountryWiseProductInsert extends Controller
{

    private $bigBuyService;
    private $productService;
    public function __construct(BigBuyApiService $bigBuyService, ProductService $productService)
    {
        $this->bigBuyService = $bigBuyService;
        $this->productService = $productService;
    }

    public function productInsertDE()
    {
        info('Bigbuy DE product inserting.........');
        $product_info         = $this->getApiProductInformation('de');
        $api_product_costinfo = $this->getApiShippingCost('de');
        $this->productInsert(1, $product_info, $api_product_costinfo);
        dd("BigBuy DE product inserted.......");
    }

    public function productInsertES()
    {
        info('Bigbuy ES product inserting.........');
        $product_info         = $this->getApiProductInformation('es');
        $api_product_costinfo = $this->getApiShippingCost('es');
        $this->productInsert(8, $product_info, $api_product_costinfo);
        dd("BigBuy ES product inserted.......");
    }

    public function productInsertCH()
    {
        info('Bigbuy CH product inserting.........');
        $product_info         = $this->getApiProductInformation('de');
        $api_product_costinfo = $this->getApiShippingCost('ch');
        $this->productInsert(83, $product_info, $api_product_costinfo);
        dd("BigBuy ES product inserted.......");
    }

    public function productInsertAT()
    {
        info('Bigbuy AT product inserting.........');

        $product_info         = $this->getApiProductInformation('de');
        $api_product_costinfo = $this->getApiShippingCost('at');
        $this->productInsert(74, $product_info, $api_product_costinfo);
        dd("BigBuy ES product inserted.......");
    }

    public function variantProductInsertES()
    {
        info('Bigbuy ES variant product inserting.........');
        $product_info         = $this->getApiProductInformation('es');
        $api_product_costinfo = $this->getApiShippingCost('es');
        $this->productInsert(8, $product_info, $api_product_costinfo, 1);
        dd("BigBuy ES variant product inserted.......");
    }

    /**
     * @param required $country_id
     * @param required $country_short_code
     */
    private function productInsert($country_id, $product_info, $api_product_costinfo, $is_variant = 0)
    {
        $product_brand        = $this->getApiManufacture();
        $product_stock_array  = $is_variant == 1 ? $this->getVariantProductStock() : $this->getParentProductStock();
        $images_arr           = $this->getApiImage();
        $api_product_category = $this->getApiCategory();
        $api_products         = $is_variant == 1 ? $this->getVariantProducts() : $this->getParentProducts();
        $local_category_im    = $this->productService->getCategoryIdWithIMHandel();
        $mapping_category     = $this->getMappingCategory();

        if (
            count($api_products) > 0
            && count($product_stock_array) > 0
            && count($images_arr) > 0
            && count($product_info) > 0
            && count($api_product_costinfo) > 0
            && count($api_product_category) > 0
            && count($product_brand) > 0
        ) {
            app(InsertProduct::class)->insert(
                $api_products,
                $product_stock_array,
                $images_arr,
                $product_info,
                $api_product_costinfo,
                $api_product_category,
                $product_brand,
                $local_category_im,
                $mapping_category,
                $country_id,
                $is_variant
            );
        } else {
            dd("api data not found");
        }

        return true;
    }

    /**
     * Get Bigbuy parent product stock Information for only parent product
     *
     * @return array
     */
    public function getParentProducts()
    {
        info("Bigbuy product loading..............");
        $parent_products = $this->bigBuyService->fetchData('rest/catalog/products');
        if (blank($parent_products)) dd('Bigbuy Product Not Found...................');

        return $parent_products;
    }

    /**
     * Get Bigbuy variant product Information for only variant product
     *
     * @return array
     */
    public function getVariantProducts()
    {
        info("Bigbuy product loading..............");
        $variant_products = $this->bigBuyService->fetchData('rest/catalog/productsvariations');
        if (blank($variant_products)) dd('Bigbuy Product Not Found...................');

        return $variant_products;
    }

    /**
     * Get Bigbuy parent product stock Information for only parent product
     *
     * @return array
     */
    private function getParentProductStock($is_variant = 0)
    {
        info("Bigbuy stock loading..............");
        $stock_url = $is_variant == 1 ? 'rest/catalog/productsvariationsstockbyhandlingdays' : 'rest/catalog/productsstockbyhandlingdays';
        $parent_product_stocks = $this->bigBuyService->fetchData($stock_url);

        if (blank($parent_product_stocks)) dd('Bigbuy parent Product Stock Not Found...................');

        $new_stock_arr = [];
        foreach ($parent_product_stocks as $parent_product_stock) {

            $maxStock = collect($parent_product_stock['stocks'])->sortByDesc('quantity')->first();
            $new_stock_arr[$parent_product_stock['id']] = [
                "maxHandlingDays" => $maxStock['maxHandlingDays'],
                "quantity"        => $maxStock['quantity'],
            ];
        }

        $parent_product_stocks = [];
        $maxStock = null;

        return $new_stock_arr ?? [];
    }

    /**
     * Get Bigbuy variant product stock Information for only variant product
     *
     * @return array
     */
    private function getVariantProductStock()
    {
        info("Bigbuy variant stock loading..............");
        $stock_url = 'rest/catalog/productsvariationsstockbyhandlingdays';
        $parent_product_stocks = $this->bigBuyService->fetchData($stock_url);

        if (blank($parent_product_stocks)) dd('Bigbuy Variant Product Stock Not Found...................');

        $new_stock_arr = [];
        foreach ($parent_product_stocks as $parent_product_stock) {

            $maxStock = collect($parent_product_stock['stocks'])->sortByDesc('quantity')->first();
            $new_stock_arr[$parent_product_stock['id']] = [
                "maxHandlingDays" => $maxStock['maxHandlingDays'],
                "quantity"        => $maxStock['quantity'],
            ];
        }

        $parent_product_stocks = [];
        $maxStock = null;

        return $new_stock_arr ?? [];
    }

    /**
     * get api product manufacture for parent and variant product
     */
    private function getApiImage()
    {
        info("Bigbuy image loading..............");

        $image_url = 'rest/catalog/productsimages';
        $product_image = $this->bigBuyService->fetchData($image_url);
        if (blank($product_image)) dd('Bigbuy Product Image Not Found...................');
        $images_arr = [];
        foreach ($product_image as $image) {
            $images_arr[$image['id']] = $image['images'];
        }
        $product_image = [];
        return $images_arr;
    }

    /**
     * API product manufacture FOR parent and variant product
     */
    private function getApiManufacture()
    {
        info("Bigbuy menufacture loading..............");
        $brand_url = 'rest/catalog/manufacturers';
        $product_brands = $this->bigBuyService->fetchData($brand_url);

        if (blank($product_brands)) dd('Bigbuy manufacture Not Found...................');
        return $product_brands ?? [];
    }

    /**
     * get api product Categories for parent and variant
     */
    private function getApiCategory()
    {
        info("Bigbuy category loading..............");
        $category_url = 'rest/catalog/productscategories';
        $all_product_category = $this->bigBuyService->fetchData($category_url);
        if (blank($all_product_category)) dd('Bigbuy Product Category Not Found...................');
        $api_product_category = [];
        foreach ($all_product_category as $product) {
            $api_product_category[$product['product']] = $product['category'];
        }
        $all_product_category = [];
        return $api_product_category;
    }

    /**
     * get api product Attributes
     */
    private function getApiAttributes($country_short_code)
    {
        $all_product_attributes = $this->bigBuyService->fetchData('rest/catalog/attributes', 'GET', $country_short_code);
        if (blank($all_product_attributes)) dd('Bigbuy Product Attributes Not Found...................');
        $api_product_atributes = [];
        foreach ($all_product_attributes as $attribute) {
            $api_product_atributes[$attribute['id']] = [
                "attributeGroup" => $attribute['attributeGroup'],
                'name'            => $attribute['name'],
            ];
        }
        $all_product_attributes = [];
        return $api_product_atributes;
    }


    /**
     * Country wise api shipping cost for parent and variant
     * @param string $$country_short_code
     */
    private function getApiShippingCost($country_short_code)
    {
        info("Bigbuy shipping loading..............");
        $shipping_url = 'rest/shipping/lowest-shipping-costs-by-country/' . $country_short_code;
        $product_shippingcosts = $this->bigBuyService->fetchData($shipping_url);
        if (blank($product_shippingcosts)) dd('Bigbuy Product Shipping Cost Not Found...................');
        $api_product_costinfo = [];
        foreach ($product_shippingcosts as $shippingcost) {
            $api_product_costinfo[$shippingcost['reference']] = $shippingcost;
        }
        $product_shippingcosts = [];
        return $api_product_costinfo;
    }

    /**
     * Country wise api product informations for parent and variant
     * @param string $$country_short_code
     */
    private function getApiProductInformation($country_short_code)
    {
        info("Bigbuy productinfo loading..............");
        $product_info_url = 'rest/catalog/productsinformation';
        $api_productsinformation = $this->bigBuyService->fetchData($product_info_url, 'GET', $country_short_code);

        if (blank($api_productsinformation)) dd('Bigbuy Product Information Not Found...................');

        $product_info = [];
        foreach ($api_productsinformation as $product) {
            $product_info[$product['id']] = $product;
        }

        $api_productsinformation = [];
        return $product_info;
    }

    private function getMappingCategory()
    {
        return DB::table('api_category_mapping')
            ->where('api_id', \App\Enums\Marketplace\ApiResources::BIGBUY_API_ID)
            ->where('is_complete', 1)
            ->pluck('mp_category_id', 'api_category_id')
            ->toArray();
    }

    /**
     * For all country products stock sync
     */
    public function stockSync()
    {
        info('Bigbuy country wise Stock sync.............');
        $api_stocks =  $this->getParentProductStock();
        count($api_stocks) > 0 ? app(StockSyncProcess::class)->process($api_stocks) : dd("stock not found");
        info('Bigbuy country wise Stock updated.............');
        return true;
    }

    /**
     * For all country products price sync
     */
    public function priceSync()
    {
        info('Bigbuy country wise Price sync.............');
        $api_products         = $this->getParentProducts();
        count($api_products) > 0 ? app(PriceSyncProcess::class)->process($api_products) : dd("price not found");

        return response()->json(['Price Update']);
    }
}
