<?php
namespace App\Enums;
abstract class Product
{
    const TEMPLATE_FIELDS = [
        'title',
        'item_number',
        'ean',
        'description',
        'image',
        'ek_price',
        'stock',
        'category',
        'gender',
        'item_weight',
        'item_color',
        'production_year',
        'materials',
        'brand',
        'item_size',
        'marketplace_api_response'
    ];

    const ALL_FIELDS = [
        'title',
        'item_number',
        'ean',
        'description',
        'images',
        'ek_price',
        'vk_price',
        'uvp',
        'stock',
        'gender',
        'item_weight',
        'item_color',
        'production_year',
        'materials',
        'brand',
        'item_size',
        'tags',
        'delivery_days'
    ];

    const ALL_FIELDS_NAME = [
        'title' => 'Product Title',
        'item_number' => 'Item Number',
        'ean' => 'Product EAN',
        'description' => 'Product Description',
        'images' => 'Product Image',
        'ek_price' => 'EK Price',
        'vk_price' => 'VK Price',
        'uvp' => 'UVP',
        'stock' => 'Stock',
        'gender' => 'Gender',
        'item_weight' => 'Product Weight',
        'item_color' => 'Product Color',
        'production_year' => 'Production Year',
        'materials' => 'Product Materials',
        'brand' => 'Product Brand',
        'item_size' => 'Product Size',
        'tags' => 'Product Tags',
        'delivery_days' => 'Handling Time'
    ];

    const EXTRA_FIELD = [
        'movement_type',
        'caliber',
        'power_reserve',
        'caliber_additional_information',
        'case_material',
        'case_diameter',
        'case_thickness',
        'crystal',
        'case_additional_information',
        'bracelet_color',
        'bracelet_material',
        'bracelet_length',
        'bracelet_width',
        'clasp',
        'bracelet_additional_information',
        'functions',
        'orignal_papers',
        'original_box',
        'year',
        'water_resistance'
    ];

    const EXTRA_FIELD_NAME = [
        'movement_type' => 'Movement Type',
        'caliber' => 'Caliber',
        'power_reserve' => 'Power Reserve',
        'caliber_additional_information' => 'Caliber Additional Information',
        'case_material' => 'Case Material',
        'case_diameter' => 'Case Diameter',
        'case_thickness' => 'Case Thickness',
        'crystal' => 'Crystal',
        'case_additional_information' => 'Case Additional Information',
        'bracelet_color' => 'Bracelet Color',
        'bracelet_material' => 'Bracelet Material',
        'bracelet_length' => 'Bracelet Length',
        'bracelet_width' => 'Bracelet Width',
        'clasp' => 'Clasp',
        'bracelet_additional_information' => 'Bracelet Additional Information',
        'functions' => 'functions',
        'orignal_papers' => 'Origial Papers',
        'original_box' => 'Original Box',
        'year' => 'Year',
        'water_resistance' => 'Water Resistance'
    ];

    const EXPORT_FIELDS = [
        'title' => "Title",
        'item_number' => "Item Number",
        'ean' => "EAN",
        'description' => "Description",
        'image' => "Image",
        'ek_price' => "EK Price",
        'uvp' => "UVP",
        'stock' => "Stock",
        'item_weight' => "Item Weight",
        'item_size' => "Item Size",
        'item_color' => "Item Color",
        'note' => "Note",
        'production_year' => "Production Year",
        'brand' => "Brand",
        'materials' => "Materials",
        'tags' => "Tags",
        'gender' => "Gender",
        'status' => "Status",
        'delivery_days' => "Handling Time",
        'category' => "Category",
    ];

    const DT_EXPORT_FIELDS = [
        'title' => "Title",
        'item_number' => "Item Number",
        'ean' => "EAN",
        'description' => "Description",
        'images' => "Image",
        'ek_price' => "EK Price",
        'vk_price' => "VK Price",
        'uvp' => "UVP",
        'stock' => "Stock",
        'item_weight' => "Item Weight",
        'item_size' => "Item Size",
        'item_color' => "Item Color",
        'note' => "Note",
        'production_year' => "Production Year",
        'brand' => "Brand",
        'materials' => "Materials",
        'tags' => "Tags",
        'gender' => "Gender",
        'status' => "Status",
        'category' => "Category",
        'delivery_days' => "Handling Time",
        'product_url' => "Product URL"
    ];

    const STATUS = [
        1000 => 'New',
        1500 => 'Neu: Sonstige (siehe Artikelbeschreibung)',
        1750 => 'Neu mit Fehlern',
        2000 => 'Vom Hersteller generalüberholt',
        2500 => 'Vom Verkäufer generalüberholt',
        2750 => 'Neuwertig',
        3000 => 'Gebraucht',
        4000 => 'Sehr gut',
        5000 => 'Gut',
        6000 => 'Akzeptabel',
        7000 => 'Als Ersatzteil / defekt'
    ];

}
