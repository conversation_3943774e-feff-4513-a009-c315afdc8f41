<?php


namespace App\Enums;


abstract class Channel
{
    const GAMBIO = 1;
    const LENGOW = 2;
    const YATEGO = 3;
    const EBAY = 4;
    const AMAZON = 5;
    const ETSY = 11;
    const SHOPIFY = 6;
    const WOOCOMMERCE = 7;
    const CLOUSALE = 8;
    const CHRONO24 = 9;
    const DROPTIENDA = 10;
    const OTTO = 12;
    const KAUFLAND = 13;

    const ALL = [
        self::GAMBIO,
        self::LENGOW,
        self::YATEGO,
        self::EBAY,
        self::AMAZON,
        self::ETSY,
        self::SHOPIFY,
        self::WOOCOMMERCE,
        self::CLOUSALE,
        self::CHRONO24,
        self::DROPTIENDA,
        self::OTTO,
    ];

    const MAPPING_CHANNELS = [
        self::ETSY,
        self::EBAY,
        self::AMAZON,
        self::YATEGO,
        self::OTTO,
        self::KAUFLAND
    ];

    const CSV_CHANNELS = [
        self::LENGOW,
        self::YATEGO,
        self::CLOUSALE,
        self::CHRONO24
    ];

    const CATEGORY_LIMITS = [
        self::ETSY =>   1,
        self::EBAY =>   1,
        self::AMAZON => 1,
        self::YATEGO => 3,
        self::OTTO => 1,
        self::KAUFLAND =>1
    ];

}
