<?php

namespace App\Enums\Marketplace;

class ApiTestResources {

    // API base address
    // const B_ADDRESS = 'https://portal.internet-bikes.com/api/twm/';
    const B_ADDRESS = 'https://test-portal.internet-bikes.com/api/twm/';

    // const BDroppy = 'https://prod.bdroppy.com/api/';
    const BDroppy = "https://sandbox.bdroppy.com/api/";

    const ADDRESSES = [

        /*
            START ::
            Bike API addresses
        */
        'AUTH'               => SELF::B_ADDRESS."auth/authenticate",
        'LIST_ALL_ORDERS'    => SELF::B_ADDRESS."orders",
        'FETCH_ORDER_BY_ID'  => SELF::B_ADDRESS."orders/", //parameter = order_id(int)
        "FETCH_ORDERS_BY_LAST_N_DAYS" => SELF::B_ADDRESS."orders/latest/", //parameter = days(int)

        'SUBMIT_AN_ORDER'    => SELF::B_ADDRESS."orders",

        "LIST_ALL_PRODUCTS"  => SELF::B_ADDRESS."products",
        "FETCH_PRODUCT_BY_ID"=> SELF::B_ADDRESS."products/", //parameter = product_id(int)
        "LIST_ALL_SEGMENT"   => SELF::B_ADDRESS."segments",
        "LIST_PRODUCTS_PER_SEGMENT" => SELF::B_ADDRESS."segment/", //parameter = segment_id(int)

        "V1_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => SELF::B_ADDRESS."segment/1/changes/1",
        "V2_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => SELF::B_ADDRESS."v2/segment/1/changes/1",

        "FETCH_STOCK_CHANGES_IN_LAST_MINUTES" => SELF::B_ADDRESS."products/changes/", //parameter = product_id(int)
        "FETCH_STOCK_PER_PRODUCT"             => SELF::B_ADDRESS."stock/", //parameter = product_id(int)
        "LIST_ALL_TRACKING_CODES_FOR_USER"    => SELF::B_ADDRESS."trackingcodes",
        "FETCH_TRACKING_CODE_BY_ID"           => SELF::B_ADDRESS."trackingcodes/", //parameter = ordere_id(int)
        /*
            END ::
            Bike API addresses
        */






        /*
            END ::
            Bike API addresses
        */
    ];

    const BDROPPY_APIS = [
        'PRODUCTS'            => SELF::BDroppy."product/export/json?",
        'CATEGORIES'          => SELF::BDroppy.'category',
        'BRANDS'              => SELF::BDroppy.'brand',
        'RESERVE_STOCK'       => SELF::BDroppy.'order/reserve', // POST Request
        'RESERVATION_STATUS'  => SELF::BDroppy.'order/reserve', // GET Request
        'CREATE_ORDER'        => SELF::BDroppy.'order', // POST Request
        'GET_ORDER_BY_ID'     => SELF::BDroppy.'order/{id}', // GET Request
        'GET_ORDER_BY_REF'    => SELF::BDroppy.'order/ref_id/{id}', // GET Request
        'USER_CATELOG_LIST'   => SELF::BDroppy.'user_catalog/list', // GET Request
    ];

    // Bike API
    const BIKE_API_ID       = 1; // Don't change
    const BIKE_API_DEFAULT_SHIPPING_COST   = 5.20;
    const BIKE_API_DELIVERY_COMPANY_ID     = 1187;
    const BIKE_API_DEFAULT_DELIVERY_DAYS   = 3;
    const BIKE_API_SUPPLIER_ID             = 0;


    // B@BUHREN API
    const B2BUHREN_API_ID   = 2; // DOn't change
    const B2BUHREN_API_DEFAULT_SHIPPING_COST   = 5.20;
    const B2BUHREN_API_DELIVERY_COMPANY_ID     = 1188;
    const B2BUHREN_API_DEFAULT_DELIVERY_DAYS   = 3;
    const B2BUHREN_API_SUPPLIER_ID             = 0;

    // BDroppy API
    const BDROPPY_API_ID    = 3; // Don't change
    const BDROPPY_API_DELIVERY_COMPANY_ID     = 1189;
    const BDROPPY_API_DEFAULT_SHIPPING_COST   = 5.20;
    const BDROPPY_API_DEFAULT_DELIVERY_DAYS   = 3;
    const BDROPPY_API_PER_PAGE_PRODUCT        = 30;

}
