<?php

namespace App\Enums\Marketplace;

class ApiResources
{

    // API base address
    // const B_ADDRESS = 'https://portal.internet-bikes.com/api/twm/';
    const B_ADDRESS = 'https://portal.internet-bikes.com/api/twm/';

    const BDroppy = 'https://prod.bdroppy.com/api/';
    // const BDroppy = "https://sandbox.bdroppy.com/api/";

    // BigBuy api
    // production
    const BigBuy = 'https://api.bigbuy.eu/';
    // developmnet
    // const BigBuy = 'https://api.sandbox.bigbuy.eu/';

    const VidaXLCsv = 'http://transport.productsup.io/8227eda6a7fa10d0d996/channel/188057/vidaXL_de_dropshipping.csv';
    const VidaXLChCsv = 'http://transport.productsup.io/e9ba40e8e3597b1588a0/channel/188052/vidaXL_ch_fr_dropshipping.csv';


    const BTSWholesalarBaseURLPro = 'https://api.btswholesaler.com'; // for production
    const BTSWholesalarBaseURLDev = 'https://apidev.btswholesaler.com'; // for dev

    const FloralogisticsBaseURL = [
        'PRODUCTION'  => "https://api.floralogistics.nl/v1/channels",
        'DEVELOPMENT' => "https://api.test.floralogistics.nl/v1/channels"
    ];

    const ADDRESSES = [

        /*
            START ::
            Bike API addresses
        */
        'AUTH'               => SELF::B_ADDRESS . "auth/authenticate",
        'LIST_ALL_ORDERS'    => SELF::B_ADDRESS . "orders",
        'FETCH_ORDER_BY_ID'  => SELF::B_ADDRESS . "orders/", //parameter = order_id(int)
        "FETCH_ORDERS_BY_LAST_N_DAYS" => SELF::B_ADDRESS . "orders/latest/", //parameter = days(int)

        'SUBMIT_AN_ORDER'    => SELF::B_ADDRESS . "orders",

        "LIST_ALL_PRODUCTS"  => SELF::B_ADDRESS . "products",
        "FETCH_PRODUCT_BY_ID" => SELF::B_ADDRESS . "products/", //parameter = product_id(int)
        "LIST_ALL_SEGMENT"   => SELF::B_ADDRESS . "segments",
        "LIST_PRODUCTS_PER_SEGMENT" => SELF::B_ADDRESS . "segment/", //parameter = segment_id(int)

        "V1_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => SELF::B_ADDRESS . "segment/1/changes/1",
        "V2_FETCH_CHANGED_PRODUCTS_FOR_LAST_N_DAYS" => SELF::B_ADDRESS . "v2/segment/",

        "FETCH_STOCK_CHANGES_IN_LAST_MINUTES" => SELF::B_ADDRESS . "products/changes/", //parameter = product_id(int)
        "FETCH_STOCK_PER_PRODUCT"             => SELF::B_ADDRESS . "stock/", //parameter = product_id(int)
        "LIST_ALL_TRACKING_CODES_FOR_USER"    => SELF::B_ADDRESS . "trackingcodes",
        "FETCH_TRACKING_CODE_BY_ID"           => SELF::B_ADDRESS . "trackingcodes/", //parameter = ordere_id(int)
        /*
            END ::
            Bike API addresses
        */






        /*
            END ::
            Bike API addresses
        */
    ];

    const BDROPPY_APIS = [
        'PRODUCTS'            => SELF::BDroppy . "product/export/json?",
        'CATEGORIES'          => SELF::BDroppy . 'category',
        'BRANDS'              => SELF::BDroppy . 'brand',
        'RESERVE_STOCK'       => SELF::BDroppy . 'order/reserve', // POST Request
        'RESERVATION_STATUS'  => SELF::BDroppy . 'order/reserve', // GET Request
        'CREATE_ORDER'        => SELF::BDroppy . 'order', // POST Request
        'GET_ORDER_BY_ID'     => SELF::BDroppy . 'order/{id}', // GET Request
        'GET_ORDER_BY_REF'    => SELF::BDroppy . 'order/ref_id/{id}', // GET Request
        'USER_CATELOG_LIST'   => SELF::BDroppy . 'user_catalog/list', // GET Request
    ];

    // Bike API
    const BIKE_API_ID       = 1; // Don't change
    const BIKE_API_DEFAULT_SHIPPING_COST   = 7.99;
    const BIKE_API_DELIVERY_COMPANY_ID     = 13156;
    const BIKE_API_DEFAULT_DELIVERY_DAYS   = 3;
    const BIKE_API_SUPPLIER_ID             = 0;
    const BIKE_API_STOCK_CSV_URL           = 'https://www.twm-bv.com/api-v1/stock';


    // B@BUHREN API
    const B2BUHREN_API_ID   = 2; // DOn't change
    const B2BUHREN_API_DEFAULT_SHIPPING_COST   = 14.50;
    const B2BUHREN_API_DELIVERY_COMPANY_ID     = 13543;
    const B2BUHREN_API_DEFAULT_DELIVERY_DAYS   = 3;
    const B2BUHREN_API_SUPPLIER_ID             = 0;

    // BDroppy API
    const BDROPPY_API_ID    = 3; // Don't change
    const BDROPPY_API_DELIVERY_COMPANY_ID     = 13565;
    const BDROPPY_API_DEFAULT_SHIPPING_COST   = 5.20;
    const BDROPPY_API_DEFAULT_DELIVERY_DAYS   = 3;
    const BDROPPY_API_PER_PAGE_PRODUCT        = 30;

    // BigBuy API
    const BIGBUY_API_ID    = 4; // Don't change
    const BIGBUY_DELIVERY_COMPANY_ID     = 15003; // Live Server
    //  const BIGBUY_DELIVERY_COMPANY_ID     = 9; // Test Server
    const BIGBUY_DEFAULT_SHIPPING_COST   = 5.20;
    const BIGBUY_DEFAULT_DELIVERY_DAYS   = 3;

    // VidaXL API
    const VIDAXL_API_ID    = 5; // Don't change
    //  const VIDAXL_DELIVERY_COMPANY_ID     = 532; // Test Server
    const VIDAXL_DELIVERY_COMPANY_ID     = 15021; // Live Server
    const VIDAXL_SHIPPING_COST   = 5.39;
    const VIDAXL_VAT   = 19;
    const VIDAXL_GET_PRODUCTS_URL   = "api_customer/products";
    const VIDAXL_CREATE_ORDER_URL   = "api_customer/orders";
    const VIDAXL_GET_ORDER_URL      = "api_customer/orders?customer_order_reference_eq=";

    //  Bino Mertens Supplier
    const BINO_MERTENS_XML_URL             = 'http://binohracky.cz/xml-output/feed-uni-de/feed-uni-de-eur.xml';
    const BINO_MERTENS_API_ID              = 6; // Don't change
    const BINO_MERTENS_DELIVERY_COMPANY_ID = 49997; // live server
    const BINO_MERTENS_SHIPPING_COST       = 7.90;
    const BINO_MERTENS_VAT                 = 19;
    const BINO_MERTENS_CATEGORY            = 123;
    const BINO_MERTENS_BRAND               = 873; //'Bino & Mertens';

    //    Plush supplier
    const PLUSH_DELIVERY_COMPANY_ID        = 14398;
    const ORDER_SEND_ADDRESS = [
        '1'     => '\App\Http\Controllers\Marketplace\BikeApiController',
        '2'     => '\App\Http\Controllers\Marketplace\B2bUhrenApiController',
        // '3'     => '\App\Http\Controllers\Marketplace\BDroppyApiController',
        '4'     => '\App\Http\Controllers\Marketplace\BigBuyApiController',
        '5'     => '\App\Http\Controllers\Marketplace\VidaXlApiController',
        '6'     => '\App\Http\Controllers\Marketplace\BinoMertensApiController',
        '14398' => '\App\Http\Controllers\Marketplace\PlushController',
        '7' => '\App\Http\Controllers\Marketplace\BTSWholesalerApiController',
        '8' => '\App\Http\Controllers\Marketplace\FloraLogisticsApiController',
        // '14379' => '\App\Http\Controllers\Marketplace\EladyController',
    ];

    const ALL_API_TRACKING_ADDRESS = [
        '1'  => '\App\Http\Controllers\Marketplace\BikeApiController',
        '4'  => '\App\Http\Controllers\Marketplace\BigBuyApiController',
        '5'  => '\App\Http\Controllers\Marketplace\VidaXlApiController',
        '8'  => '\App\Http\Controllers\Marketplace\FloraLogisticsApiController',
    ];

    const BTSWholesalar = [
        'TOKEN'             => 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsInN1YiI6ODk0OTMyLCJpYXQiOjE2NzE3MDM2OTF9.HBBMS3nTnEYgC4Fiezg72iDnRiK3tDjtMum-qBMkDLE',
        'API_ID'            => 7,
        'GET_PRODUCT'       => SELF::BTSWholesalarBaseURLPro . "/v1/api/getProducts",
        'GET_SHIPPING_ID'   => SELF::BTSWholesalarBaseURLPro . "/v1/api/getShippingPrices",
        'CREATE_ORDER'      => SELF::BTSWholesalarBaseURLPro . "/v1/api/setCreateOrder",
        'CANCEL_ORDER'      => SELF::BTSWholesalarBaseURLPro . "/v1/api/setCancelOrder",
        'GET_ORDER'         => SELF::BTSWholesalarBaseURLPro . "/v1/api/getOrder",
        'GET_TRACKING'      => SELF::BTSWholesalarBaseURLPro . "/v1/api/getTrackings",
        'GET_COUNTRY'      => SELF::BTSWholesalarBaseURLPro . "/v1/api/getCountries",

    ];

    const FloraLogistics = [
        'TOKEN'             => '1hZJmqpvpIWYXu8Qd1InvkyzKjuSzHYCznMZGpyuDoy1MHDrCiiZhIXtaOkMwLBhoNxUH3xGQMg9nPei0z2uNX5FoC8paA6f6Ro9',
        'API_ID'            => 8,
        'ORDER_URL'         => SELF::FloralogisticsBaseURL['PRODUCTION']."/orders/",
        'TRACKING_URL'      => SELF::FloralogisticsBaseURL['PRODUCTION']."/shipments/?filter[order__reference]=",
        'SHIPPING_COST'     => 5.95,
        'DELIVERY_COMPANY_ID'=> 50101,
    ];

    const VIDAXL_CSVS = [
        'DE' => "http://transport.productsup.io/8227eda6a7fa10d0d996/channel/188057/vidaXL_de_dropshipping.csv",
        'CH' => "http://transport.productsup.io/e9ba40e8e3597b1588a0/channel/188050/vidaXL_ch_de_dropshipping.csv",
        'ES' => "http://transport.productsup.io/7947c7becca20572b9b6/channel/188064/vidaXL_es_dropshipping.csv",
        'AT' => "http://transport.productsup.io/9e92912654ad834d7822/channel/188085/vidaXL_at_dropshipping.csv",
    ];

    const Elady = [
        'DELIVERY_COMPANY_ID'=> 14379,
    ];
}
